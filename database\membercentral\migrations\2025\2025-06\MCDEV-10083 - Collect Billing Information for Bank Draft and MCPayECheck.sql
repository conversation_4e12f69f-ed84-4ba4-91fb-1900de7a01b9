USE membercentral
GO

ALTER TABLE dbo.tr_bankAccounts ADD firstName varchar(75) NOT NULL CONSTRAINT [DF_tr_bankAccounts_firstName] DEFAULT ('');
ALTER TABLE dbo.tr_bankAccounts ADD lastName varchar(75) NOT NULL CONSTRAINT [DF_tr_bankAccounts_lastName] DEFAULT ('');
ALTER TABLE dbo.tr_bankAccounts ADD address1 varchar(100) NOT NULL CONSTRAINT [DF_tr_bankAccounts_address1] DEFAULT ('');
ALTER TABLE dbo.tr_bankAccounts ADD city varchar(75) NOT NULL CONSTRAINT [DF_tr_bankAccounts_city] DEFAULT ('');
ALTER TABLE dbo.tr_bankAccounts ADD stateCode varchar(4) NOT NULL CONSTRAINT [DF_tr_bankAccounts_stateCode] DEFAULT ('');
ALTER TABLE dbo.tr_bankAccounts ADD postalCode varchar(25) NOT NULL CONSTRAINT [DF_tr_bankAccounts_postalCode] DEFAULT ('');
GO

DECLARE @tmpFields TABLE (fieldID int);

INSERT INTO @tmpFields (fieldID)
SELECT fieldID
FROM dbo.mp_fields
WHERE fieldName IN ('First Name','Last Name','Billing Postal Code','Billing Address','Billing City','Billing State/Province');

-- BankDraft
INSERT INTO dbo.mp_gatewayFields (gatewayID, fieldID, isRequired)
SELECT 16, fieldID, 1
FROM @tmpFields;

-- MCPayECheck
INSERT INTO dbo.mp_gatewayFields (gatewayID, fieldID, isRequired)
SELECT 19, fieldID, 1
FROM @tmpFields;
GO