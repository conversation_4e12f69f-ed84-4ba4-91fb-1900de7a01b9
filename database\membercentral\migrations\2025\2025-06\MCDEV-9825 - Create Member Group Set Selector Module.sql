USE membercentral
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @adminViewRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

	CREATE TABLE #ajaxComponentMethods (
		autoid int IDENTITY(1,1),
		methodName varchar(500),
		resourceTypeFunctionID int,
		methodID int
	);

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES
		('getGroupSetsJSON', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='GROUPSETWIDGET',
		@requestCFC='model.admin.common.modules.groupSetSelector.groupSetSelector',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

DECLARE @componentID int, @RTFID int;

IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
  	DROP TABLE #ajaxComponentMethods;
CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

SELECT @RTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
  VALUES 
  	('getPreviewMemberGroupSetData', @RTFID);

EXEC dbo.ajax_addComponentMethodRightsBulk
  	@componentName='ADMREPORTS',
  	@requestCFC='model.admin.reports.report',
  	@componentID=@componentID OUTPUT;

IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
	DROP TABLE #ajaxComponentMethods;

-- Register new MEMBERGROUPSETS methods for enhanced form validation
CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

DECLARE @componentID INT, @adminViewRTFID int;
SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
VALUES
	('checkGroupSetName', @adminViewRTFID),
	('saveMemberGroupSet', @adminViewRTFID);

EXEC dbo.ajax_addComponentMethodRightsBulk
	@componentName='MEMBERGROUPSETS',
	@requestCFC='model.admin.memberGroupSets.memberGroupSetsAdmin',
	@componentID=@componentID OUTPUT;

IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
  	DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
  	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
  	SELECT ERROR_MESSAGE();
END CATCH
GO

-- Create custom field template for storing multiple group set selections in member profiles
-- This creates a reusable template that can be applied to specific organizations as needed
-- The field will store comma-separated group set IDs for members

PRINT 'Creating custom field template for Member Group Set selections...';

-- Note: This template should be customized per organization
-- Example usage for specific organization:
/*
DECLARE @orgID INT, @columnID INT;
SELECT @orgID = orgID FROM dbo.organizations WHERE orgCode = 'YOUR_ORG_CODE';

EXEC dbo.ams_createMemberDataColumn
    @orgID = @orgID,
    @columnName = 'Selected Group Sets',
    @columnDesc = 'Multiple Group Set selections for member profile organization and reporting',
    @skipImport = 0,
    @dataTypeCode = 'STRING',
    @displayTypeCode = 'TEXTBOX',
    @allowNull = 1,
    @allowNewValuesOnImport = 0,
    @isReadOnly = 0,
    @allowMultiple = 0,
    @maxChars = 500,
    @columnID = @columnID OUTPUT;

PRINT 'Created Selected Group Sets custom field with columnID: ' + CAST(@columnID AS VARCHAR(10));
*/

PRINT 'Custom field template ready for organization-specific implementation.';
GO

CREATE PROC dbo.ams_getMemberDataByGroupSets
@orgID int,
@groupSetIDList varchar(max),
@membersTableName varchar(60),
@membersResultTableName varchar(60),
@mode varchar(10) = 'view',
@outputGroupsXML xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..'+@membersTableName) IS NULL
		RAISERROR('Designated source table does not exist.',16,1);
	IF OBJECT_ID('tempdb..'+@membersResultTableName) IS NULL
		RAISERROR('Designated result table does not exist.',16,1);

	-- Parse group set IDs
	CREATE TABLE #tblGroupSets (groupSetID int PRIMARY KEY, groupSetName varchar(200), groupSetOrder int);
	CREATE TABLE #tblGroups (groupID int PRIMARY KEY, groupSetID int, groupName varchar(115), labelOverride varchar(100), groupOrder int);
	CREATE TABLE #tblOutputGroups (groupSetID int, groupSetName varchar(200), groupSetOrder int, groupID int, groupName varchar(115), labelOverride varchar(100), groupOrder int);

	DECLARE @groupSetID int, @groupSetName varchar(200), @groupSetOrder int = 1;

	-- Parse the comma-separated list of group set IDs
	DECLARE @xml xml = '<root><item>' + REPLACE(@groupSetIDList, ',', '</item><item>') + '</item></root>';

	INSERT INTO #tblGroupSets (groupSetID, groupSetName, groupSetOrder)
	SELECT
		CAST(x.value('.', 'int') AS int) as groupSetID,
		mgs.groupSetName,
		ROW_NUMBER() OVER (ORDER BY CAST(x.value('.', 'int') AS int)) as groupSetOrder
	FROM @xml.nodes('/root/item') AS t(x)
	INNER JOIN dbo.ams_memberGroupSets mgs ON mgs.groupSetID = CAST(x.value('.', 'int') AS int)
	WHERE mgs.orgID = @orgID
	AND CAST(x.value('.', 'int') AS int) > 0;

	-- Get all groups for the specified group sets
	INSERT INTO #tblGroups (groupID, groupSetID, groupName, labelOverride, groupOrder)
	SELECT
		mgsg.groupID,
		mgsg.groupSetID,
		g.groupName,
		ISNULL(NULLIF(mgsg.labelOverride, ''), g.groupName) as labelOverride,
		ISNULL(mgsg.groupOrder, 0) as groupOrder
	FROM #tblGroupSets gs
	INNER JOIN dbo.ams_memberGroupSetGroups mgsg ON mgsg.groupSetID = gs.groupSetID
	INNER JOIN dbo.ams_groups g ON g.groupID = mgsg.groupID
	WHERE g.orgID = @orgID
	AND g.status <> 'D'
	ORDER BY gs.groupSetOrder, mgsg.groupOrder, g.groupName;

	-- Build output groups data
	INSERT INTO #tblOutputGroups (groupSetID, groupSetName, groupSetOrder, groupID, groupName, labelOverride, groupOrder)
	SELECT
		gs.groupSetID,
		gs.groupSetName,
		gs.groupSetOrder,
		tg.groupID,
		tg.groupName,
		tg.labelOverride,
		tg.groupOrder
	FROM #tblGroupSets gs
	INNER JOIN #tblGroups tg ON tg.groupSetID = gs.groupSetID
	ORDER BY gs.groupSetOrder, tg.groupOrder, tg.groupName;

	-- Create XML output for groups
	SET @outputGroupsXML = (
		SELECT
			groupSetID as '@groupSetID',
			groupSetName as '@groupSetName',
			groupSetOrder as '@groupSetOrder',
			(
				SELECT
					groupID as '@groupID',
					groupName as '@groupName',
					labelOverride as '@labelOverride',
					groupOrder as '@groupOrder'
				FROM #tblOutputGroups og2
				WHERE og2.groupSetID = og1.groupSetID
				ORDER BY og2.groupOrder, og2.groupName
				FOR XML PATH('group'), TYPE
			)
		FROM #tblOutputGroups og1
		GROUP BY groupSetID, groupSetName, groupSetOrder
		ORDER BY groupSetOrder
		FOR XML PATH('groupSet'), ROOT('groupSets')
	);

	-- Build dynamic SQL to add group membership columns to result table
	DECLARE @finalSQL nvarchar(max) = '', @selectSQL nvarchar(max) = '', @crlf nvarchar(2) = char(13)+char(10);
	DECLARE @column_list nvarchar(max) = '';

	-- Start with empty SELECT (no memberID needed in result)
	SET @selectSQL = 'SELECT ' + @crlf;
	DECLARE @firstColumn bit = 1;

	-- Add group membership columns for each group in the group sets
	DECLARE group_cursor CURSOR FOR
	SELECT
		'gs' + CAST(groupSetID AS varchar(10)) + '_g' + CAST(groupID AS varchar(10)) as columnName,
		'CASE WHEN EXISTS (SELECT 1 FROM dbo.cache_members_groups cmg WHERE cmg.memberID = m.memberID AND cmg.groupID = ' + CAST(groupID AS varchar(10)) + ') THEN ''Yes'' ELSE ''No'' END' as columnSQL,
		labelOverride as groupLabel
	FROM #tblOutputGroups
	ORDER BY groupSetOrder, groupOrder, groupName;

	DECLARE @columnName varchar(50), @columnSQL varchar(500), @groupLabel varchar(115);

	OPEN group_cursor;
	FETCH NEXT FROM group_cursor INTO @columnName, @columnSQL, @groupLabel;

	WHILE @@FETCH_STATUS = 0
	BEGIN
		IF @firstColumn = 1
		BEGIN
			SET @selectSQL = @selectSQL + @columnSQL + ' as [' + @groupLabel + ']' + @crlf;
			SET @firstColumn = 0;
		END
		ELSE
		BEGIN
			SET @selectSQL = @selectSQL + ', ' + @columnSQL + ' as [' + @groupLabel + ']' + @crlf;
		END

		SET @column_list = @column_list + ', [' + @groupLabel + '] varchar(3) NULL';

		FETCH NEXT FROM group_cursor INTO @columnName, @columnSQL, @groupLabel;
	END;

	CLOSE group_cursor;
	DEALLOCATE group_cursor;

	-- Complete the SELECT statement
	SET @selectSQL = @selectSQL + 'FROM dbo.ams_members m' + @crlf +
		'INNER JOIN ' + @membersTableName + ' mt ON mt.memberID = m.memberID' + @crlf +
		'WHERE m.orgID = @orgID';

	-- Add columns to result table if we have any groups
	IF @column_list <> ''
	BEGIN
		DECLARE @ParmDefinition nvarchar(500) = N'@orgID int', @alterSQL nvarchar(max);
		
		SET @alterSQL = 'ALTER TABLE ' + QUOTENAME(@membersResultTableName) + ' ADD ' + STUFF(@column_list, 1, 2, '');
		EXEC (@alterSQL);

		-- Execute the final SQL to populate results (without column list, relies on column order)
		SET @finalSQL = 'INSERT INTO ' + @membersResultTableName + @crlf + @selectSQL;
		EXEC sp_executesql @finalSQL, @ParmDefinition, @orgID=@orgID;

		-- Drop the holding column (following field set pattern)
		EXEC (N'ALTER TABLE ' + @membersResultTableName + ' DROP COLUMN MGSAutoID;');
	END;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH

GO
