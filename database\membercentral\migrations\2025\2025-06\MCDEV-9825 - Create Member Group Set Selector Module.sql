USE membercentral
GO

CREATE TABLE [dbo].[ams_memberGroupSetUsage](
    [useID] [int] IDENTITY(1,1) NOT NULL,
    [siteResourceID] [int] NOT NULL,
    [groupSetID] [int] NOT NULL,
    [area] [varchar](20) NULL,
    [useSiteResourceID] [int] NULL,
    [groupSetOrder] [int] NOT NULL DEFAULT(1),
    CONSTRAINT [PK_ams_memberGroupSetUsage] PRIMARY KEY CLUSTERED 
    (
        [useID] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

-- Add foreign key constraints
ALTER TABLE [dbo].[ams_memberGroupSetUsage] WITH CHECK ADD CONSTRAINT [FK_ams_memberGroupSetUsage_ams_memberGroupSets] 
    FOREIGN KEY([groupSetID]) REFERENCES [dbo].[ams_memberGroupSets] ([groupSetID])

ALTER TABLE [dbo].[ams_memberGroupSetUsage] CHECK CONSTRAINT [FK_ams_memberGroupSetUsage_ams_memberGroupSets]

ALTER TABLE [dbo].[ams_memberGroupSetUsage] WITH CHECK ADD CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources] 
    FOREIGN KEY([siteResourceID]) REFERENCES [dbo].[cms_siteResources] ([siteResourceID])

ALTER TABLE [dbo].[ams_memberGroupSetUsage] CHECK CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources]

ALTER TABLE [dbo].[ams_memberGroupSetUsage] WITH CHECK ADD CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources_use] 
    FOREIGN KEY([useSiteResourceID]) REFERENCES [dbo].[cms_siteResources] ([siteResourceID])

ALTER TABLE [dbo].[ams_memberGroupSetUsage] CHECK CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources_use]

-- Create indexes for performance
CREATE NONCLUSTERED INDEX [IX_ams_memberGroupSetUsage_siteResourceID_area] ON [dbo].[ams_memberGroupSetUsage]
(
    [siteResourceID] ASC,
    [area] ASC
)
INCLUDE([groupSetID],[groupSetOrder]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

CREATE NONCLUSTERED INDEX [IX_ams_memberGroupSetUsage_groupSetID] ON [dbo].[ams_memberGroupSetUsage]
(
    [groupSetID] ASC
) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE PROC dbo.ams_createMemberGroupSetUsage
@siteResourceID int,
@groupSetID int,
@area varchar(20),
@createSiteResourceID bit,
@useID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @usesiteResourceID int, @siteResourceTypeID int, @siteid int, @maxorder int, @orgID int;

    set @useID = null;
    set @usesiteResourceID = null;

    -- Check if usage already exists
    select @useID = useID 
    from dbo.ams_memberGroupSetUsage 
    where siteResourceID = @siteResourceID 
    and groupSetID = @groupSetID 
    and isnull(area,'') = isnull(@area,'');

    IF @useID is null begin
        BEGIN TRAN;	
            IF @createSiteResourceID = 1 BEGIN
                set @siteResourceTypeID = dbo.fn_getResourceTypeID('memberGroupSet');
                
                -- Get orgID from group set
                select @orgID = gs.orgID
                from dbo.ams_memberGroupSets gs
                where gs.groupSetID = @groupSetID;
                
                -- Get siteID from orgID (use first site for the org)
                select top 1 @siteID = siteID 
                from dbo.sites 
                where orgID = @orgID
                order by siteID;

                exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=1,
                    @siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@usesiteResourceID OUTPUT;
            END

            -- get max order
            select @maxorder = isnull(MAX(groupSetOrder), 0) + 1 
            FROM dbo.ams_memberGroupSetUsage
            WHERE siteResourceID = @siteResourceID
            AND isnull(area,'') = isnull(@area,'');

            INSERT INTO dbo.ams_memberGroupSetUsage (siteResourceID, groupSetID, area, useSiteResourceID, groupSetOrder)
            VALUES (@siteResourceID, @groupSetID, nullif(@area,''), @usesiteResourceID, @maxorder);

            SELECT @useID = SCOPE_IDENTITY();
        COMMIT TRAN;
    END

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_reorderMemberGroupSetUsage
@siteResourceID int,
@area varchar(20)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @tmp TABLE (neworder int NOT NULL, useID int NOT NULL, groupSetOrder int NOT NULL);

    INSERT INTO @tmp (useID, groupSetOrder, newOrder)
    SELECT useID, groupSetOrder, ROW_NUMBER() OVER(ORDER BY groupSetOrder) as newOrder
    FROM dbo.ams_memberGroupSetUsage
    WHERE siteResourceID = @siteResourceID
    AND isnull(area,'') = isnull(@area,'');

    UPDATE mgsu
    SET mgsu.groupSetOrder = t.neworder
    FROM dbo.ams_memberGroupSetUsage mgsu
    INNER JOIN @tmp t on mgsu.useID = t.useID
    WHERE mgsu.siteResourceID = @siteResourceID
    AND isnull(mgsu.area,'') = isnull(@area,'');

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_moveMemberGroupSetUsage
@useID int,
@dir varchar(4)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @currentOrderNum int, @siteResourceID int, @area varchar(20);

    SELECT @currentOrderNum=groupSetOrder, @siteResourceID=siteResourceID, @area=area
    FROM dbo.ams_memberGroupSetUsage
    WHERE useID = @useID;

    BEGIN TRAN;
        IF @dir = 'up' BEGIN
            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder + 1
            WHERE siteResourceID = @siteResourceID
            AND isnull(area,'') = isnull(@area,'')
            AND groupSetOrder >= @currentOrderNum - 1;

            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder - 2
            WHERE useID = @useID;
        END
        ELSE BEGIN
            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder - 1
            WHERE siteResourceID = @siteResourceID
            AND isnull(area,'') = isnull(@area,'')
            AND groupSetOrder <= @currentOrderNum + 1;

            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder + 2
            WHERE useID = @useID;
        END

        EXEC dbo.ams_reorderMemberGroupSetUsage @siteResourceID=@siteResourceID, @area=@area;
    COMMIT TRAN;

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_removeMemberGroupSetUsage
@useID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @siteResourceID int, @area varchar(20), @useSiteResourceID int;

    SELECT @siteResourceID = siteResourceID, @area = area, @useSiteResourceID = useSiteResourceID
    FROM dbo.ams_memberGroupSetUsage
    WHERE useID = @useID;

    BEGIN TRAN;
        -- Delete the usage record
        DELETE FROM dbo.ams_memberGroupSetUsage
        WHERE useID = @useID;

        -- Clean up the associated site resource if it exists
        IF @useSiteResourceID IS NOT NULL BEGIN
            DELETE FROM dbo.cms_siteResources
            WHERE siteResourceID = @useSiteResourceID;
        END

        -- Reorder remaining items
        EXEC dbo.ams_reorderMemberGroupSetUsage @siteResourceID=@siteResourceID, @area=@area;
    COMMIT TRAN;

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @adminViewRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

	CREATE TABLE #ajaxComponentMethods (
		autoid int IDENTITY(1,1),
		methodName varchar(500),
		resourceTypeFunctionID int,
		methodID int
	);

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES
		('getGroupSetsJSON', @adminViewRTFID),
		('getAvailableAndSelectedGroupSetsJSON', @adminViewRTFID),
		('createGroupSetUsage', @adminViewRTFID),
		('createGroupSet', @adminViewRTFID),
		('validateGroupSetUsage', @adminViewRTFID),
		('gsRemove', @adminViewRTFID),
		('gsMove', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='GROUPSETWIDGET',
		@requestCFC='model.admin.common.modules.groupSetSelector.groupSetSelector',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

DECLARE @componentID int, @RTFID int;

IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
  	DROP TABLE #ajaxComponentMethods;
CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

SELECT @RTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
  VALUES 
  	('getPreviewMemberGroupSetData', @RTFID);

EXEC dbo.ajax_addComponentMethodRightsBulk
  	@componentName='ADMREPORTS',
  	@requestCFC='model.admin.reports.report',
  	@componentID=@componentID OUTPUT;

IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
  	DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
  	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
  	SELECT ERROR_MESSAGE();
END CATCH
GO

-- Create stored procedure for getting member data by group sets (similar to ams_getMemberDataByFieldSets)
CREATE PROC dbo.ams_getMemberDataByGroupSets
@orgID int,
@groupSetIDList varchar(max),
@membersTableName varchar(60),
@membersResultTableName varchar(60),
@mode varchar(10) = 'view',
@outputGroupsXML xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..'+@membersTableName) IS NULL
		RAISERROR('Designated source table does not exist.',16,1);
	IF OBJECT_ID('tempdb..'+@membersResultTableName) IS NULL
		RAISERROR('Designated result table does not exist.',16,1);

	-- Parse group set IDs
	CREATE TABLE #tblGroupSets (groupSetID int PRIMARY KEY, groupSetName varchar(200), groupSetOrder int);
	CREATE TABLE #tblGroups (groupID int PRIMARY KEY, groupSetID int, groupName varchar(115), labelOverride varchar(100), groupOrder int);
	CREATE TABLE #tblOutputGroups (groupSetID int, groupSetName varchar(200), groupSetOrder int, groupID int, groupName varchar(115), labelOverride varchar(100), groupOrder int);

	DECLARE @groupSetID int, @groupSetName varchar(200), @groupSetOrder int = 1;

	-- Parse the comma-separated list of group set IDs
	DECLARE @xml xml = '<root><item>' + REPLACE(@groupSetIDList, ',', '</item><item>') + '</item></root>';

	INSERT INTO #tblGroupSets (groupSetID, groupSetName, groupSetOrder)
	SELECT
		CAST(x.value('.', 'int') AS int) as groupSetID,
		mgs.groupSetName,
		ROW_NUMBER() OVER (ORDER BY CAST(x.value('.', 'int') AS int)) as groupSetOrder
	FROM @xml.nodes('/root/item') AS t(x)
	INNER JOIN dbo.ams_memberGroupSets mgs ON mgs.groupSetID = CAST(x.value('.', 'int') AS int)
	WHERE mgs.orgID = @orgID
	AND CAST(x.value('.', 'int') AS int) > 0;

	-- Get all groups for the specified group sets
	INSERT INTO #tblGroups (groupID, groupSetID, groupName, labelOverride, groupOrder)
	SELECT
		mgsg.groupID,
		mgsg.groupSetID,
		g.groupName,
		ISNULL(NULLIF(mgsg.labelOverride, ''), g.groupName) as labelOverride,
		ISNULL(mgsg.groupOrder, 0) as groupOrder
	FROM #tblGroupSets gs
	INNER JOIN dbo.ams_memberGroupSetGroups mgsg ON mgsg.groupSetID = gs.groupSetID
	INNER JOIN dbo.ams_groups g ON g.groupID = mgsg.groupID
	WHERE g.orgID = @orgID
	AND g.status <> 'D'
	ORDER BY gs.groupSetOrder, mgsg.groupOrder, g.groupName;

	-- Build output groups data
	INSERT INTO #tblOutputGroups (groupSetID, groupSetName, groupSetOrder, groupID, groupName, labelOverride, groupOrder)
	SELECT
		gs.groupSetID,
		gs.groupSetName,
		gs.groupSetOrder,
		tg.groupID,
		tg.groupName,
		tg.labelOverride,
		tg.groupOrder
	FROM #tblGroupSets gs
	INNER JOIN #tblGroups tg ON tg.groupSetID = gs.groupSetID
	ORDER BY gs.groupSetOrder, tg.groupOrder, tg.groupName;

	-- Create XML output for groups
	SET @outputGroupsXML = (
		SELECT
			groupSetID as '@groupSetID',
			groupSetName as '@groupSetName',
			groupSetOrder as '@groupSetOrder',
			(
				SELECT
					groupID as '@groupID',
					groupName as '@groupName',
					labelOverride as '@labelOverride',
					groupOrder as '@groupOrder'
				FROM #tblOutputGroups og2
				WHERE og2.groupSetID = og1.groupSetID
				ORDER BY og2.groupOrder, og2.groupName
				FOR XML PATH('group'), TYPE
			)
		FROM #tblOutputGroups og1
		GROUP BY groupSetID, groupSetName, groupSetOrder
		ORDER BY groupSetOrder
		FOR XML PATH('groupSet'), ROOT('groupSets')
	);

	-- Build dynamic SQL to add group membership columns to result table
	DECLARE @finalSQL nvarchar(max) = '', @selectSQL nvarchar(max) = '', @crlf nvarchar(2) = char(13)+char(10);
	DECLARE @column_list nvarchar(max) = '';

	-- Add base member columns
	SET @selectSQL = 'SELECT m.memberID' + @crlf;

	-- Add group membership columns for each group in the group sets
	DECLARE group_cursor CURSOR FOR
	SELECT
		'gs' + CAST(groupSetID AS varchar(10)) + '_g' + CAST(groupID AS varchar(10)) as columnName,
		'CASE WHEN EXISTS (SELECT 1 FROM dbo.cache_members_groups cmg WHERE cmg.memberID = m.memberID AND cmg.groupID = ' + CAST(groupID AS varchar(10)) + ') THEN ''Yes'' ELSE ''No'' END' as columnSQL,
		labelOverride as groupLabel
	FROM #tblOutputGroups
	ORDER BY groupSetOrder, groupOrder, groupName;

	DECLARE @columnName varchar(50), @columnSQL varchar(500), @groupLabel varchar(115);

	OPEN group_cursor;
	FETCH NEXT FROM group_cursor INTO @columnName, @columnSQL, @groupLabel;

	WHILE @@FETCH_STATUS = 0
	BEGIN
		SET @selectSQL = @selectSQL + ', ' + @columnSQL + ' as [' + @groupLabel + ']' + @crlf;
		SET @column_list = @column_list + ', [' + @groupLabel + '] varchar(3) NULL';

		FETCH NEXT FROM group_cursor INTO @columnName, @columnSQL, @groupLabel;
	END;

	CLOSE group_cursor;
	DEALLOCATE group_cursor;

	-- Complete the SELECT statement
	SET @selectSQL = @selectSQL + 'FROM dbo.ams_members m' + @crlf +
		'INNER JOIN ' + @membersTableName + ' mt ON mt.memberID = m.memberID' + @crlf +
		'WHERE m.orgID = @orgID';

	-- Add columns to result table if we have any groups
	IF @column_list <> ''
	BEGIN
		DECLARE @ParmDefinition nvarchar(500) = N'@orgID int', @alterSQL nvarchar(max);
		
		SET @alterSQL = 'ALTER TABLE ' + QUOTENAME(@membersResultTableName) + ' ADD ' + STUFF(@column_list, 1, 2, '');
		EXEC (@alterSQL);

		-- Execute the final SQL to populate results
		SET @finalSQL = 'INSERT INTO ' + QUOTENAME(@membersResultTableName) + ' (' + 
			STUFF(@column_list, 1, 2, '') + ')' + @crlf + @selectSQL;
		EXEC sp_executesql @finalSQL, @ParmDefinition, @orgID=@orgID;
	END;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH

GO
