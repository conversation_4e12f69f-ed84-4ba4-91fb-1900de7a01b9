USE membercentral
GO

CREATE TABLE [dbo].[ams_memberGroupSetUsage](
    [useID] [int] IDENTITY(1,1) NOT NULL,
    [siteResourceID] [int] NOT NULL,
    [groupSetID] [int] NOT NULL,
    [area] [varchar](20) NULL,
    [useSiteResourceID] [int] NULL,
    [groupSetOrder] [int] NOT NULL DEFAULT(1),
    CONSTRAINT [PK_ams_memberGroupSetUsage] PRIMARY KEY CLUSTERED 
    (
        [useID] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

-- Add foreign key constraints
ALTER TABLE [dbo].[ams_memberGroupSetUsage] WITH CHECK ADD CONSTRAINT [FK_ams_memberGroupSetUsage_ams_memberGroupSets] 
    FOREIGN KEY([groupSetID]) REFERENCES [dbo].[ams_memberGroupSets] ([groupSetID])

ALTER TABLE [dbo].[ams_memberGroupSetUsage] CHECK CONSTRAINT [FK_ams_memberGroupSetUsage_ams_memberGroupSets]

ALTER TABLE [dbo].[ams_memberGroupSetUsage] WITH CHECK ADD CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources] 
    FOREIGN KEY([siteResourceID]) REFERENCES [dbo].[cms_siteResources] ([siteResourceID])

ALTER TABLE [dbo].[ams_memberGroupSetUsage] CHECK CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources]

ALTER TABLE [dbo].[ams_memberGroupSetUsage] WITH CHECK ADD CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources_use] 
    FOREIGN KEY([useSiteResourceID]) REFERENCES [dbo].[cms_siteResources] ([siteResourceID])

ALTER TABLE [dbo].[ams_memberGroupSetUsage] CHECK CONSTRAINT [FK_ams_memberGroupSetUsage_cms_siteResources_use]

-- Create indexes for performance
CREATE NONCLUSTERED INDEX [IX_ams_memberGroupSetUsage_siteResourceID_area] ON [dbo].[ams_memberGroupSetUsage]
(
    [siteResourceID] ASC,
    [area] ASC
)
INCLUDE([groupSetID],[groupSetOrder]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

CREATE NONCLUSTERED INDEX [IX_ams_memberGroupSetUsage_groupSetID] ON [dbo].[ams_memberGroupSetUsage]
(
    [groupSetID] ASC
) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE PROC dbo.ams_createMemberGroupSetUsage
@siteResourceID int,
@groupSetID int,
@area varchar(20),
@createSiteResourceID bit,
@useID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @usesiteResourceID int, @siteResourceTypeID int, @siteid int, @maxorder int, @orgID int;

    set @useID = null;
    set @usesiteResourceID = null;

    -- Check if usage already exists
    select @useID = useID 
    from dbo.ams_memberGroupSetUsage 
    where siteResourceID = @siteResourceID 
    and groupSetID = @groupSetID 
    and isnull(area,'') = isnull(@area,'');

    IF @useID is null begin
        BEGIN TRAN;	
            IF @createSiteResourceID = 1 BEGIN
                set @siteResourceTypeID = dbo.fn_getResourceTypeID('memberGroupSet');
                
                -- Get orgID from group set
                select @orgID = gs.orgID
                from dbo.ams_memberGroupSets gs
                where gs.groupSetID = @groupSetID;
                
                -- Get siteID from orgID (use first site for the org)
                select top 1 @siteID = siteID 
                from dbo.sites 
                where orgID = @orgID
                order by siteID;

                exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=1,
                    @siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@usesiteResourceID OUTPUT;
            END

            -- get max order
            select @maxorder = isnull(MAX(groupSetOrder), 0) + 1 
            FROM dbo.ams_memberGroupSetUsage
            WHERE siteResourceID = @siteResourceID
            AND isnull(area,'') = isnull(@area,'');

            INSERT INTO dbo.ams_memberGroupSetUsage (siteResourceID, groupSetID, area, useSiteResourceID, groupSetOrder)
            VALUES (@siteResourceID, @groupSetID, nullif(@area,''), @usesiteResourceID, @maxorder);

            SELECT @useID = SCOPE_IDENTITY();
        COMMIT TRAN;
    END

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_reorderMemberGroupSetUsage
@siteResourceID int,
@area varchar(20)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @tmp TABLE (neworder int NOT NULL, useID int NOT NULL, groupSetOrder int NOT NULL);

    INSERT INTO @tmp (useID, groupSetOrder, newOrder)
    SELECT useID, groupSetOrder, ROW_NUMBER() OVER(ORDER BY groupSetOrder) as newOrder
    FROM dbo.ams_memberGroupSetUsage
    WHERE siteResourceID = @siteResourceID
    AND isnull(area,'') = isnull(@area,'');

    UPDATE mgsu
    SET mgsu.groupSetOrder = t.neworder
    FROM dbo.ams_memberGroupSetUsage mgsu
    INNER JOIN @tmp t on mgsu.useID = t.useID
    WHERE mgsu.siteResourceID = @siteResourceID
    AND isnull(mgsu.area,'') = isnull(@area,'');

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_moveMemberGroupSetUsage
@useID int,
@dir varchar(4)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @currentOrderNum int, @siteResourceID int, @area varchar(20);

    SELECT @currentOrderNum=groupSetOrder, @siteResourceID=siteResourceID, @area=area
    FROM dbo.ams_memberGroupSetUsage
    WHERE useID = @useID;

    BEGIN TRAN;
        IF @dir = 'up' BEGIN
            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder + 1
            WHERE siteResourceID = @siteResourceID
            AND isnull(area,'') = isnull(@area,'')
            AND groupSetOrder >= @currentOrderNum - 1;

            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder - 2
            WHERE useID = @useID;
        END
        ELSE BEGIN
            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder - 1
            WHERE siteResourceID = @siteResourceID
            AND isnull(area,'') = isnull(@area,'')
            AND groupSetOrder <= @currentOrderNum + 1;

            UPDATE dbo.ams_memberGroupSetUsage
            SET groupSetOrder = groupSetOrder + 2
            WHERE useID = @useID;
        END

        EXEC dbo.ams_reorderMemberGroupSetUsage @siteResourceID=@siteResourceID, @area=@area;
    COMMIT TRAN;

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_removeMemberGroupSetUsage
@useID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    DECLARE @siteResourceID int, @area varchar(20), @useSiteResourceID int;

    SELECT @siteResourceID = siteResourceID, @area = area, @useSiteResourceID = useSiteResourceID
    FROM dbo.ams_memberGroupSetUsage
    WHERE useID = @useID;

    BEGIN TRAN;
        -- Delete the usage record
        DELETE FROM dbo.ams_memberGroupSetUsage
        WHERE useID = @useID;

        -- Clean up the associated site resource if it exists
        IF @useSiteResourceID IS NOT NULL BEGIN
            DELETE FROM dbo.cms_siteResources
            WHERE siteResourceID = @useSiteResourceID;
        END

        -- Reorder remaining items
        EXEC dbo.ams_reorderMemberGroupSetUsage @siteResourceID=@siteResourceID, @area=@area;
    COMMIT TRAN;

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO

DECLARE @componentID int;

IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
	DROP TABLE #ajaxComponentMethods;

CREATE TABLE #ajaxComponentMethods (
	methodName varchar(100),
	resourceTypeFunctionID int
);

DECLARE @adminViewRTFID int;

SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
VALUES 
	('getGroupSetsJSON', @adminViewRTFID),
	('getAvailableAndSelectedGroupSetsJSON', @adminViewRTFID),
	('createGroupSetUsage', @adminViewRTFID),
	('createGroupSet', @adminViewRTFID),
	('validateGroupSetUsage', @adminViewRTFID),
	('gsRemove', @adminViewRTFID),
	('gsMove', @adminViewRTFID);

EXEC dbo.ajax_addComponentMethodRightsBulk
	@componentName='GROUPSETWIDGET',
	@requestCFC='model.admin.common.modules.groupSetSelector.groupSetSelector',
	@componentID=@componentID OUTPUT;

IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
	DROP TABLE #ajaxComponentMethods;

GO
