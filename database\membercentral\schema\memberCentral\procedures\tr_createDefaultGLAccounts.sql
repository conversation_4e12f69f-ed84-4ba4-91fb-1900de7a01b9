ALTER PROC dbo.tr_createDefaultGLAccounts
@orgID int,
@orgIdentityID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @GLAccountID int, @batchID int, @invoiceProfileID int, @recordedByMemberID int;

	SELECT @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();

	BEGIN TRAN;
		-- Create one default Invoice Profile
		insert into dbo.tr_invoiceProfiles (orgID, profileName, status, orgIdentityID)
		values (@orgID, 'Default Invoice Profile', 'A', @orgIdentityID);
			set @invoiceProfileID = SCOPE_IDENTITY();

		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=2, @accountName='Accounts Receivable', @accountCode='ACCOUNTSRECEIVABLE', @GLCode='ACCOUNTSRECEIVABLE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, @salesTaxProfileID=null, @salesTaxTaxJarCategoryID=null, @recordedByMemberID=@recordedByMemberID, @GLAccountID=@GLAccountID output;
		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deposits', @accountCode='DEPOSITS', @GLCode='DEPOSITS', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, @salesTaxProfileID=null, @salesTaxTaxJarCategoryID=null, @recordedByMemberID=@recordedByMemberID, @GLAccountID=@GLAccountID output;
		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Sales Tax Accounts', @accountCode=null, @GLCode='SALESTAX', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, @salesTaxProfileID=null, @salesTaxTaxJarCategoryID=null, @recordedByMemberID=@recordedByMemberID, @GLAccountID=@GLAccountID output;
		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=4, @accountName='Write Off', @accountCode='WRITEOFF', @GLCode='WRITEOFF', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, @salesTaxProfileID=null, @salesTaxTaxJarCategoryID=null, @recordedByMemberID=@recordedByMemberID, @GLAccountID=@GLAccountID output;
	
		-- create pending payment batch
		EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=null, @batchTypeID=1, @batchCode='PENDINGPAYMENTS', @batchName='Pending Payments', 
			@controlAmt=0, @controlCount=0, @depositDate='1/1/2050', @isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT;
	COMMIT TRAN;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
