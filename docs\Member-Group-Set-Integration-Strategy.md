# Member Group Set Selector Integration Strategy

## Overview
This document outlines the comprehensive strategy for integrating the Multiple Group Set Selector widget into MemberCentral's member management interfaces, focusing on UI-only enhancements without database schema changes.

## Integration Points

### 1. Member Administration Forms
**Target Forms:**
- `membercentral/model/admin/members/dsp_memberForm_demographicsEdit.cfm` - Member profile editing
- `membercentral/model/admin/members/frm_memberAdd.cfm` - New member creation
- `membercentral/model/admin/members/dsp_memberForm_customFields.cfm` - Custom fields tab

**Integration Approach:**
- Add multiple group set selector widget to custom fields section
- Store selections in existing member custom field as comma-separated values
- Use hidden form field pattern for data persistence

### 2. Member Settings Configuration
**Target Form:**
- `membercentral/model/admin/memberSettings/frm_edit.cfm` - Member settings configuration

**Current Field Set Integration:**
```coldfusion
<div class="form-group row mt-4">
    <div class="col-sm-3">Use these Member Field Sets<br/>when showing the "Custom" tab</div>
    <div class="col-sm-9">
        #local.strCustomFieldsSelector.html#
    </div>
</div>
```

**Proposed Group Set Integration:**
```coldfusion
<div class="form-group row mt-4">
    <div class="col-sm-3">Use these Member Group Sets<br/>for member profile organization</div>
    <div class="col-sm-9">
        #local.strGroupSetSelector.html#
    </div>
</div>
```

### 3. Member Registration Forms
**Target Forms:**
- Site-specific registration forms in `membercentral/sitecomponents/*/custom/`
- Join forms and membership applications
- Profile update forms

**Integration Pattern:**
- Add group set selector to registration workflow
- Store selections during member creation process
- Support pre-selection based on membership type

## Data Storage Strategy

### Custom Field Implementation
**Field Configuration:**
- **Field Name:** "Selected Group Sets"
- **Data Type:** STRING (varchar)
- **Display Type:** TEXTBOX (hidden from UI)
- **Max Length:** 500 characters
- **Allow Multiple:** No (using comma-separated values instead)
- **Allow Null:** Yes

**Storage Format:**
```
"1,5,12,8" // Comma-separated group set IDs
```

**Advantages:**
- No database schema changes required
- Leverages existing custom field infrastructure
- Supports existing import/export processes
- Maintains backward compatibility

### Alternative Storage Options
1. **JSON Format:** `{"groupSets":[1,5,12,8],"lastUpdated":"2025-06-26"}`
2. **Pipe-Separated:** `1|5|12|8` (following existing MemberCentral patterns)
3. **Member Notes:** Store in structured member notes field

## Widget Integration Patterns

### 1. Standard Integration
```coldfusion
<cfset local.strGroupSetSelector = local.objGroupSetSelector.getMultipleGroupSetSelector(
    selectorID="memberGroupSets",
    siteID=local.siteID,
    orgID=local.orgID,
    selectedGroupSetIDs=local.memberGroupSetIDs,
    hasPermissionAction=true,
    hasOrderingAction=true
)>

<div class="form-group row">
    <div class="col-sm-3">Group Set Memberships</div>
    <div class="col-sm-9">
        #local.strGroupSetSelector.html#
    </div>
</div>
```

### 2. Modal Integration
```coldfusion
<cfset local.strGroupSetSelector = local.objGroupSetSelector.getMultipleGroupSetSelector(
    selectorID="modalGroupSets",
    siteID=local.siteID,
    orgID=local.orgID,
    selectedGroupSetIDs=local.memberGroupSetIDs,
    selectedGSGridHeight=200,
    availableGSGridHeight=250
)>
```

### 3. Inline Preview Integration
```coldfusion
<cfset local.strGroupSetSelector = local.objGroupSetSelector.getMultipleGroupSetSelector(
    selectorID="previewGroupSets",
    siteID=local.siteID,
    orgID=local.orgID,
    selectedGroupSetIDs=local.memberGroupSetIDs,
    useInlinePreview=true,
    inlinePreviewSectionID="memberGroupSetPreview"
)>
```

## Form Processing Implementation

### Save Processing
```coldfusion
<cfif structKeyExists(form, "selectedGroupSets_memberGroupSets")>
    <cfset local.selectedGroupSets = form["selectedGroupSets_memberGroupSets"]>
    
    <!-- Save to custom field -->
    <cfif len(local.selectedGroupSets)>
        <cfset local.objMemberData.setMemberData(
            memberID=local.memberID,
            orgID=local.orgID,
            columnName="Selected Group Sets",
            columnValue=local.selectedGroupSets
        )>
    </cfif>
</cfif>
```

### Load Processing
```coldfusion
<cfset local.memberGroupSetIDs = "">
<cfset local.qryMemberGroupSets = local.objMemberData.getMemberData(
    memberID=local.memberID,
    columnName="Selected Group Sets"
)>
<cfif local.qryMemberGroupSets.recordCount>
    <cfset local.memberGroupSetIDs = local.qryMemberGroupSets.columnValue>
</cfif>
```

## Validation and Error Handling

### Client-Side Validation
```javascript
function validateMemberGroupSets() {
    let selectedGroupSets = $('#selectedGroupSets_memberGroupSets').val();
    let groupSetArray = selectedGroupSets ? selectedGroupSets.split(',') : [];
    
    // Validate maximum selections
    if (groupSetArray.length > 10) {
        showError('Maximum 10 group sets can be selected');
        return false;
    }
    
    return true;
}
```

### Server-Side Validation
```coldfusion
<cfif structKeyExists(form, "selectedGroupSets_memberGroupSets")>
    <cfset local.selectedGroupSets = listToArray(form["selectedGroupSets_memberGroupSets"])>
    
    <cfif arrayLen(local.selectedGroupSets) gt 10>
        <cfset local.errors.groupSets = "Maximum 10 group sets allowed">
    </cfif>
    
    <!-- Validate group set IDs exist and belong to organization -->
    <cfloop array="#local.selectedGroupSets#" index="local.groupSetID">
        <cfif not local.objGroupSets.groupSetExists(local.groupSetID, local.orgID)>
            <cfset local.errors.groupSets = "Invalid group set selection">
            <cfbreak>
        </cfif>
    </cfloop>
</cfif>
```

## Migration and Deployment

### Phase 1: Core Widget Implementation
- [x] Complete multiple group set selector widget
- [x] Implement client-side state management
- [x] Create AJAX method registrations

### Phase 2: Member Settings Integration
- [ ] Add group set selector to member settings form
- [ ] Implement custom field creation
- [ ] Test settings persistence

### Phase 3: Member Administration Integration
- [ ] Integrate into member profile editing forms
- [ ] Add to member creation workflow
- [ ] Implement data loading and saving

### Phase 4: Registration Form Integration
- [ ] Identify target registration forms
- [ ] Implement site-specific integrations
- [ ] Test member creation with group set selections

### Phase 5: Reporting and Analytics
- [ ] Create member group set reports
- [ ] Implement preview functionality
- [ ] Add analytics for group set usage

## Testing Strategy

### Unit Testing
- Widget rendering with various configurations
- Client-side state management functions
- Data persistence and retrieval

### Integration Testing
- Form submission with group set selections
- Member profile loading with existing selections
- Cross-browser compatibility

### User Acceptance Testing
- Member administrator workflow testing
- Registration form usability testing
- Performance testing with large group set lists

## Rollback Procedures

### Emergency Rollback
1. Remove widget includes from forms
2. Disable AJAX method registrations
3. Preserve existing data in custom fields

### Gradual Rollback
1. Disable new functionality per form
2. Maintain data integrity
3. Provide migration path back to previous state

## Performance Considerations

### Client-Side Optimization
- Lazy loading of available group sets
- Efficient array manipulation for selections
- Minimal DOM updates during interactions

### Server-Side Optimization
- Cached group set data retrieval
- Efficient custom field queries
- Optimized AJAX response sizes

## Security Considerations

### Access Control
- Validate user permissions for group set access
- Ensure organization-scoped group set selections
- Implement proper CSRF protection

### Data Validation
- Sanitize group set ID inputs
- Validate comma-separated value format
- Prevent injection attacks through custom field values
