<cfsavecontent variable="local.gsSelectorJS">
	<cfoutput>
	<script language="javascript">
		<cfif arguments.hasPermissionAction>
			var #ToScript(local.permsGotoLink,"mca_perms_link_#arguments.selectorID#")#
		</cfif>
		function loadGroupSetGrids_#arguments.selectorID#() {
			let loadGSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					let availGSListSource = $('##mc_AvailGSList_#arguments.selectorID#').html();
					let availGSListTemplate = Handlebars.compile(availGSListSource);
					$('##availGSGridContainer_#arguments.selectorID#').html(availGSListTemplate(r));
					mcActivateTooltip($('##availGSGridContainer_#arguments.selectorID#'));

					let incGSListSource = $('##mc_IncGSList_#arguments.selectorID#').html();
					let incGSListTemplate = Handlebars.compile(incGSListSource);
					$('##incGSGridContainer_#arguments.selectorID#').html(incGSListTemplate(r));
					mcActivateTooltip($('##incGSGridContainer_#arguments.selectorID#'));

					$('.gsCountSpan').removeClass('d-none');
					$('.gsCountLoadingSpan').addClass('d-none');
					$('.selGSCount').text(r.arrselectedgroupsets ? r.arrselectedgroupsets.length : 0);

					<cfif arguments.hasOrderingAction>
						// Initialize sortable for selected group sets
						if (typeof Sortable !== 'undefined') {
							new Sortable(document.getElementById('incGSGridContainer_#arguments.selectorID#'), {
								animation: 150,
								ghostClass: 'sortable-ghost',
								onEnd: function(evt) {
									if (evt.oldIndex !== evt.newIndex) {
										// Get the groupSetID from the moved element
										let gsID = parseInt($(evt.item).data('group-set-id'));

										// Update the array order directly based on drag position
										let movedItem = selectedGroupSets_#arguments.selectorID#.splice(evt.oldIndex, 1)[0];
										selectedGroupSets_#arguments.selectorID#.splice(evt.newIndex, 0, movedItem);

										updateHiddenField_#arguments.selectorID#();
										// Reload to reflect new order
										loadGroupSetGrids_#arguments.selectorID#();
									}
								}
							});
						}
					</cfif>
				} else {
					let reloadHTML = '<div class="text-center mt-5"><span class="d-block text-danger mb-2">Sorry, we were unable to load the data.</span><i class="fa-solid fa-rotate-right fa-2x cursor-pointer" onclick="loadGroupSetGrids_#arguments.selectorID#()"></i><span class="d-block">Reload</span></div>';
					$('##availGSGridContainer_#arguments.selectorID#').html(reloadHTML);
					$('##incGSGridContainer_#arguments.selectorID#').html(reloadHTML);
				}
			};

			$('##availGSGridContainer_#arguments.selectorID#').html(mca_getLoadingHTML());
			$('##incGSGridContainer_#arguments.selectorID#').html(mca_getLoadingHTML());
			<cfif len(arguments.getGroupSetDataFunc)>
				#arguments.getGroupSetDataFunc#(loadGSResult);
			<cfelse>
				getAvailableAndSelectedGroupSetsJSON_#arguments.selectorID#(loadGSResult);
			</cfif>
		}

		// Client-side state management for selected group sets
		let selectedGroupSets_#arguments.selectorID# = [];
		let memberGroupSets_#arguments.selectorID# = []; // Group sets member belongs to through group membership

		function initializeSelectedGroupSets_#arguments.selectorID#() {
			// Initialize from selectedGroupSetIDs argument
			let selectedIDs = '#arguments.selectedGroupSetIDs#';
			if (selectedIDs) {
				selectedGroupSets_#arguments.selectorID# = selectedIDs.split(',').map(id => parseInt(id.trim())).filter(id => id > 0);
			}
			updateHiddenField_#arguments.selectorID#();
		}

		function loadMemberGroupSetAssociations_#arguments.selectorID#(memberID) {
			if (!memberID || memberID <= 0) return;

			TS_AJX({
				url: '#application.objAdmin.buildLinkToTool(toolType="AJAX")#',
				data: {
					mcproxy_component: 'GROUPSETWIDGET',
					mcproxy_method: 'getMemberGroupSetAssociations',
					mcproxy_memberID: memberID,
					mcproxy_orgID: #arguments.orgID#
				},
				success: function(response) {
					if (response.success) {
						memberGroupSets_#arguments.selectorID# = response.memberGroupSets || [];
						// Update display to show member's current group set associations
						updateMemberGroupSetDisplay_#arguments.selectorID#();
					}
				},
				error: function() {
					console.log('Error loading member group set associations');
				}
			});
		}

		function updateMemberGroupSetDisplay_#arguments.selectorID#() {
			// Add visual indicators for group sets the member belongs to
			memberGroupSets_#arguments.selectorID#.forEach(function(groupSet) {
				let gsElement = $('[data-group-set-id="' + groupSet.groupsetid + '"]');
				if (gsElement.length) {
					gsElement.addClass('member-group-set');
					gsElement.attr('title', 'Member belongs to this group set through group membership (' +
						groupSet.memberGroupCount + '/' + groupSet.totalGroupCount + ' groups)');
				}
			});
		}

		function getAvailableAndSelectedGroupSetsJSON_#arguments.selectorID#(onCompleteFunc){
			<cfif len(arguments.getGroupSetDataFunc)>
				#arguments.getGroupSetDataFunc#(onCompleteFunc);
			<cfelse>
				let objParams = {
					siteID: #arguments.siteID#,
					orgID: #arguments.orgID#
				};
				TS_AJX('GROUPSETWIDGET','getGroupSetsJSON',objParams,function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						// Separate available and selected group sets
						let availableGroupSets = [];
						let selectedGroupSets = [];

						r.arravailablegroupsets.forEach(function(gs) {
							if (selectedGroupSets_#arguments.selectorID#.includes(gs.groupsetid)) {
								selectedGroupSets.push({
									groupsetid: gs.groupsetid,
									groupsetname: gs.groupsetname,
									isBeingUsed: gs.isBeingUsed
								});
							} else {
								availableGroupSets.push(gs);
							}
						});

						// Create response structure matching expected format
						let response = {
							success: true,
							arravailablegroupsets: availableGroupSets,
							arrselectedgroupsets: selectedGroupSets
						};

						onCompleteFunc(response);
					} else {
						onCompleteFunc(r);
					}
				},onCompleteFunc,60000,onCompleteFunc);
			</cfif>
		}

		function addGroupSet_#arguments.selectorID#(gsID) {
			<cfif len(arguments.addGroupSetFunc)>
				#arguments.addGroupSetFunc#(gsID, function() {
					// Add to selected array
					if (!selectedGroupSets_#arguments.selectorID#.includes(gsID)) {
						selectedGroupSets_#arguments.selectorID#.push(gsID);
						updateHiddenField_#arguments.selectorID#();
						loadGroupSetGrids_#arguments.selectorID#();
					}
				});
			<cfelse>
				// Add to selected array
				if (!selectedGroupSets_#arguments.selectorID#.includes(gsID)) {
					selectedGroupSets_#arguments.selectorID#.push(gsID);
					updateHiddenField_#arguments.selectorID#();
					loadGroupSetGrids_#arguments.selectorID#();
				}
			</cfif>
		}

		function removeGroupSet_#arguments.selectorID#(gsID) {
			<cfif len(arguments.removeGroupSetFunc)>
				#arguments.removeGroupSetFunc#(gsID, function() {
					// Remove from selected array
					let index = selectedGroupSets_#arguments.selectorID#.indexOf(gsID);
					if (index > -1) {
						selectedGroupSets_#arguments.selectorID#.splice(index, 1);
						updateHiddenField_#arguments.selectorID#();
						loadGroupSetGrids_#arguments.selectorID#();
					}
				});
			<cfelse>
				// Remove from selected array
				let index = selectedGroupSets_#arguments.selectorID#.indexOf(gsID);
				if (index > -1) {
					selectedGroupSets_#arguments.selectorID#.splice(index, 1);
					updateHiddenField_#arguments.selectorID#();
					loadGroupSetGrids_#arguments.selectorID#();
				}
			</cfif>
		}

		<cfif arguments.hasOrderingAction>
			function moveGroupSet_#arguments.selectorID#(gsID, dir){
				<cfif len(arguments.orderGroupSetFunc)>
					#arguments.orderGroupSetFunc#(gsID, dir, function() {
						// Move in selected array
						let index = selectedGroupSets_#arguments.selectorID#.indexOf(gsID);
						if (index > -1) {
							if (dir === 'up' && index > 0) {
								// Swap with previous element
								[selectedGroupSets_#arguments.selectorID#[index-1], selectedGroupSets_#arguments.selectorID#[index]] =
								[selectedGroupSets_#arguments.selectorID#[index], selectedGroupSets_#arguments.selectorID#[index-1]];
							} else if (dir === 'down' && index < selectedGroupSets_#arguments.selectorID#.length - 1) {
								// Swap with next element
								[selectedGroupSets_#arguments.selectorID#[index], selectedGroupSets_#arguments.selectorID#[index+1]] =
								[selectedGroupSets_#arguments.selectorID#[index+1], selectedGroupSets_#arguments.selectorID#[index]];
							}
							updateHiddenField_#arguments.selectorID#();
							loadGroupSetGrids_#arguments.selectorID#();
						}
					});
				<cfelse>
					// Move in selected array
					let index = selectedGroupSets_#arguments.selectorID#.indexOf(gsID);
					if (index > -1) {
						if (dir === 'up' && index > 0) {
							// Swap with previous element
							[selectedGroupSets_#arguments.selectorID#[index-1], selectedGroupSets_#arguments.selectorID#[index]] =
							[selectedGroupSets_#arguments.selectorID#[index], selectedGroupSets_#arguments.selectorID#[index-1]];
						} else if (dir === 'down' && index < selectedGroupSets_#arguments.selectorID#.length - 1) {
							// Swap with next element
							[selectedGroupSets_#arguments.selectorID#[index], selectedGroupSets_#arguments.selectorID#[index+1]] =
							[selectedGroupSets_#arguments.selectorID#[index+1], selectedGroupSets_#arguments.selectorID#[index]];
						}
						updateHiddenField_#arguments.selectorID#();
						loadGroupSetGrids_#arguments.selectorID#();
					}
				</cfif>
			}
		</cfif>

		function updateHiddenField_#arguments.selectorID#() {
			// Update hidden form field with selected group set IDs
			let hiddenField = $('##selectedGroupSets_#arguments.selectorID#');
			if (hiddenField.length) {
				hiddenField.val(selectedGroupSets_#arguments.selectorID#.join(','));
			}
		}

		// Initialize on page load
		$(document).ready(function() {
			initializeSelectedGroupSets_#arguments.selectorID#();
			<cfif structKeyExists(arguments, "memberID") and arguments.memberID gt 0>
				loadMemberGroupSetAssociations_#arguments.selectorID#(#arguments.memberID#);
			</cfif>
		});

		function showGSErrorMessage_#arguments.selectorID#(title, msg){
			MCModalUtils.showModal({
				isslideout: false,
				iframe: false,
				size: 'md',
				title: title,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				strmodalbody: {
					content: msg
				},
				strmodalfooter: {
					showclose: true
				}
			});
		}

		function editGroupSet_#arguments.selectorID#(gsID) {
			var editURL = '#local.editGroupSetLink#&gsID=' + gsID;
			MCModalUtils.showModal(editURL, function() {
				loadGroupSetGrids_#arguments.selectorID#();
			});
		}

		$(function() {
			initializeSelectedGroupSets_#arguments.selectorID#();
			loadGroupSetGrids_#arguments.selectorID#();
		});
	</script>
	<style>
		##gsGridContainer_#arguments.selectorID# .btn-xs { padding: 0.25rem 0.4rem; }
		##gsGridContainer_#arguments.selectorID# .sortable-ghost { opacity: 0.4; }

		/* Member Group Set Association Indicators */
		##gsGridContainer_#arguments.selectorID# .member-group-set {
			border-left: 4px solid ##28a745 !important;
			background-color: ##f8fff9 !important;
			position: relative;
		}
		##gsGridContainer_#arguments.selectorID# .member-group-set .card-body {
			background-color: ##f8fff9 !important;
		}
		##gsGridContainer_#arguments.selectorID# .member-group-set::before {
			content: "✓ ";
			color: ##28a745;
			font-weight: bold;
			margin-right: 5px;
			position: absolute;
			left: 8px;
			top: 8px;
		}
		##gsGridContainer_#arguments.selectorID# .member-group-set .badge {
			background-color: ##28a745 !important;
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gsSelectorJS)#">

<cfoutput>
<!-- Hidden field to store selected group set IDs -->
<input type="hidden" id="selectedGroupSets_#arguments.selectorID#" name="selectedGroupSets_#arguments.selectorID#" value="#arguments.selectedGroupSetIDs#" />

<div class="row no-gutters" id="gsGridContainer_#arguments.selectorID#">
	<div class="col-12">
		<div class="card card-box">
			<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-sm">
					Selected Group Sets
				</div>
				<span class="gsCountSpan small d-none"><span class="selGSCount pr-1"></span>selected</span>
				<span class="gsCountLoadingSpan small">loading..</span>
			</div>
			<div class="card-body p-0">
				<div id="incGSGridContainer_#arguments.selectorID#" style="height:#arguments.selectedGSGridHeight#px;overflow-y:auto;">
				</div>
				<div class="accordion" id="availGSAccordion_#arguments.selectorID#">
					<div class="card card-box rounded-bottom">
						<div class="card-header bg-light rounded-0" id="heading_#arguments.selectorID#">
							<button class="btn btn-link d-flex align-items-center justify-content-between collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#">
								<span class="font-weight-bold font-size-sm">Available Group Sets</span>
								<div class="d-flex align-items-center">
									<a href="##" name="btnCreateGroupSet" onclick="editGroupSet_#arguments.selectorID#(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Group Set" class="mr-2">
										<i class="fa-regular fa-circle-plus fa-lg"></i>
									</a>
									<i class="fa-solid fa-caret-up font-size-xl"></i>
								</div>
							</button>
						</div>
						<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##availGSAccordion_#arguments.selectorID#">
							<div class="card-body bg-secondary p-2" style="height:#arguments.availableGSGridHeight#px;overflow-y:auto;">
								<div id="availGSGridContainer_#arguments.selectorID#"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="err_gsSelector_#arguments.selectorID#" class="alert alert-danger mt-2 d-none" role="alert"></div>

<!-- Handlebars Template for Available Group Sets -->
<script id="mc_AvailGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	<ul class="list-group list-group-flush">
		{{##each arravailablegroupsets}}
		<li class="list-group-item d-flex align-items-center justify-content-between p-2">
			<div class="flex-grow-1">
				<span class="font-weight-bold">{{groupsetname}}</span>
			</div>
			<div class="btn-group btn-group-sm ml-2" role="group">
				<button type="button" class="btn btn-outline-primary btn-xs" onclick="addGroupSet_#arguments.selectorID#({{groupsetid}});" data-toggle="tooltip" data-placement="top" title="Add Group Set">
					<i class="fa-regular fa-circle-plus"></i>
				</button>
				<button type="button" class="btn btn-outline-secondary btn-xs" onclick="editGroupSet_#arguments.selectorID#({{groupsetid}});" data-toggle="tooltip" data-placement="top" title="Edit Group Set">
					<i class="fa-regular fa-pen-to-square"></i>
				</button>
			</div>
		</li>
		{{/each}}
		{{##unless arravailablegroupsets.length}}
		<li class="list-group-item text-center text-muted p-3">
			<i class="fa-regular fa-folder-open fa-2x d-block mb-2"></i>
			No available group sets
		</li>
		{{/unless}}
	</ul>
</script>

<!-- Handlebars Template for Selected Group Sets -->
<script id="mc_IncGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	<ul class="list-group list-group-flush">
		{{##each arrselectedgroupsets}}
		<li class="list-group-item d-flex align-items-center justify-content-between p-2" data-group-set-id="{{groupsetid}}">
			<div class="flex-grow-1">
				<span class="font-weight-bold">{{groupsetname}}</span>
			</div>
			<div class="btn-group btn-group-sm ml-2" role="group">
				<cfif arguments.hasOrderingAction>
					<a href="##" id="moveUpGSBtn_#arguments.selectorID#_{{groupsetid}}"
						{{##compare @index '!=' 0}}
							class="btn btn-xs btn-outline-dark"
							onclick="$(this).tooltip('hide');moveGroupSet_#arguments.selectorID#({{groupsetid}},'up');return false;"
							data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Up"
						{{/compare}}
						{{##compare @index '==' 0}}
							class="btn btn-xs btn-outline-dark invisible"
						{{/compare}}
						>
						<i class="fa-solid fa-up"></i>
					</a>
					<a href="##" id="moveDownGSBtn_#arguments.selectorID#_{{groupsetid}}"
						{{##compare (math @index "+" 1) '!=' ../arrselectedgroupsets.length}}
							class="btn btn-xs btn-outline-dark"
							onclick="$(this).tooltip('hide');moveGroupSet_#arguments.selectorID#({{groupsetid}},'down');return false;"
							data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Down"
						{{/compare}}
						{{##compare (math @index "+" 1) '==' ../arrselectedgroupsets.length}}
							class="btn btn-xs btn-outline-dark invisible"
						{{/compare}}
						>
						<i class="fa-solid fa-down"></i>
					</a>
				</cfif>
				<button type="button" class="btn btn-outline-secondary btn-xs" onclick="editGroupSet_#arguments.selectorID#({{groupsetid}});" data-toggle="tooltip" data-placement="top" title="Edit Group Set">
					<i class="fa-regular fa-pen-to-square"></i>
				</button>
				<button type="button" class="btn btn-outline-danger btn-xs" onclick="removeGroupSet_#arguments.selectorID#({{groupsetid}});" data-toggle="tooltip" data-placement="top" title="Remove Group Set">
					<i class="fa-regular fa-circle-minus"></i>
				</button>
			</div>
		</li>
		{{/each}}
		{{##unless arrselectedgroupsets.length}}
		<li class="list-group-item text-center text-muted p-3">
			<i class="fa-regular fa-folder-open fa-2x d-block mb-2"></i>
			No group sets selected
		</li>
		{{/unless}}
	</ul>
</script>
</cfoutput>
