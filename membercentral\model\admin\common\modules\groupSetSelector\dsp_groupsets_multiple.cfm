<cfsavecontent variable="local.gsSelectorJS">
	<cfoutput>
	<script language="javascript">
		<cfif arguments.hasPermissionAction>
			var #ToScript(local.permsGotoLink,"mca_perms_link_#arguments.selectorID#")#
		</cfif>
		function loadGroupSetGrids_#arguments.selectorID#() {
			let loadGSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					let availGSListSource = $('##mc_AvailGSList_#arguments.selectorID#').html();
					let availGSListTemplate = Handlebars.compile(availGSListSource);
					$('##availGSGridContainer_#arguments.selectorID#').html(availGSListTemplate(r));
					mcActivateTooltip($('##availGSGridContainer_#arguments.selectorID#'));

					let incGSListSource = $('##mc_IncGSList_#arguments.selectorID#').html();
					let incGSListTemplate = Handlebars.compile(incGSListSource);
					$('##incGSGridContainer_#arguments.selectorID#').html(incGSListTemplate(r));
					mcActivateTooltip($('##incGSGridContainer_#arguments.selectorID#'));

					$('.gsCountSpan').removeClass('d-none');
					$('.gsCountLoadingSpan').addClass('d-none');
					$('.selGSCount').text(r.arrselectedgroupsets ? r.arrselectedgroupsets.length : 0);

					<cfif arguments.hasOrderingAction>
						// Initialize sortable for selected group sets
						if (typeof Sortable !== 'undefined') {
							new Sortable(document.getElementById('incGSGridContainer_#arguments.selectorID#'), {
								animation: 150,
								ghostClass: 'sortable-ghost',
								onEnd: function(evt) {
									if (evt.oldIndex !== evt.newIndex) {
										// Get the useID from the moved element
										let useID = $(evt.item).data('use-id');
										let direction = evt.newIndex > evt.oldIndex ? 'down' : 'up';
										let steps = Math.abs(evt.newIndex - evt.oldIndex);

										// Call move function multiple times if needed
										for (let i = 0; i < steps; i++) {
											moveGroupSetUsageRow_#arguments.selectorID#(useID, direction);
										}
									}
								}
							});
						}
					</cfif>
				} else {
					let reloadHTML = '<div class="text-center mt-5"><span class="d-block text-danger mb-2">Sorry, we were unable to load the data.</span><i class="fa-solid fa-rotate-right fa-2x cursor-pointer" onclick="loadGroupSetGrids_#arguments.selectorID#()"></i><span class="d-block">Reload</span></div>';
					$('##availGSGridContainer_#arguments.selectorID#').html(reloadHTML);
					$('##incGSGridContainer_#arguments.selectorID#').html(reloadHTML);
				}
			};

			$('##availGSGridContainer_#arguments.selectorID#').html(mca_getLoadingHTML());
			$('##incGSGridContainer_#arguments.selectorID#').html(mca_getLoadingHTML());
			<cfif len(arguments.getGroupSetDataFunc)>
				#arguments.getGroupSetDataFunc#(loadGSResult);
			<cfelse>
				getAvailableAndSelectedGroupSetsJSON_#arguments.selectorID#(loadGSResult);
			</cfif>
		}

		function getAvailableAndSelectedGroupSetsJSON_#arguments.selectorID#(onCompleteFunc){
			<cfif len(arguments.getGroupSetDataFunc)>
				#arguments.getGroupSetDataFunc#(onCompleteFunc);
			<cfelse>
				let objParams = {
					siteID: #arguments.siteID#,
					siteResourceID: #arguments.siteResourceID#,
					area: '#arguments.area#'
				};
				TS_AJX('GROUPSETWIDGET','getAvailableAndSelectedGroupSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
			</cfif>
		}

		function addGroupSetUsage_#arguments.selectorID#(gsID) {
			let addGSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadGroupSetGrids_#arguments.selectorID#();
				} else {
					showGSErrorMessage_#arguments.selectorID#('Unable to Add Group Set', r.message || 'We were unable to add this Member Group Set.');
				}
			};

			<cfif len(arguments.addGroupSetUsageFunc)>
				#arguments.addGroupSetUsageFunc#(gsID, addGSResult);
			<cfelse>
				let objParams = {
					siteResourceID: #arguments.siteResourceID#,
					groupSetID: gsID,
					area: '#arguments.area#'
				};
				TS_AJX('GROUPSETWIDGET','createGroupSetUsage',objParams,addGSResult,addGSResult,20000,addGSResult);
			</cfif>
		}

		function removeGroupSetUsage_#arguments.selectorID#(useID) {
			let removeGSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadGroupSetGrids_#arguments.selectorID#();
				} else {
					showGSErrorMessage_#arguments.selectorID#('Unable to Remove Group Set', r.message || 'We were unable to remove this Member Group Set.');
				}
			};

			<cfif len(arguments.removeGroupSetUsageFunc)>
				#arguments.removeGroupSetUsageFunc#(useID, removeGSResult);
			<cfelse>
				let objParams = { useID: useID };
				TS_AJX('GROUPSETWIDGET','gsRemove',objParams,removeGSResult,removeGSResult,20000,removeGSResult);
			</cfif>
		}

		<cfif arguments.hasOrderingAction>
			function moveGroupSetUsageRow_#arguments.selectorID#(useID, dir){
				let moveRowResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadGroupSetGrids_#arguments.selectorID#();
					} else {
						showGSErrorMessage_#arguments.selectorID#('Unable to Move Group Set', r.message || 'We were unable to move this Member Group Set.');
					}
				};
				<cfif len(arguments.orderGroupSetFunc)>
					#arguments.orderGroupSetFunc#(useID, dir, moveRowResult);
				<cfelse>
					let objParams = { useID: useID, dir: dir };
					TS_AJX('GROUPSETWIDGET','gsMove',objParams,moveRowResult,moveRowResult,20000,moveRowResult);
				</cfif>
			}
		</cfif>

		function showGSErrorMessage_#arguments.selectorID#(title, msg){
			MCModalUtils.showModal({
				isslideout: false,
				iframe: false,
				size: 'md',
				title: title,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				strmodalbody: {
					content: msg
				},
				strmodalfooter: {
					showclose: true
				}
			});
		}

		function editGroupSet_#arguments.selectorID#(gsID) {
			var editURL = '#local.editGroupSetLink#&gsID=' + gsID;
			MCModalUtils.showModal(editURL, function() {
				loadGroupSetGrids_#arguments.selectorID#();
			});
		}

		$(function() {
			loadGroupSetGrids_#arguments.selectorID#();
		});
	</script>
	<style>
		##gsGridContainer_#arguments.selectorID# .btn-xs { padding: 0.25rem 0.4rem; }
		##gsGridContainer_#arguments.selectorID# .sortable-ghost { opacity: 0.4; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gsSelectorJS)#">

<cfoutput>
<div class="row no-gutters" id="gsGridContainer_#arguments.selectorID#">
	<div class="col-12">
		<div class="card card-box">
			<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-sm">
					Selected Group Sets
				</div>
				<span class="gsCountSpan small d-none"><span class="selGSCount pr-1"></span>selected</span>
				<span class="gsCountLoadingSpan small">loading..</span>
			</div>
			<div class="card-body p-0">
				<div id="incGSGridContainer_#arguments.selectorID#" style="height:#arguments.selectedGSGridHeight#px;overflow-y:auto;">
				</div>
				<div class="accordion" id="availGSAccordion_#arguments.selectorID#">
					<div class="card card-box rounded-bottom">
						<div class="card-header bg-light rounded-0" id="heading_#arguments.selectorID#">
							<button class="btn btn-link d-flex align-items-center justify-content-between collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#">
								<span class="font-weight-bold font-size-sm">Available Group Sets</span>
								<div class="d-flex align-items-center">
									<a href="##" name="btnCreateGroupSet" onclick="editGroupSet_#arguments.selectorID#(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Group Set" class="mr-2">
										<i class="fa-regular fa-circle-plus fa-lg"></i>
									</a>
									<i class="fa-solid fa-caret-up font-size-xl"></i>
								</div>
							</button>
						</div>
						<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##availGSAccordion_#arguments.selectorID#">
							<div class="card-body bg-secondary p-2" style="height:#arguments.availableGSGridHeight#px;overflow-y:auto;">
								<div id="availGSGridContainer_#arguments.selectorID#"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="err_gsSelector_#arguments.selectorID#" class="alert alert-danger mt-2 d-none" role="alert"></div>

<!-- Handlebars Template for Available Group Sets -->
<script id="mc_AvailGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	<ul class="list-group list-group-flush">
		{{##each arravailablegroupsets}}
		<li class="list-group-item d-flex align-items-center justify-content-between p-2">
			<div class="flex-grow-1">
				<span class="font-weight-bold">{{groupSetName}}</span>
				<small class="d-block text-muted">{{groupCount}} group(s)</small>
			</div>
			<div class="btn-group btn-group-sm ml-2" role="group">
				<button type="button" class="btn btn-outline-primary btn-xs" onclick="addGroupSetUsage_#arguments.selectorID#({{groupSetID}});" data-toggle="tooltip" data-placement="top" title="Add Group Set">
					<i class="fa-regular fa-circle-plus"></i>
				</button>
				<button type="button" class="btn btn-outline-secondary btn-xs" onclick="editGroupSet_#arguments.selectorID#({{groupSetID}});" data-toggle="tooltip" data-placement="top" title="Edit Group Set">
					<i class="fa-regular fa-pen-to-square"></i>
				</button>
			</div>
		</li>
		{{/each}}
		{{##unless arravailablegroupsets.length}}
		<li class="list-group-item text-center text-muted p-3">
			<i class="fa-regular fa-folder-open fa-2x d-block mb-2"></i>
			No available group sets
		</li>
		{{/unless}}
	</ul>
</script>

<!-- Handlebars Template for Selected Group Sets -->
<script id="mc_IncGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	<ul class="list-group list-group-flush">
		{{##each arrselectedgroupsets}}
		<li class="list-group-item d-flex align-items-center justify-content-between p-2" data-use-id="{{useID}}">
			<div class="flex-grow-1">
				<span class="font-weight-bold">{{groupSetName}}</span>
				<small class="d-block text-muted">{{groupCount}} group(s)</small>
			</div>
			<div class="btn-group btn-group-sm ml-2" role="group">
				<cfif arguments.hasOrderingAction>
					<a href="##" id="moveUpGSBtn_#arguments.selectorID#_{{groupSetID}}"
						{{##compare @index '!=' 0}}
							class="btn btn-xs btn-outline-dark"
							onclick="$(this).tooltip('hide');moveGroupSetUsageRow_#arguments.selectorID#({{useID}},'up');return false;"
							data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Up"
						{{/compare}}
						{{##compare @index '==' 0}}
							class="btn btn-xs btn-outline-dark invisible"
						{{/compare}}
						>
						<i class="fa-solid fa-up"></i>
					</a>
					<a href="##" id="moveDownGSBtn_#arguments.selectorID#_{{groupSetID}}"
						{{##compare (math @index "+" 1) '!=' ../arrselectedgroupsets.length}}
							class="btn btn-xs btn-outline-dark"
							onclick="$(this).tooltip('hide');moveGroupSetUsageRow_#arguments.selectorID#({{useID}},'down');return false;"
							data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Down"
						{{/compare}}
						{{##compare (math @index "+" 1) '==' ../arrselectedgroupsets.length}}
							class="btn btn-xs btn-outline-dark invisible"
						{{/compare}}
						>
						<i class="fa-solid fa-down"></i>
					</a>
				</cfif>
				<button type="button" class="btn btn-outline-secondary btn-xs" onclick="editGroupSet_#arguments.selectorID#({{groupSetID}});" data-toggle="tooltip" data-placement="top" title="Edit Group Set">
					<i class="fa-regular fa-pen-to-square"></i>
				</button>
				<button type="button" class="btn btn-outline-danger btn-xs" onclick="removeGroupSetUsage_#arguments.selectorID#({{useID}});" data-toggle="tooltip" data-placement="top" title="Remove Group Set">
					<i class="fa-regular fa-circle-minus"></i>
				</button>
			</div>
		</li>
		{{/each}}
		{{##unless arrselectedgroupsets.length}}
		<li class="list-group-item text-center text-muted p-3">
			<i class="fa-regular fa-folder-open fa-2x d-block mb-2"></i>
			No group sets selected
		</li>
		{{/unless}}
	</ul>
</script>
</cfoutput>
