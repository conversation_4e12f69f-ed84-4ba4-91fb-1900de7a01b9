<cfcomponent output="false">

	<cffunction name="getGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="selectorID" type="string" required="yes" hint="id for hidden input control">
		<cfargument name="selectedValue" type="numeric" required="no" default="0" hint="selected groupSetID value">
		<cfargument name="selectedGroupSetName" type="string" required="no" default="" hint="selected group set name">
		<cfargument name="allowBlankOption" type="boolean" required="no" default="true">
		<cfargument name="inlinePreviewSectionID" type="string" required="no" default="" hint="provide only if selector is within a modal to avoid using modal again for group set preview/edit">
		<cfargument name="qryGroupSets" type="query" required="no" hint="override query">
		<cfargument name="getGroupSetDataFunc" type="string" required="no" default="" hint="override javascript function name that is present on the calling form">
		<cfargument name="usageMode" type="string" required="no" default="gsWidget" hint="override this value to include specific logic at places">
	
		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfif NOT structKeyExists(arguments, "qryGroupSets")>
			<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>
			<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.orgID)>
		<cfelse>
			<cfset local.qryGroupSets = arguments.qryGroupSets>
		</cfif>

		<cfset local.selectedGroupSetID = 0>
		<cfset local.selectedGroupSetLabel = "Choose Group Set">

		<cfif arguments.selectedValue GT 0 OR len(arguments.selectedGroupSetName)>
			<cfquery name="local.qryGroupSetSelected" dbtype="query">
				select groupSetID, groupSetName
				from [local].qryGroupSets
				<cfif arguments.selectedValue GT 0>
					where groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.selectedValue#">
				<cfelse>
					where groupSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.selectedGroupSetName#">
				</cfif>
			</cfquery>

			<cfif local.qryGroupSetSelected.recordCount>
				<cfset local.selectedGroupSetID = local.qryGroupSetSelected.groupSetID>
				<cfset local.selectedGroupSetLabel = local.qryGroupSetSelected.groupSetName>
			</cfif>
		</cfif>

		<cfif len(arguments.inlinePreviewSectionID)>
			<cfset local.useInlinePreview = true>
			<cfset local.mode = "stream">
		<cfelse>
			<cfset local.useInlinePreview = false>
			<cfset local.mode = "direct">
		</cfif>
		
		<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='edit') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=#local.mode#">
		<cfset local.previewGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSets',mca_ta='previewGroupSet') & "&mode=stream">
			
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_single.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMultipleGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="selectorID" type="string" required="yes" hint="id for the selector">
		<cfargument name="siteID" type="numeric" required="yes" hint="site ID for the selector">
		<cfargument name="orgID" type="numeric" required="yes" hint="organization ID for group set filtering">
		<cfargument name="selectedGroupSetIDs" type="string" required="no" default="" hint="comma-separated list of selected group set IDs">
		<cfargument name="getGroupSetDataFunc" type="string" required="no" default="" hint="javascript function name for getting data">
		<cfargument name="addGroupSetFunc" type="string" required="no" default="" hint="javascript function name for adding group set">
		<cfargument name="removeGroupSetFunc" type="string" required="no" default="" hint="javascript function name for removing group set">
		<cfargument name="orderGroupSetFunc" type="string" required="no" default="" hint="javascript function name for ordering group sets">
		<cfargument name="hasPermissionAction" type="boolean" required="no" default="true">
		<cfargument name="hasOrderingAction" type="boolean" required="no" default="true">
		<cfargument name="hasEditRights" type="boolean" required="no" default="true">
		<cfargument name="selectedGSGridHeight" type="numeric" required="no" default="200">
		<cfargument name="availableGSGridHeight" type="numeric" required="no" default="300">
		<cfargument name="usageMode" type="string" required="no" default="gsWidgetMultiple">

		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSetAdmin',mca_ta='editGroupSet') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=stream">
		
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_multiple.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<!--- AJAX Methods --->
	<cffunction name="getGroupSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>

		<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.mcproxy_orgID)>

		<cfset local.returnStruct = {
			success = true,
			arravailablegroupsets = []
		}>

		<cftry>
			<cfloop query="local.qryGroupSets">
				<cfset arrayAppend(local.returnStruct.arravailablegroupsets, {
					groupsetid = local.qryGroupSets.groupSetID,
					groupsetname = local.qryGroupSets.groupSetName,
					isBeingUsed = (local.qryGroupSets.GroupSetBeingUsed GT 0)
				})>
			</cfloop>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.message = "Error retrieving group sets: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>
