<cfcomponent output="false">

	<cffunction name="getGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="selectorID" type="string" required="yes" hint="id for hidden input control">
		<cfargument name="selectedValue" type="numeric" required="no" default="0" hint="selected groupSetID value">
		<cfargument name="selectedGroupSetName" type="string" required="no" default="" hint="selected group set name">
		<cfargument name="allowBlankOption" type="boolean" required="no" default="true">
		<cfargument name="inlinePreviewSectionID" type="string" required="no" default="" hint="provide only if selector is within a modal to avoid using modal again for group set preview/edit">
		<cfargument name="qryGroupSets" type="query" required="no" hint="override query">
		<cfargument name="getGroupSetDataFunc" type="string" required="no" default="" hint="override javascript function name that is present on the calling form">
		<cfargument name="usageMode" type="string" required="no" default="gsWidget" hint="override this value to include specific logic at places">
	
		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfif NOT structKeyExists(arguments, "qryGroupSets")>
			<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>
			<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=local.objAdmin.getOrgIDFromSiteID(arguments.siteID))>
		<cfelse>
			<cfset local.qryGroupSets = arguments.qryGroupSets>
		</cfif>

		<cfset local.selectedGroupSetID = 0>
		<cfset local.selectedGroupSetLabel = "Choose Group Set">

		<cfif arguments.selectedValue GT 0 OR len(arguments.selectedGroupSetName)>
			<cfquery name="local.qryGroupSetSelected" dbtype="query">
				select groupSetID, groupSetName
				from [local].qryGroupSets
				<cfif arguments.selectedValue GT 0>
					where groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.selectedValue#">
				<cfelse>
					where groupSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.selectedGroupSetName#">
				</cfif>
			</cfquery>

			<cfif local.qryGroupSetSelected.recordCount>
				<cfset local.selectedGroupSetID = local.qryGroupSetSelected.groupSetID>
				<cfset local.selectedGroupSetLabel = local.qryGroupSetSelected.groupSetName>
			</cfif>
		</cfif>

		<cfif len(arguments.inlinePreviewSectionID)>
			<cfset local.useInlinePreview = true>
			<cfset local.mode = "stream">
		<cfelse>
			<cfset local.useInlinePreview = false>
			<cfset local.mode = "direct">
		</cfif>
		
		<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSetAdmin',mca_ta='editGroupSet') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=#local.mode#">
		<cfset local.previewGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSetAdmin',mca_ta='previewGroupSet') & "&mode=stream">
			
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_single.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMultipleGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="selectorID" type="string" required="yes" hint="id for the selector">
		<cfargument name="getGroupSetDataFunc" type="string" required="no" default="" hint="javascript function name for getting data">
		<cfargument name="addGroupSetUsageFunc" type="string" required="no" default="" hint="javascript function name for adding usage">
		<cfargument name="removeGroupSetUsageFunc" type="string" required="no" default="" hint="javascript function name for removing usage">
		<cfargument name="hasPermissionAction" type="boolean" required="no" default="true">
		<cfargument name="hasOrderingAction" type="boolean" required="no" default="true">
		<cfargument name="hasEditRights" type="boolean" required="no" default="true">
		<cfargument name="selectedGSGridHeight" type="numeric" required="no" default="200">
		<cfargument name="availableGSGridHeight" type="numeric" required="no" default="300">
		<cfargument name="usageMode" type="string" required="no" default="gsWidgetMultiple">

		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSetAdmin',mca_ta='editGroupSet') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=stream">
		
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_multiple.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<!--- AJAX Methods --->
	<cffunction name="getGroupSetsJSON" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>
		
		<cfset local.orgID = local.objAdmin.getOrgIDFromSiteID(arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=local.orgID)>
		
		<cfset local.result = {
			success = "true",
			groupsets = []
		}>
		
		<cfloop query="local.qryGroupSets">
			<cfset arrayAppend(local.result.groupsets, {
				groupSetID = local.qryGroupSets.groupSetID,
				groupSetName = local.qryGroupSets.groupSetName,
				groupCount = local.qryGroupSets.groupCount,
				isBeingUsed = (local.qryGroupSets.GroupSetBeingUsed GT 0)
			})>
		</cfloop>
		
		<cfreturn serializeJSON(local.result)>
	</cffunction>

	<cffunction name="createGroupSet" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>
		
		<cfset local.orgID = local.objAdmin.getOrgIDFromSiteID(arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.groupSetName = arguments.event.getTrimValue('groupSetName', '')>
		
		<cfset local.result = {
			success = "false",
			message = "",
			groupSetID = 0
		}>
		
		<cftry>
			<cfif len(local.groupSetName)>
				<cfquery name="local.qryCreateGroupSet" datasource="#application.dsn.membercentral.dsn#">
					EXEC dbo.ams_createMemberGroupSet 
						@orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">,
						@groupSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.groupSetName#">
				</cfquery>
				
				<cfif local.qryCreateGroupSet.recordCount AND local.qryCreateGroupSet.groupSetID GT 0>
					<cfset local.result.success = "true">
					<cfset local.result.groupSetID = local.qryCreateGroupSet.groupSetID>
					<cfset local.result.message = "Group set created successfully">
				<cfelse>
					<cfset local.result.message = "Failed to create group set">
				</cfif>
			<cfelse>
				<cfset local.result.message = "Group set name is required">
			</cfif>
			
			<cfcatch>
				<cfset local.result.message = "Error creating group set: #cfcatch.message#">
			</cfcatch>
		</cftry>
		
		<cfreturn serializeJSON(local.result)>
	</cffunction>

	<cffunction name="validateGroupSetUsage" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.groupSetID = val(arguments.event.getValue('groupSetID', 0))>
		
		<cfset local.result = {
			success = "true",
			canDelete = true,
			usageCount = 0,
			usageDetails = []
		}>
		
		<cftry>
			<cfquery name="local.qryUsage" datasource="#application.dsn.membercentral.dsn#">
				SELECT COUNT(*) as usageCount
				FROM dbo.ams_memberDirectoryClassifications
				WHERE groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.groupSetID#">
			</cfquery>
			
			<cfset local.result.usageCount = local.qryUsage.usageCount>
			<cfset local.result.canDelete = (local.qryUsage.usageCount EQ 0)>
			
			<cfif local.qryUsage.usageCount GT 0>
				<cfset arrayAppend(local.result.usageDetails, "Used in #local.qryUsage.usageCount# directory classification(s)")>
			</cfif>
			
			<cfcatch>
				<cfset local.result.success = "false">
				<cfset local.result.message = "Error checking group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>
		
		<cfreturn serializeJSON(local.result)>
	</cffunction>

	<cffunction name="getAvailableAndSelectedGroupSetsJSON" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>

		<cfset local.orgID = local.objAdmin.getOrgIDFromSiteID(arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.siteResourceID = val(arguments.event.getValue('siteResourceID', 0))>
		<cfset local.area = arguments.event.getTrimValue('area', '')>

		<cfset local.result = {
			success = "true",
			availablegroupsets = [],
			selectedgroupsets = []
		}>

		<cftry>
			<!--- Get all available group sets --->
			<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=local.orgID)>

			<cfloop query="local.qryGroupSets">
				<cfset arrayAppend(local.result.availablegroupsets, {
					groupSetID = local.qryGroupSets.groupSetID,
					groupSetName = local.qryGroupSets.groupSetName,
					groupCount = local.qryGroupSets.groupCount,
					isBeingUsed = (local.qryGroupSets.GroupSetBeingUsed GT 0)
				})>
			</cfloop>

			<!--- TODO: Implement selected group sets logic based on usage context --->
			<!--- This would require additional parameters and usage tracking tables --->

			<cfcatch>
				<cfset local.result.success = "false">
				<cfset local.result.message = "Error retrieving group sets: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.result)>
	</cffunction>

	<cffunction name="createGroupSetUsage" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.groupSetID = val(arguments.event.getValue('groupSetID', 0))>
		<cfset local.siteResourceID = val(arguments.event.getValue('siteResourceID', 0))>
		<cfset local.area = arguments.event.getTrimValue('area', '')>

		<cfset local.result = {
			success = "false",
			message = "",
			useID = 0
		}>

		<cftry>
			<!--- TODO: Implement group set usage creation logic --->
			<!--- This would require a usage tracking table similar to ams_memberFieldUsage --->
			<cfset local.result.message = "Group set usage creation not yet implemented">

			<cfcatch>
				<cfset local.result.message = "Error creating group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.result)>
	</cffunction>

	<cffunction name="gsRemove" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.useID = val(arguments.event.getValue('useID', 0))>

		<cfset local.result = {
			success = "false",
			message = ""
		}>

		<cftry>
			<!--- TODO: Implement group set usage removal logic --->
			<cfset local.result.message = "Group set usage removal not yet implemented">

			<cfcatch>
				<cfset local.result.message = "Error removing group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.result)>
	</cffunction>

	<cffunction name="gsMove" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.useID = val(arguments.event.getValue('useID', 0))>
		<cfset local.direction = arguments.event.getTrimValue('dir', '')>

		<cfset local.result = {
			success = "false",
			message = ""
		}>

		<cftry>
			<!--- TODO: Implement group set usage ordering logic --->
			<cfset local.result.message = "Group set usage ordering not yet implemented">

			<cfcatch>
				<cfset local.result.message = "Error moving group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.result)>
	</cffunction>

</cfcomponent>
