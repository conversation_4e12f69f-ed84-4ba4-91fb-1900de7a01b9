<cfcomponent output="false">

	<cffunction name="getGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="selectorID" type="string" required="yes" hint="id for hidden input control">
		<cfargument name="selectedValue" type="numeric" required="no" default="0" hint="selected groupSetID value">
		<cfargument name="selectedGroupSetName" type="string" required="no" default="" hint="selected group set name">
		<cfargument name="allowBlankOption" type="boolean" required="no" default="true">
		<cfargument name="inlinePreviewSectionID" type="string" required="no" default="" hint="provide only if selector is within a modal to avoid using modal again for group set preview/edit">
		<cfargument name="qryGroupSets" type="query" required="no" hint="override query">
		<cfargument name="getGroupSetDataFunc" type="string" required="no" default="" hint="override javascript function name that is present on the calling form">
		<cfargument name="usageMode" type="string" required="no" default="gsWidget" hint="override this value to include specific logic at places">
	
		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfif NOT structKeyExists(arguments, "qryGroupSets")>
			<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>
			<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.orgID)>
		<cfelse>
			<cfset local.qryGroupSets = arguments.qryGroupSets>
		</cfif>

		<cfset local.selectedGroupSetID = 0>
		<cfset local.selectedGroupSetLabel = "Choose Group Set">

		<cfif arguments.selectedValue GT 0 OR len(arguments.selectedGroupSetName)>
			<cfquery name="local.qryGroupSetSelected" dbtype="query">
				select groupSetID, groupSetName
				from [local].qryGroupSets
				<cfif arguments.selectedValue GT 0>
					where groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.selectedValue#">
				<cfelse>
					where groupSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.selectedGroupSetName#">
				</cfif>
			</cfquery>

			<cfif local.qryGroupSetSelected.recordCount>
				<cfset local.selectedGroupSetID = local.qryGroupSetSelected.groupSetID>
				<cfset local.selectedGroupSetLabel = local.qryGroupSetSelected.groupSetName>
			</cfif>
		</cfif>

		<cfif len(arguments.inlinePreviewSectionID)>
			<cfset local.useInlinePreview = true>
			<cfset local.mode = "stream">
		<cfelse>
			<cfset local.useInlinePreview = false>
			<cfset local.mode = "direct">
		</cfif>
		
		<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSetAdmin',mca_ta='editGroupSet') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=#local.mode#">
		<cfset local.previewGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSetAdmin',mca_ta='previewGroupSet') & "&mode=stream">
			
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_single.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMultipleGroupSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="selectorID" type="string" required="yes" hint="id for the selector">
		<cfargument name="siteID" type="numeric" required="yes" hint="site ID for the selector">
		<cfargument name="siteResourceID" type="numeric" required="yes" hint="site resource ID for usage tracking">
		<cfargument name="area" type="string" required="no" default="" hint="area for usage tracking">
		<cfargument name="getGroupSetDataFunc" type="string" required="no" default="" hint="javascript function name for getting data">
		<cfargument name="addGroupSetUsageFunc" type="string" required="no" default="" hint="javascript function name for adding usage">
		<cfargument name="removeGroupSetUsageFunc" type="string" required="no" default="" hint="javascript function name for removing usage">
		<cfargument name="orderGroupSetFunc" type="string" required="no" default="" hint="javascript function name for ordering usage">
		<cfargument name="hasPermissionAction" type="boolean" required="no" default="true">
		<cfargument name="hasOrderingAction" type="boolean" required="no" default="true">
		<cfargument name="hasEditRights" type="boolean" required="no" default="true">
		<cfargument name="selectedGSGridHeight" type="numeric" required="no" default="200">
		<cfargument name="availableGSGridHeight" type="numeric" required="no" default="300">
		<cfargument name="usageMode" type="string" required="no" default="gsWidgetMultiple">

		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfset local.editGroupSetLink = local.objAdmin.buildLinkToTool(toolType='MemberGroupSetAdmin',mca_ta='editGroupSet') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=stream">
		
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_groupsets_multiple.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<!--- AJAX Methods --->
	<cffunction name="getGroupSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>

		<cfset local.qryGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.mcproxy_orgID)>

		<cfset local.returnStruct = {
			success = true,
			arravailablegroupsets = []
		}>

		<cftry>
			<cfloop query="local.qryGroupSets">
				<cfset arrayAppend(local.returnStruct.arravailablegroupsets, {
					groupsetid = local.qryGroupSets.groupSetID,
					groupsetname = local.qryGroupSets.groupSetName,
					isBeingUsed = (local.qryGroupSets.GroupSetBeingUsed GT 0)
				})>
			</cfloop>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.message = "Error retrieving group sets: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_groupSetName" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>

		<cfset local.returnStruct = {
			success = false,
			message = "",
			groupSetID = 0
		}>

		<cftry>
			<cfif len(trim(arguments.mcproxy_groupSetName))>
				<cfquery name="local.qryCreateGroupSet" datasource="#application.dsn.membercentral.dsn#">
					EXEC dbo.ams_createMemberGroupSet
						@orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
						@groupSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.mcproxy_groupSetName)#">
				</cfquery>

				<cfif local.qryCreateGroupSet.recordCount AND local.qryCreateGroupSet.groupSetID GT 0>
					<cfset local.returnStruct.success = true>
					<cfset local.returnStruct.groupSetID = local.qryCreateGroupSet.groupSetID>
					<cfset local.returnStruct.message = "Group set created successfully">
				<cfelse>
					<cfset local.returnStruct.message = "Failed to create group set">
				</cfif>
			<cfelse>
				<cfset local.returnStruct.message = "Group set name is required">
			</cfif>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.message = "Error creating group set: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="validateGroupSetUsage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_groupSetID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.returnStruct = {
			success = true,
			canDelete = true,
			usageCount = 0,
			usageDetails = []
		}>

		<cftry>
			<!--- Check usage in directory classifications --->
			<cfquery name="local.qryDirectoryUsage" datasource="#application.dsn.membercentral.dsn#">
				SELECT COUNT(*) as usageCount
				FROM dbo.ams_memberDirectoryClassifications
				WHERE groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_groupSetID#">
			</cfquery>

			<!--- Check usage in group set usage table --->
			<cfquery name="local.qryGroupSetUsage" datasource="#application.dsn.membercentral.dsn#">
				SELECT COUNT(*) as usageCount
				FROM dbo.ams_memberGroupSetUsage
				WHERE groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_groupSetID#">
			</cfquery>

			<cfset local.totalUsage = local.qryDirectoryUsage.usageCount + local.qryGroupSetUsage.usageCount>
			<cfset local.returnStruct.usageCount = local.totalUsage>
			<cfset local.returnStruct.canDelete = (local.totalUsage EQ 0)>

			<cfif local.qryDirectoryUsage.usageCount GT 0>
				<cfset arrayAppend(local.returnStruct.usageDetails, "Used in #local.qryDirectoryUsage.usageCount# directory classification(s)")>
			</cfif>

			<cfif local.qryGroupSetUsage.usageCount GT 0>
				<cfset arrayAppend(local.returnStruct.usageDetails, "Used in #local.qryGroupSetUsage.usageCount# group set selector(s)")>
			</cfif>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.message = "Error checking group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAvailableAndSelectedGroupSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteResourceID" type="numeric" required="true">
		<cfargument name="mcproxy_area" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>

		<cfset local.returnStruct = {
			success = true,
			arravailablegroupsets = [],
			arrselectedgroupsets = []
		}>

		<cftry>
			<!--- Get all available group sets for the organization --->
			<cfset local.qryAllGroupSets = local.objMemberGroupSets.getGroupSets(orgID=arguments.mcproxy_orgID)>

			<!--- Get currently selected group sets for this resource/area --->
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qrySelectedGroupSets">
				SELECT mgsu.useID, mgsu.groupSetID, mgsu.groupSetOrder, mgs.groupSetName
				FROM dbo.ams_memberGroupSetUsage mgsu
				INNER JOIN dbo.ams_memberGroupSets mgs ON mgs.groupSetID = mgsu.groupSetID
				WHERE mgsu.siteResourceID = <cfqueryparam value="#arguments.mcproxy_siteResourceID#" cfsqltype="cf_sql_integer">
				AND ISNULL(mgsu.area,'') = <cfqueryparam value="#arguments.mcproxy_area#" cfsqltype="cf_sql_varchar">
				ORDER BY mgsu.groupSetOrder
			</cfquery>

			<!--- Build selected group sets array --->
			<cfloop query="local.qrySelectedGroupSets">
				<cfset arrayAppend(local.returnStruct.arrselectedgroupsets, {
					useID = local.qrySelectedGroupSets.useID,
					groupsetid = local.qrySelectedGroupSets.groupSetID,
					groupsetname = local.qrySelectedGroupSets.groupSetName,
					groupSetOrder = local.qrySelectedGroupSets.groupSetOrder
				})>
			</cfloop>

			<!--- Build available group sets array (exclude already selected ones) --->
			<cfloop query="local.qryAllGroupSets">
				<cfset local.isSelected = false>
				<cfloop query="local.qrySelectedGroupSets">
					<cfif local.qrySelectedGroupSets.groupSetID EQ local.qryAllGroupSets.groupSetID>
						<cfset local.isSelected = true>
						<cfbreak>
					</cfif>
				</cfloop>

				<cfif NOT local.isSelected>
					<cfset arrayAppend(local.returnStruct.arravailablegroupsets, {
						groupsetid = local.qryAllGroupSets.groupSetID,
						groupsetname = local.qryAllGroupSets.groupSetName
					})>
				</cfif>
			</cfloop>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.message = "Error retrieving group sets: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createGroupSetUsage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteResourceID" type="numeric" required="true">
		<cfargument name="mcproxy_groupSetID" type="numeric" required="true">
		<cfargument name="mcproxy_area" type="string" required="false" default="">

		<cfset var local = structNew()>

		<cfset local.returnStruct = {
			success = false,
			message = "",
			useID = 0
		}>

		<cftry>
			<!--- Create group set usage using stored procedure --->
			<cfquery name="local.qryCreateUsage" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.ams_createMemberGroupSetUsage
					@siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteResourceID#">,
					@groupSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_groupSetID#">,
					@area = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_area#">,
					@createSiteResourceID = 0
			</cfquery>

			<cfif local.qryCreateUsage.recordCount AND local.qryCreateUsage.useID GT 0>
				<cfset local.returnStruct.success = true>
				<cfset local.returnStruct.useID = local.qryCreateUsage.useID>
				<cfset local.returnStruct.message = "Group set usage created successfully">
			<cfelse>
				<cfset local.returnStruct.message = "Failed to create group set usage">
			</cfif>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.message = "Error creating group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="gsRemove" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_useID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.returnStruct = {
			success = false,
			message = ""
		}>

		<cftry>
			<!--- Remove group set usage using stored procedure --->
			<cfquery name="local.qryRemoveUsage" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.ams_removeMemberGroupSetUsage
					@useID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_useID#">
			</cfquery>

			<cfset local.returnStruct.success = true>
			<cfset local.returnStruct.message = "Group set usage removed successfully">

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.message = "Error removing group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="gsMove" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_useID" type="numeric" required="true">
		<cfargument name="mcproxy_dir" type="string" required="true">

		<cfset var local = structNew()>

		<cfset local.returnStruct = {
			success = false,
			message = ""
		}>

		<cftry>
			<!--- Move group set usage using stored procedure --->
			<cfquery name="local.qryMoveUsage" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.ams_moveMemberGroupSetUsage
					@useID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_useID#">,
					@dir = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_dir#">
			</cfquery>

			<cfset local.returnStruct.success = true>
			<cfset local.returnStruct.message = "Group set usage moved successfully">

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.returnStruct.message = "Error moving group set usage: #cfcatch.message#">
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>
