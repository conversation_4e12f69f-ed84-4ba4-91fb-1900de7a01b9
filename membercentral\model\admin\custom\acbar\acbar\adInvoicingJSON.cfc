<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getInsertionOrders" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"invoicenumber")>
		<cfset arrayAppend(local.arrCols,"advertiserName")>
		<cfset arrayAppend(local.arrCols,"PLJNumber")>
		<cfset arrayAppend(local.arrCols,"amountBilled")>
		<cfset arrayAppend(local.arrCols,"amountDue")>
		<cfset arrayAppend(local.arrCols,"createdDate")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryOrders" datasource="#application.dsn.customApps.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpOrders') IS NOT NULL
					DROP TABLE ##tmpOrders;
				IF OBJECT_ID('tempdb..##tmpOrders2') IS NOT NULL
					DROP TABLE ##tmpOrders2;
				IF OBJECT_ID('tempdb..##tmpOrdersTotal') IS NOT NULL
					DROP TABLE ##tmpOrdersTotal;
				CREATE TABLE ##tmpOrders (orderID int, PLJNumber varchar(200), advertiserMemberID int, referenceInfo varchar(500), 
					invoiceID int, enteredByMemberID int, createdDate datetime, advertiserName varchar(400), advertiserCompany varchar(100), 
					enteredByMember varchar(400), enteredByMemberOrgID int, rateCardName varchar(250), configName varchar(250), runs int, 
					pricePerRun decimal(18,2), allowPriceOverride bit);
				CREATE TABLE ##tmpOrders2 (orderID int, PLJNumber varchar(200), advertiserMemberID int, referenceInfo varchar(500), 
					invoiceID int, invoiceNumber varchar(20), enteredByMemberID int, createdDate datetime, amountBilled decimal(18,2), 
					amountDue decimal(18,2), advertiserName varchar(400), advertiserCompany varchar(100), enteredByMember varchar(400), 
					enteredByMemberOrgID int, rateCardName varchar(250), configName varchar(250), runs int, pricePerRun decimal(18,2), 
					allowPriceOverride bit, row int);
				CREATE TABLE ##tmpOrdersTotal (orderID int, totalAmountBilled decimal(18,2), amountPaid decimal(18,2));

				DECLARE @orgID int, @totalCount int, @posStart int, @posStartPlusCount int;
				SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('ACBAR');
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				
				INSERT INTO ##tmpOrders (orderID, PLJNumber, advertiserMemberID, referenceInfo, invoiceID, enteredByMemberID,
					enteredByMember, enteredByMemberOrgID, advertiserName, advertiserCompany, createdDate, rateCardName, configName, runs,
					pricePerRun, allowPriceOverride)
				SELECT o.orderID, o.PLJNumber, advMActive.memberID AS advertiserMemberID, o.referenceInfo, o.invoiceID, o.enteredByMemberID,
					mActive.lastName + ', ' + mActive.firstName AS enteredByMember, mActive.orgID, 
					advMActive.lastName + ', ' + advMActive.firstName + ' (' + advMActive.memberNumber + ')' AS advertiserName,
					advMActive.company, o.createdDate, c.rateCardName, cc.configName, cc.runs, cc.pricePerRun, cc.allowPriceOverride
				FROM dbo.ACBAR_AdInvoicing_insertionOrders AS o
				INNER JOIN dbo.ACBAR_AdInvoicing_rateCardConfigs AS cc ON cc.rateCardConfigID = o.rateCardConfigID
					<cfif val(arguments.event.getTrimValue('fRateCardID',0))>
						AND cc.rateCardID = <cfqueryparam value="#arguments.event.getValue('fRateCardID')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
				INNER JOIN dbo.ACBAR_AdInvoicing_rateCards AS c ON c.rateCardID = cc.rateCardID
				INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID IN (@orgID,1)
					AND m.memberID = o.enteredByMemberID
				INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.orgID IN (@orgID,1)
					AND mActive.memberID = m.activeMemberID
				INNER JOIN memberCentral.dbo.ams_members AS advM ON advM.orgID = @orgID
					AND advM.memberID = o.advertiserMemberID
				INNER JOIN memberCentral.dbo.ams_members AS advMActive ON advMActive.orgID = @orgID
					AND advMActive.memberID = advM.activeMemberID
					<cfif arguments.event.getValue('fLinkedMemberID',0) gt 0>
						AND advMActive.memberID = <cfqueryparam value="#arguments.event.getValue('fLinkedMemberID')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
				<cfif arguments.event.getValue('fLinkedGroupID',0) gt 0>
					INNER JOIN memberCentral.dbo.cache_members_groups AS mgLinked ON mgLinked.memberID = advMActive.memberID
						AND mgLinked.groupID = <cfqueryparam value="#arguments.event.getValue('fLinkedGroupID')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				WHERE 1 = 1
				<cfif len(arguments.event.getValue('fPLJNumber',''))>
					AND o.PLJNumber LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('fPLJNumber')#%">
				</cfif>
				<cfif val(arguments.event.getTrimValue('fRateCardConfigID',0))>
					AND o.rateCardConfigID = <cfqueryparam value="#arguments.event.getValue('fRateCardConfigID')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif len(arguments.event.getValue('fCreatedDateStart',''))>
					AND o.createdDate >= <cfqueryparam value="#arguments.event.getValue('fCreatedDateStart')#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(arguments.event.getValue('fCreatedDateEnd',''))>
					AND o.createdDate < <cfqueryparam value="#dateAdd('d',1,arguments.event.getValue('fCreatedDateEnd'))#" cfsqltype="CF_SQL_DATE">
				</cfif>;

				EXEC dbo.acbar_adInvoicing_ordersAmountAndPaid;

				INSERT INTO ##tmpOrders2 (orderID, PLJNumber, advertiserMemberID, referenceInfo, invoiceID, invoiceNumber, enteredByMemberID,
					enteredByMember, enteredByMemberOrgID, advertiserName, advertiserCompany, createdDate, rateCardName, configName, runs,
					pricePerRun, amountBilled, amountDue, allowPriceOverride, row)
				SELECT orderID, PLJNumber, advertiserMemberID, referenceInfo, invoiceID, invoiceNumber, enteredByMemberID, enteredByMember,
					enteredByMemberOrgID, advertiserName, advertiserCompany, createdDate, rateCardName, configName, runs, pricePerRun, amountBilled, amountDue,
					allowPriceOverride, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#)
				FROM (
					SELECT o.orderID, o.PLJNumber, o.advertiserMemberID, o.referenceInfo, o.invoiceID, o.enteredByMemberID, 
						o.enteredByMember, o.enteredByMemberOrgID, o.advertiserName, o.advertiserCompany, o.createdDate, o.rateCardName, o.configName, 
						o.runs, o.pricePerRun, o.allowPriceOverride, ot.totalAmountBilled AS amountBilled, 
						ot.totalAmountBilled - ot.amountPaid AS amountDue,
						'ACBAR' + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber
					FROM ##tmpOrders as o
					INNER JOIN membercentral.dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = o.invoiceID
					LEFT OUTER JOIN ##tmpOrdersTotal as ot on ot.orderID = o.orderID
					WHERE 1 = 1
					<cfif len(arguments.event.getValue('fBilledAmtStart',''))>
						AND ISNULL(ot.totalAmountBilled,0) >= <cfqueryparam value="#rereplace(arguments.event.getValue('fBilledAmtStart',''),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(arguments.event.getValue('fBilledAmtEnd',''))>
						AND ISNULL(ot.totalAmountBilled,0) <= <cfqueryparam value="#rereplace(arguments.event.getValue('fBilledAmtEnd',''),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(arguments.event.getValue('fDueAmtStart',''))>
						AND (ISNULL(ot.totalAmountBilled,0)-ISNULL(ot.amountPaid,0)) >= <cfqueryparam value="#rereplace(arguments.event.getValue('fDueAmtStart',''),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(arguments.event.getValue('fDueAmtEnd',''))>
						AND (ISNULL(ot.totalAmountBilled,0)-ISNULL(ot.amountPaid,0)) <= <cfqueryparam value="#rereplace(arguments.event.getValue('fDueAmtEnd',''),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
				) AS tmp;

				SET @totalCount = @@ROWCOUNT;

				SELECT tmp.orderID, tmp.PLJNumber, tmp.advertiserMemberID, tmp.advertiserName, tmp.advertiserCompany, tmp.referenceInfo, tmp.invoiceID,
					tmp.invoiceNumber, tmp.enteredByMemberID, tmp.enteredByMember, tmp.enteredByMemberOrgID, tmp.createdDate, tmp.rateCardName, tmp.configName,
					tmp.runs, tmp.pricePerRun, tmp.amountBilled, tmp.amountDue, tmp.allowPriceOverride, o.runDatesXML,
					@totalCount AS totalCount
				FROM ##tmpOrders2 AS tmp
				INNER JOIN dbo.ACBAR_AdInvoicing_insertionOrders AS o ON o.orderID = tmp.orderID
				WHERE tmp.row > @posStart
				AND tmp.row <= @posStartPlusCount
				ORDER BY tmp.row;

				IF OBJECT_ID('tempdb..##tmpOrders') IS NOT NULL
					DROP TABLE ##tmpOrders;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryOrders">
			<cfset local.runDateXML = xmlParse(local.qryOrders.runDatesXML)>
			<cfset local.arrRowNodes = local.runDateXML.xmlRoot.xmlChildren>
			<cfset var allowPriceOverride = local.qryOrders.allowPriceOverride>
			<cfset local.arrData.append({
				"orderID": "ACBAR#right('00000' & local.qryOrders.orderID,5)#",
				"invoicenumber": local.qryOrders.invoiceNumber,
				"PLJNumber": local.qryOrders.PLJNumber,
				"amountBilled": dollarFormat(local.qryOrders.amountBilled),
				"amountDue": dollarFormat(local.qryOrders.amountDue),
				"advertiserMemberID": local.qryOrders.advertiserMemberID,
				"advertiserName": local.qryOrders.advertiserName,
				"advertiserCompany": local.qryOrders.advertiserCompany,
				"createdDate": DateTimeFormat(local.qryOrders.createdDate,"m/d/yy h:NN tt") & " CT",
				"enteredByMemberID": local.qryOrders.enteredByMemberID,
				"enteredByMember": local.qryOrders.enteredByMember,
				"enteredByMemberOrgID": local.qryOrders.enteredByMemberOrgID,
				"rateCardName": local.qryOrders.rateCardName,
				"configName": local.qryOrders.configName,
				"allowPriceOverride": allowPriceOverride,
				"actualRuns": arrayLen(local.arrRowNodes),
				"pricePerRun": dollarFormat(local.qryOrders.pricePerRun),
				"strRunDates": arrayToList(
					arrayMap(local.arrRowNodes, (xmlChild) => { 
						return dateFormat(xmlChild.xmlAttributes.date, 'm/d/yy');
					}),
					'|'
				),
				"DT_RowId": "ord_#local.qryOrders.orderID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryOrders.totalcount),
			"recordsFiltered":  val(local.qryOrders.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRateCards" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfquery datasource="#application.dsn.customApps.dsn#" name="local.qryRateCards">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				DECLARE @totalCount int;

				IF OBJECT_ID('tempdb..##tblRateCards') IS NOT NULL 
					DROP TABLE ##tblRateCards;
				CREATE TABLE ##tblRateCards (rateCardID int, rateCardName varchar(250), createdDate datetime,
					rateCardConfigID int, configName varchar(250), runs int, pricePerRun decimal(18,2),
					configCreatedDate datetime, configUsageCount int, row int);

				INSERT INTO ##tblRateCards (rateCardID, rateCardName, createdDate, rateCardConfigID, configName,
					runs, pricePerRun, configCreatedDate, configUsageCount, row)
				SELECT c.rateCardID, c.rateCardName, c.createdDate, cc.rateCardConfigID, cc.configName, cc.runs,
					cc.pricePerRun, cc.createdDate, COUNT(o.orderID),
					ROW_NUMBER() OVER (ORDER BY c.rateCardName, cc.configName) AS row
				FROM dbo.ACBAR_AdInvoicing_rateCards AS c
				LEFT OUTER JOIN dbo.ACBAR_AdInvoicing_rateCardConfigs AS cc ON cc.rateCardID = c.rateCardID
				LEFT OUTER JOIN dbo.ACBAR_AdInvoicing_insertionOrders AS o ON o.rateCardConfigID = cc.rateCardConfigID
				<cfif len(arguments.event.getTrimValue('fRateCardName',''))>
					WHERE c.rateCardName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('fRateCardName')#%">
				</cfif>
				GROUP BY c.rateCardID, c.rateCardName, c.createdDate, cc.rateCardConfigID, cc.configName,
					cc.runs, cc.pricePerRun, cc.createdDate;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT rateCardID, rateCardName, createdDate, rateCardConfigID, configName,
					runs, pricePerRun, configCreatedDate, configUsageCount, @totalCount as totalCount
				FROM ##tblRateCards
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tblRateCards') IS NOT NULL 
					DROP TABLE ##tblRateCards;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrEntries = []>
		<cfoutput query="local.qryRateCards" group="rateCardID">
			<cfset local.rateCardRowID = "rc#local.qryRateCards.rateCardID#">

			<cfset local.objRateCard = {
				"level": 1,
				"rowType": "rateCard",
				"rateCardID": local.qryRateCards.rateCardID,
				"displayName": local.qryRateCards.rateCardName,
				"createdDate": DateFormat(local.qryRateCards.createdDate,"m/d/yyyy"),
				"rateCardUsageCount": 0,
				"hasChildren": 0,
				"parentRowID": "gridRoot",
				"DT_RowId": local.rateCardRowID,
				"DT_RowClass": "child-of-gridRoot"
			}>
			<cfset local.arrEntries.append(local.objRateCard)>

			<cfset local.configIndex = 0>
			<cfset local.rateCardUsageCount = 0>
			<cfset local.rateParentRowID = local.rateCardRowID>
				
			<cfoutput>
				<cfif val(local.qryRateCards.rateCardConfigID) gt 0>
					<cfset local.configIndex++>
					
					<cfset local.configRowID = "#local.rateCardRowID#-#local.qryRateCards.rateCardConfigID#">

					<cfset local.objConfig = {
						"level": 2,
						"rowType": "rateCardConfig",
						"rateCardID": local.qryRateCards.rateCardID,
						"rateCardConfigID": val(local.qryRateCards.rateCardConfigID),
						"displayName": local.qryRateCards.configName,
						"createdDate": DateFormat(local.qryRateCards.configCreatedDate,"m/d/yyyy"),
						"runs": local.qryRateCards.runs,
						"pricePerRun": dollarFormat(local.qryRateCards.pricePerRun),
						"configUsageCount": local.qryRateCards.configUsageCount,
						"parentRowID": local.rateParentRowID,
						"DT_RowId": local.configRowID,
						"DT_RowClass": "child-of-#local.rateParentRowID#"
					}>
					<cfset local.arrEntries.append(local.objConfig)>

					<cfset local.rateCardUsageCount += local.qryRateCards.configUsageCount>
				</cfif>
			</cfoutput>

			<cfset local.objRateCard.hasChildren = local.configIndex gt 0>
			<cfset local.objRateCard.rateCardUsageCount = local.rateCardUsageCount>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryRateCards.totalCount),
			"recordsFiltered": val(local.qryRateCards.totalCount),
			"data": local.arrEntries
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
</cfcomponent>