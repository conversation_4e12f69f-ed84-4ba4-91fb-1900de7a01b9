<cfsavecontent variable="local.headJS">
	<cfoutput>
	<!--- GLWidget --->
	#local.strGLAccountWidget.js#

	<!--- Referral Panel Widget --->
	<cfif arguments.event.getValue('mc_siteInfo.hasReferrals')>
		#local.strReferralPanelWidget.js#
	</cfif>
	
	<script type="text/javascript">
		var #toScript(local.arrObjTypes,"arrobjtypes")#
		var #toScript(local.strVisualTypes,"objvisualtypes")#
		
		function showObjectTechInfo(objtypeid) {
			$('div##dashObject' + objtypeid).toggleClass('d-none');
		}
		function doChooseDashboardSection() {
			$('.dashObjFilter').hide();
			var section = $('##dashSection').val();
			if (section.length) {
				var arrSelected = arrobjtypes.filter(function(type) { return type.section.toLowerCase() == section.toLowerCase(); });

				var dashboardSectionTemplateSource = $('##mc_dashboardsSectionTempate').html();
				var dashboardSectionTemplate = Handlebars.compile(dashboardSectionTemplateSource);
				$('##dashObjDefContainer').html(dashboardSectionTemplate({arrsections:arrSelected}));
				$('##divDashboardObjectTypeSection').show();
			} else {
				$('##dashObjDefContainer').html('');
				$('##divDashboardObjectTypeSection,.dashObjFilter').hide();
			}
		}
		function chooseDashboardObject() {
			var objtypeid = Number($('input[name="dashObject"]:checked').val());
			doChooseDashboardObject(objtypeid, false);
			loadDashboardObjectFields(objtypeid);
		}
		function doChooseDashboardObject(objtypeid, editModeInit) {
			if (objtypeid != 0) {
				var arrSelected = arrobjtypes.filter(function(type) { return Number(type.objecttypeid) == objtypeid; });
				if (arrSelected.length) {
					var objType = arrSelected[0];

					$('##divDashboardObjectVisualTypes').html('');
					$('.dashObjFilter').hide();

					let selectedvisualtypeid = #val(local.qryDashboardObject.visualTypeID)#;
					let objectVisualTypes = { 
						arrvisualtypes: objvisualtypes[objtypeid], 
						selectedvisualtypeid: selectedvisualtypeid > 0 ? selectedvisualtypeid : objvisualtypes[objtypeid][0].defaultvisualtypeid
					};
					let visualTypeTemplateSource = $('##mc_dashboardObjectVisualTypesTemplate').html();
					let visualTypeTemplate = Handlebars.compile(visualTypeTemplateSource);
					$('##divDashboardObjectVisualTypes').html(visualTypeTemplate(objectVisualTypes));
					$('##divDashboardObjectVisualTypes').removeClass('d-none');

					if (objType.usememberdatesconfig == 1 && $('##dashboardObjMemJoinDates').length) {
						$('##dashboardObjMemJoinDates').show();
					}
					if (objType.usesubscriptionfilter == 1 && $('##dashboardObjMemSubs').length) {
						if(!editModeInit) try { $("##dashSubTypes,##dashSubs").val(null).trigger('change'); } catch (e) { };
						$('##dashboardObjMemSubs').show();
					}
					if (objType.userevenueglfilter == 1 && $('##dashboardObjRevGL').length) {
						$('##dashboardObjRevGL').show();
						window['mcgl_#local.strGLAccountWidgetData.gridext#Table'].columns.adjust();
					}
					if (objType.useinvoiceprofilefilter == 1 && $('##dashboardObjInvoiceProfile').length) {
						$('##dashboardObjInvoiceProfile').show();
					}
					if (objType.uselistfilter == 1 && $('##dashboardObjLists').length) {
						if(!editModeInit) try { $("##dashListNameList").val(null).trigger('change'); } catch (e) { };
						$('##dashboardObjLists').show();
					}
					if (objType.usepanelfilter == 1 && $('##dashboardObjRefPanel').length) {
						$('##dashboardObjRefPanel').show();
						mcref_initRefPanelsTable('#this.siteResourceID#_1');
					}
					if (objType.usegroupsetfilter == 1 && $('##dashboardObjGrpSets').length) {
						$('##dashboardObjGrpSets').show();
					}
					if (objType.usecalendarfilter == 1 && $('##dashboardObjEVSettings').length) {
						if(!editModeInit) try { $("##dashEVCals,##dashEVCats").val(null).trigger('change'); } catch (e) { };
						$('##dashboardObjEVSettings').show();
					}
					if (objType.usenumericmembercustomfieldfilter == 1 && $('##dashboardObjNumericMemCustomFields').length) {
						$('##dashboardObjNumericMemCustomFields').show();
					}
					
					changeDashboardCategory();
				}
			} else {
				$('##divDashboardObjectVisualTypes').html('');
				$('.dashObjFilter').hide();
			}
		}
		function changeDashboardCategory() {
			if($('##dashObjectCat').val() == 0)
				$('div##newDashCategoryRow').show();
			else {
				$('##newDashCategoryName').val('');
				$('div##newDashCategoryRow').hide();
			}
		}
		function validateAndSaveDashObject() {
			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadDashboard();
				} else {
					alert('We were unable to save dashboard chart. Try again.');
				}
			};

			$('##btnSaveDashObject').prop('disabled',true);

			var arrReq = [];
			<cfif local.isEditMode>
				var objectTypeID = #local.qryDashboardObject.objectTypeID#;
			<cfelse>
				var objectTypeID = Number($('input[name="dashObject"]:checked').val() || 0);
			</cfif>
			var arrSelected = objectTypeID > 0 ? arrobjtypes.filter(function(type) { return Number(type.objecttypeid) === objectTypeID; }) : [];

			if ($('##dashObjectCat').val() == 0 && $('##newDashCategoryName').val().trim() == '') arrReq.push('Enter the new category name.');
			
			if (objectTypeID == 0 || arrSelected.length == 0) arrReq.push('Choose a Dashboard Chart Type.');
			else if (arrSelected[0].usememberdatesconfig == 1 && !$('input[name="dashObjectMemDateConfig"]').is(':checked'))
				arrReq.push('Select a Member Dates Config.');
			else if (arrSelected[0].usegroupsetfilter == 1 && $('##dashGroupSetID').val() == 0)
				arrReq.push('Select a Group Set.');
			
			if (arrSelected[0].usenumericmembercustomfieldfilter == 1 && $('##dashNumericMemCustomFieldColumnID').val() == 0)
				arrReq.push('Select a Custom Field.');

			if ($('.MCDashObjField').length) {
				var fieldsErrorArray = [];
				var fieldContainer = $('.dashObjFieldsContainer');
				fieldsErrorArray = $.map(fieldContainer,doChartFieldsValidate);

				/*drop empty elements*/
				var fieldsErrorArray = $.map(fieldsErrorArray, function(thisError){
					if (thisError.length) return thisError;
					else return null;
				});

				arrReq = arrReq.concat(fieldsErrorArray);
			}

			if (arrReq.length > 0) {
				var strErr = arrReq.join('<br/>');
				$('##btnSaveDashObject').prop('disabled',false);
				$('##divDashboardObjFrmErr').html(strErr).show();
				$("html, body").animate({ scrollTop: 0 }, "slow");
				return false;
			} else {
				$('##divDashboardObjFrmErr').html('').hide();
			}
			
			var udid = 0;
			var typeidlist = '';
			var subidlist = '';
			var glid = 0;
			var acctiplist = '';
			var listnamelist = '';
			var panelidlist = '';
			var groupsetid = 0;
			var evcalendaridlist = '';
			var evcategoryidlist = '';
			var customFieldColumnID = 0;
			var strfields = '';

			if (arrSelected.length) {
				var objType = arrSelected[0];

				if (objType.usememberdatesconfig == 1) {
					udid = $('input[name="dashObjectMemDateConfig"]:checked').val();
				}
				if (objType.usesubscriptionfilter == 1) {
					typeidlist = $('##dashSubTypes').val() || '';
					if (typeidlist != '' && typeidlist.length == 1) {
						subidlist = $('##dashSubs').val() || '';
						if (subidlist != '') subidlist = subidlist.join(',');
					}
					if (typeidlist != '') typeidlist = typeidlist.join(',');
				}
				if (objType.userevenueglfilter == 1) {
					glid = $('##dashGLAccountIDList').val();
				}
				if (objType.useinvoiceprofilefilter == 1) {
					acctiplist = $('##dashIPList').val() || '';
					if (acctiplist != '') acctiplist = acctiplist.join(',');
				}
				if (objType.uselistfilter == 1) {
					listnamelist = $('##dashListNameList').val() || '';
					if (listnamelist != '') listnamelist = listnamelist.join(',');
				}
				if (objType.usepanelfilter == 1) {
					panelidlist = $('##dashReferralPanelIDList').val();
				}
				if (objType.usegroupsetfilter == 1) {
					groupsetid = $('##dashGroupSetID').val();
				}
				if (objType.usecalendarfilter == 1) {
					evcalendaridlist = $('##dashEVCals').val() || '';
					if (evcalendaridlist != '' && evcalendaridlist.length == 1) {
						evcategoryidlist = $('##dashEVCats').val() || '';
						if (evcategoryidlist != '') evcategoryidlist = evcategoryidlist.join(',');
					}
					if (evcalendaridlist != '') evcalendaridlist = evcalendaridlist.join(',');
				}
				if (objType.usenumericmembercustomfieldfilter == 1) {
					customFieldColumnID = $('##dashNumericMemCustomFieldColumnID').val();
				}
			}
			if ($('input.MCDashObjField,select.MCDashObjField').length) {
				strfields = JSON.stringify($('input.MCDashObjField,select.MCDashObjField').serializeArray());
			}

			$('div##divDashboardObjectForm').hide();
			$('div##divDashboardObjectFormLoading').show();

			var objParams = { objectID:$('##dashboardObjID').val(), dashboardID:#local.dashboardID#,
				objectLabel:$('##dashObjLabel').val().trim(), objectDesc:$('##dashObjDesc').val().trim(), objectTypeID:objectTypeID,
				categoryID:$('##dashObjectCat').val(), newCategoryName:$('##newDashCategoryName').val().trim(), visualTypeID:$('##visualType').val(),
				udid:udid, subscriptionTypeIDList:typeidlist, subscriptionIDList:subidlist, glAccountIDList:glid,
				invoiceProfileIDList:acctiplist, listnameList:listnamelist, panelIDList:panelidlist, groupSetID:groupsetid,
				evCalendarIDList:evcalendaridlist, evCategoryIDList:evcategoryidlist, customFieldColumnID:customFieldColumnID, fields:strfields };
			TS_AJX('ADMINDASHBOARD','saveDashboardObject',objParams,saveResult,saveResult,30000,saveResult);
		}
		function loadSubs(arrSubsSelected) {
			$('##dashSubs').empty().trigger("change");

			var typeidlist = $('select##dashSubTypes').val() || '';
			if (typeidlist != '' && typeidlist.length == 1) {
				var typeid = Number(typeidlist[0]);

				if (typeof arrSubsSelected == "undefined")
					var arrSubsSelected = [];

				var objParams = { typeID:typeid };
				$.getJSON('/?event=proxy.ts_json&c=ADMINDASHBOARD&m=getSubsriptionsFromTypeID', objParams)
					.done(function(r) { populateSubs(r,arrSubsSelected); })
					.fail(function(r) { populateSubs(r,arrSubsSelected); });
			} else {
				$('##dashSubsRow').hide();
			}
		}
		function populateSubs(r,arrSubsSelected) {
			if(r.arrsubs.length) {
				$.each(r.arrsubs, function (i, item) {
					$('##dashSubs').append(new Option(item.subscriptionName, item.subscriptionID, false, (arrSubsSelected.length && arrSubsSelected.includes(item.subscriptionID)) ? true : false));
				});
				$('##dashSubs').trigger('change');
				$('##dashSubsRow').show();
			}
			return false;
		}
		function loadEVCats(arrEVCatsSelected) {
			$('##dashEVCats').empty().trigger("change");

			var evcalendaridlist = $('select##dashEVCals').val() || '';
			if (evcalendaridlist != '' && evcalendaridlist.length == 1) {
				var calendarid = Number(evcalendaridlist[0]);

				if (typeof arrEVCatsSelected == "undefined")
					var arrEVCatsSelected = [];

				var objParams = { calendarID:calendarid };
				$.getJSON('/?event=proxy.ts_json&c=VIRTUALGROUPS&m=getFields_event_categories', objParams)
					.done(function(r) { populateEVCategories(r,arrEVCatsSelected); })
					.fail(function(r) { populateEVCategories(r,arrEVCatsSelected); });
			} else {
				$('##dashEVCatsRow').hide();
			}
		}
		function populateEVCategories(r,arrEVCatsSelected) {
			if(r.qrycategories.length) {
				$.each(r.qrycategories, function (i, item) {
					$('##dashEVCats').append(new Option(item.category, item.categoryid, false, (arrEVCatsSelected.length && arrEVCatsSelected.includes(item.categoryid)) ? true : false));
				});
				$('##dashEVCats').trigger('change');
				$('##dashEVCatsRow').show();
			}
			return false;
		}
		<cfif local.isEditMode>
			function initEditDashboardObjForm() {
				$('##btnSaveDashObject').prop('disabled',true);
				
				var objectTypeID = $('##dashObject').val();
				var arrSelected = arrobjtypes.filter(function(type) { return Number(type.objecttypeid) == objectTypeID; });

				doChooseDashboardObject(objectTypeID, true);
				loadDashboardObjectFields(objectTypeID);

				<cfif arguments.event.getValue('mc_siteInfo.sf_subscriptions')>
					if ($('##dashSubTypes').length && $('##dashSubTypes').val() !== null) {
						var #toScript(local.subIDList,"subIDList")# 
						var arrsubs = [];
						if (subIDList.length) {
							arrsubs = subIDList.split(',').map(function(id) { return Number(id) });
						}
						loadSubs(arrsubs);
					}
				</cfif>

				<cfif local.showEvents>
					if ($('##dashEVCals').length && $('##dashEVCals').val() !== null) {
						var #toScript(local.evCategoryIDList,"evCategoryIDList")# 
						var arrevcategories = [];
						if (evCategoryIDList.length) {
							arrevcategories = evCategoryIDList.split(',').map(function(id) { return Number(id) });
						}
						loadEVCats(arrevcategories);
					}
				</cfif>

				$('##btnSaveDashObject').prop('disabled',false);
			}

			$(function() {
				initEditDashboardObjForm();
			});
		<cfelse>
			function addGLAccountsToWidget(glidlist,gext) {
				var glResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						var tbodyEl = $('##mcgl_'+gext+'Table tbody');
						if(r.arrgls.length > 0) tbodyEl.find('tr').remove();
						$.each(r.arrgls, function (i,gl) {
							tbodyEl.append(
								'<tr id="'+gext+'-gla-'+gl.glaccountid+'">'
									+'<td class="align-top">'+gl.thepathexpanded+'</td>'
									+'<td class="align-top text-center"><a href="javascript:removeNewDashObjGLAccount('+gl.glaccountid+',\''+gext+'\')" title="Remove GL Account" class="btn btn-xs text-danger p-1 mx-1"><i class="fa-solid fa-trash-alt"></i></a></td>'
								+'</tr>'
							);
						}); 

						$('##dashGLAccountIDList').val(glidlist);
						MCModalUtils.hideModal();
					} else {
						alert('We were unable to save revenue GLAccount selections. Try again.');
					}
				};

				$('##dashGLAccountIDList').val('');
				if (glidlist.length) {
					var objParams = { glAccountIDList:glidlist };
					TS_AJX('ADMINDASHBOARD','getGLAccountDetailsFromGLIDList',objParams,glResult,glResult,10000,glResult);
				} else {
					mcgl_reloadGrid(gext,1);
				}
			}
			function removeNewDashObjGLAccount(id,gext) {
				var arrGLs = $('##dashGLAccountIDList').val().split(',');
				arrGLs = arrGLs.filter(function(gl) { return Number(gl) != Number(id); });
				$('##dashGLAccountIDList').val(arrGLs.join(','));

				var tbodyEl = $('##mcgl_'+gext+'Table tbody');
				tbodyEl.find('tr##'+gext+'-gla-'+id).remove();
				if(tbodyEl.find('tr').length == 0) mcgl_reloadGrid(gext);
			}
		</cfif>

		function loadDashboardObjectFields(objtypeid) {
			var getObjectFieldsResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.hasfilterfields || r.hascustomfields) {
						let fieldsSource = $('##mc_dashboardObjectFieldsTemplate').html();
						let fieldsTemplate = Handlebars.compile(fieldsSource);
						let arrFields = [];

						if (r.hasfilterfields) {
							let filterFields = r.strfields.filter;
							$('##divDashboardObjectFilters').html(fieldsTemplate(filterFields)).removeClass('d-none');
							arrFields = arrFields.concat(filterFields.fields);
						}
						if (r.hascustomfields) {
							let customFields = r.strfields.custom;
							$('##divDashboardObjectCustom').html(fieldsTemplate(customFields)).removeClass('d-none');
							arrFields = arrFields.concat(customFields.fields);
						}
						initDashboardObjectFieldsControls($('.dashObjFieldsContainer'),arrFields);
					} else {
						$('##divDashboardObjectFilters,##divDashboardObjectCustom').html('').addClass('d-none');
					}
				} else {
					alert('We were unable to load this chart fields.');
				}
			};

			var objParams = { objectID:$('##dashboardObjID').val(), objectTypeID:objtypeid };
			TS_AJX('ADMINDASHBOARD','getDashboardObjectFields',objParams,getObjectFieldsResult,getObjectFieldsResult,10000,getObjectFieldsResult);
		}
		function initDashboardObjectFieldsControls (scope, arrFields, mode) {
			if(mode === undefined) {
				mode = 'init';
			}
			if (scope.find('.dateControl').length) {
				arrFields.map(function(str) {
					if (str.mode == 'daterange')
						mca_setupDatePickerRangeFields(str.formfromname,str.formtoname);
				});
				mca_setupCalendarIcons('frmEditDashboardObject');
			}
		}
		function doChartFieldsValidate(chartfieldsWrapper) {
			var errorMsgArray = [];

			var thisInstance = $(chartfieldsWrapper);

			/*required fields*/
			var chartFieldRequired = thisInstance.find('input:text[data-isrequired="true"], select[data-isrequired="true"], textarea[data-isrequired="true"]').not(':hidden').not(':disabled');
			
			var chartFieldRequiredErrorMsgArray = $.map(chartFieldRequired,validateMCChart_fieldIsRequired);
			Array.prototype.push.apply(errorMsgArray, chartFieldRequiredErrorMsgArray);

			/*text controls offering whole number*/
			var textControlIntegerCustomField = thisInstance.find('input[data-chartfielddisplaycode="TEXTBOX"][data-chartfielddatatypecode="INTEGER"]').not(':hidden').not(':disabled');
			var textControlIntegerCustomFieldErrorMsgArray = $.map(textControlIntegerCustomField,validateMCChart_textControlValidInteger);
			Array.prototype.push.apply(errorMsgArray, textControlIntegerCustomFieldErrorMsgArray);

			/*text controls offering decimal number*/
			var textControlDecimalCustomField = thisInstance.find('input[data-chartfielddisplaycode="TEXTBOX"][data-chartfielddatatypecode="DECIMAL2"]').not(':hidden').not(':disabled');
			var textControlDecimalCustomFieldErrorMsgArray = $.map(textControlDecimalCustomField,validateMCChart_textControlValidDecimal);
			Array.prototype.push.apply(errorMsgArray, textControlDecimalCustomFieldErrorMsgArray);

			/*drop empty elements*/
			var finalErrors = $.map(errorMsgArray, function(thisError){
				if (thisError.length) return thisError;
				else return null;
			});
			
			return finalErrors;
		}
		function validateMCChart_fieldIsRequired(thisField) {
			var fld = $(thisField);
			var fldName = $(thisField).attr('name');
			var displayTypeCode = fld.data('chartfielddisplaycode');
			var returnMsg = '';

			switch(displayTypeCode) {
				case 'TEXTBOX':
				case 'TEXTAREA':
				case 'DATE':
					if (fld.val() == '') {
						returnMsg =  fld.data('chartfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + ' is required.';
					}
				break;
				case 'SELECT':
					if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
						returnMsg =  fld.data('chartfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + ' is required.';
					}
				break;
				case 'RADIO':
				case 'CHECKBOX':
					if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
						returnMsg =  fld.data('chartfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + ' is required.';
					}
				break;
			}
			return returnMsg;
		}
		function validateMCChart_textControlValidInteger(thisField) {
			var returnMsg = '';
			var fld = $(thisField);
			var fldval = Number(fld.val().trim());

			if (fldval != '' && fldval !== parseInt(fldval)) {
				returnMsg = 'Enter a valid whole number for ' + fld.data('chartfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
			}
			return returnMsg;
		}
		function validateMCChart_textControlValidDecimal(thisField) {
			var returnMsg = '';
			var fld = $(thisField);
			var fldval = Number(fld.val().trim().replace(/,/g,''));

			if (fldval != '') {
				if (fldval !== parseFloat(fldval)) {
					returnMsg = 'Enter a valid decimal number for ' + fld.data('chartfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
				}
			}
			return returnMsg;
		}

		$(function() {
			mca_setupSelect2();
			changeDashboardCategory();
		});
	</script>
	<style type="text/css">
		div##divDashboardObjectForm { min-height:300px; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.headJS)#">

<cfoutput>
<div class="row" id="divDashboardObjectForm">
	<div class="col-xl-12">
		<!--- error placeholder --->
		<div id="divDashboardObjFrmErr" class="alert alert-danger mb-3" style="display:none;"></div>

		<form name="frmEditDashboardObject" id="frmEditDashboardObject">
			<input type="hidden" name="dashboardObjID" id="dashboardObjID" value="#val(local.qryDashboardObject.objectID)#">

			<div class="form-group row">
				<label for="dashObjLabel" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Chart Label</label>
				<div class="col-md-9 col-sm-12">
					<input type="text" name="dashObjLabel" id="dashObjLabel" value="#local.qryDashboardObject.objectLabel#" class="form-control form-control-sm" maxlength="100" autocomplete="off">
				</div>
			</div>
			<div class="form-group row">
				<label for="dashObjDesc" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Chart Desc</label>
				<div class="col-md-9 col-sm-12">
					<textarea name="dashObjDesc" id="dashObjDesc" class="form-control form-control-sm w-100" rows="3" cols="50">#local.qryDashboardObject.objectDesc#</textarea>
				</div>
			</div>
			<div class="row my-3">
				<div class="col-sm-12">
					<b>Category Selection</b><br/>
					Charts are organized into categories on your dashboard. In which category would you like to place this chart?
					<div class="form-group row mt-3">
						<label for="dashObjectCat" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Category</label>
						<div class="col-md-9 col-sm-12">
							<select name="dashObjectCat" id="dashObjectCat" onChange="changeDashboardCategory();" class="form-control form-control-sm">
								<cfloop query="local.qryDashboardCategories">
									<option value="#local.qryDashboardCategories.categoryID#"<cfif local.qryDashboardObject.categoryID eq local.qryDashboardCategories.categoryID> selected="selected"</cfif>>#local.qryDashboardCategories.categoryName#</option>
								</cfloop>
								<option value="0">---create a new dashboard category---</option>
							</select>
						</div>
					</div>
					<div class="form-group row" id="newDashCategoryRow" style="display:none;">
						<label for="newDashCategoryName" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">New Dashboard Category</label>
						<div class="col-md-9 col-sm-12">
							<input type="text" name="newDashCategoryName" id="newDashCategoryName" class="form-control form-control-sm" value="" maxlength="200" autocomplete="off">
						</div>
					</div>
				</div>
			</div>

			<cfif local.isEditMode>
				<div class="row my-3">
					<div class="col-sm-12">
						<div class="form-group row">
							<label class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Chart</label>
							<div class="col-md-9 col-sm-12">
								<div>
									<input type="hidden" name="dashObject" id="dashObject" value="#local.qryDashboardObject.objectTypeID#">
									<b>#local.qryDashboardObject.objectTypeTitle#</b>
								</div>
								<div class="font-weight-light">
									#local.qryDashboardObject.objectTypeDesc#
								</div>
								<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(local.qryDashboardObject.objectTypeTechDesc)>
									<div class="mt-1 mb-2">
										<a href="javascript:renderDashboardObjectDemo(#local.qryDashboardObject.objectTypeID#,#local.qryDashboard.fiscalYearStartMonth#);"><i class="fa-regular fa-chart-line"></i> Sample Chart</a>
										<a href="javascript:showObjectTechInfo(#local.qryDashboardObject.objectTypeID#);" class="ml-2"><i class="fa-regular fa-wrench"></i> Technical Description</a>
									</div>
									<div class="d-none" id="dashObject#local.qryDashboardObject.objectTypeID#">
										<div class="alert d-flex align-items-center pl-2 align-content-center alert-info mt-2">
											<span class="font-size-lg d-block d-40 mr-2 text-center">
												<span class="superuser">&nbsp;</span>
											</span>
											<span>
												<strong class="d-block">Technical Info</strong> #local.qryDashboardObject.objectTypeTechDesc#
											</span>
										</div>
									</div>
								<cfelse>
									<div class="mt-1 mb-2">
										<a href="javascript:renderDashboardObjectDemo(#local.qryDashboardObject.objectTypeID#,#local.qryDashboard.fiscalYearStartMonth#);"><i class="fa-regular fa-chart-line"></i> Sample Chart</a>
									</div>
								</cfif>
							</div>
						</div>
					</div>
				</div>
			<cfelse>
				<div class="row my-2">
					<div class="col-sm-12">
						<b>Chart Definition</b><br/>
						Which chart would you like on your dashboard?
						<div class="form-group row mt-3">
							<label for="dashSection" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Type of Chart</label>
							<div class="col-md-9 col-sm-12">
								<select name="dashSection" id="dashSection" class="form-control form-control-sm" onchange="doChooseDashboardSection();">
									<option value="">Choose a chart type</option>
									<cfoutput query="local.qryObjSections">
										<option value="#local.qryObjSections.section#">#local.qryObjSections.section#</option>
									</cfoutput>
								</select>
							</div>
						</div>
					</div>
				</div>
			</cfif>

			<div class="row" id="divDashboardObjectTypeSection"<cfif NOT local.isEditMode> style="display:none;"</cfif>>
				<div class="col-sm-12">
					<cfif NOT local.isEditMode>
						<div class="form-group row">
							<label for="dashObject" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Chart</label>
							<div class="col-md-9 col-sm-12" id="dashObjDefContainer"></div>
						</div>
					</cfif>

					<div id="divDashboardObjectVisualTypes" class="d-none"></div>
					<div id="divDashboardObjectFilters" class="d-none"></div>

					<cfif local.qryMemberJoinDateFields.recordCount>
						<div class="form-group row mt-3 dashObjFilter" id="dashboardObjMemJoinDates" style="display:none;">
							<label for="dashObjectMemDateConfig" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Member Dates Config</label>
							<div class="col-md-9 col-sm-12">
								<cfloop array="#local.arrMemberJoinDateFields#" index="local.thisConfig">
									<div class="form-check pb-2">
										<input class="form-check-input" type="radio" name="dashObjectMemDateConfig" id="dashObjectMemDateConfig_#local.thisConfig.udid#" value="#local.thisConfig.udid#"<cfif local.udid eq local.thisConfig.udid> checked</cfif>>
										<label class="form-check-label" for="dashObjectMemDateConfig_#local.thisConfig.udid#">#local.thisConfig.configName#</label>
									</div>
								</cfloop>
							</div>
						</div>
					</cfif>

					<cfif arguments.event.getValue('mc_siteInfo.sf_subscriptions')>
						<div class="row mt-3 dashObjFilter" id="dashboardObjMemSubs" style="display:none;">
							<div class="col-sm-12">
								Optionally, limit this chart to the following subscriptions:<br/>
								<div style="padding:15px 0 15px 10px;">
									<div class="form-group row">
										<label for="dashSubTypes" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Subscription Type</label>
										<div class="col-md-9 col-sm-12">
											<select name="dashSubTypes" id="dashSubTypes" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="All Subscription Types" onchange="loadSubs()">
												<cfloop query="local.qrySubscriptionTypes">
													<option value="#local.qrySubscriptionTypes.typeID#"<cfif listFind(local.typeIDList,local.qrySubscriptionTypes.typeID)> selected</cfif>>#local.qrySubscriptionTypes.typeName#</option>
												</cfloop>
											</select>
										</div>
									</div>
									<div class="form-group row" id="dashSubsRow" style="display:none;">
										<label for="dashSubs" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Subscription</label>
										<div class="col-md-9 col-sm-12">
											<select name="dashSubs" id="dashSubs" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="All Subscriptions"></select>
										</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>

					<div class="row dashObjFilter mt-3" id="dashboardObjRevGL" style="display:none;">
						<div class="col-sm-12">
							Optionally, limit this chart to the following revenue GL accounts:<br/>
							<div>
								<input type="hidden" name="dashGLAccountIDList" id="dashGLAccountIDList" value="">
								#local.strGLAccountWidget.html#
							</div>
						</div>
					</div>

					<cfif arguments.event.getValue('mc_siteInfo.hasReferrals')>
						<div class="row dashObjFilter mt-3" id="dashboardObjRefPanel" style="display:none;">
							<div class="col-sm-12">
								<input type="hidden" name="dashReferralPanelIDList" id="dashReferralPanelIDList" value="">
								#local.strReferralPanelWidget.html#
							</div>
						</div>
					</cfif>

					<cfif local.qryLists.recordCount>
						<div class="row mt-3 dashObjFilter" id="dashboardObjLists" style="display:none;">
							<div class="col-sm-12">
								Optionally, limit this chart to the following lists:<br/>
								<div style="padding:15px 0 15px 10px;">
									<div class="form-group row">
										<label for="dashListNameList" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">List</label>
										<div class="col-md-9 col-sm-12">
											<select name="dashListNameList" id="dashListNameList" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="All Lists">
												<cfloop query="local.qryLists">
													<option value="#local.qryLists.listName#"<cfif listFind(local.listNameList,local.qryLists.listName)> selected</cfif>>#local.qryLists.listName#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>

					<cfif local.qryInvoiceProfiles.recordCount>
						<div class="row mt-3 dashObjFilter" id="dashboardObjInvoiceProfile" style="display:none;">
							<div class="col-sm-12">
								Optionally, limit this chart to the following invoice profiles:<br/>
								<div style="padding:15px 0 15px 10px;">
									<div class="form-group row">
										<label for="dashIPList" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Invoice Profile</label>
										<div class="col-md-9 col-sm-12">
											<select name="dashIPList" id="dashIPList" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="All Invoice Profiles">
												<cfloop query="local.qryInvoiceProfiles">
													<option value="#local.qryInvoiceProfiles.profileID#"<cfif listFind(local.invoiceProfileIDList,local.qryInvoiceProfiles.profileID)> selected</cfif>>#local.qryInvoiceProfiles.profileName#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>

					<cfif local.qryGroupSets.recordCount>
						<div class="form-group row mt-3 dashObjFilter" id="dashboardObjGrpSets" style="display:none;">
							<label for="dashGroupSetID" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Group Set</label>
							<div class="col-md-9 col-sm-12">
								#local.strGroupSetSelector.html#
							</div>
						</div>
					</cfif>

					<cfif local.showEvents>
						<div class="row mt-3 dashObjFilter" id="dashboardObjEVSettings" style="display:none;">
							<div class="col-sm-12">
								Optionally, limit this chart to the following event calendars:<br/>
								<div style="padding:15px 0 15px 10px;">
									<div class="form-group row">
										<label for="dashEVCals" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Calendar</label>
										<div class="col-md-9 col-sm-12">
											<select name="dashEVCals" id="dashEVCals" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="All Calendars" onchange="loadEVCats()">
												<cfloop query="local.qryCalendars">
													<option value="#local.qryCalendars.calendarID#"<cfif listFind(local.evCalendarIDList,local.qryCalendars.calendarID)> selected</cfif>>#local.qryCalendars.calendarName#</option>
												</cfloop>
											</select>
										</div>
									</div>
									<div class="form-group row" id="dashEVCatsRow" style="display:none;">
										<label for="dashEVCats" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Category</label>
										<div class="col-md-9 col-sm-12">
											<select name="dashEVCats" id="dashEVCats" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="All Categories"></select>
										</div>
									</div>
								</div>
							</div>
						</div>
					</cfif>

					
					<cfif local.qryNumericCustomFields.recordCount>
						<div class="form-group row mt-3 dashObjFilter" id="dashboardObjNumericMemCustomFields" style="display:none;">
							<label for="dashNumericMemCustomFieldColumnID" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Numeric Custom Field</label>
							<div class="col-md-9 col-sm-12">
								<select name="dashNumericMemCustomFieldColumnID" id="dashNumericMemCustomFieldColumnID" class="form-control form-control-sm">
									<option value="0">Select a Custom Field</option>
									<cfloop query="local.qryNumericCustomFields">
										<option value="#local.qryNumericCustomFields.columnID#"<cfif local.customFieldColumnID EQ local.qryNumericCustomFields.columnID> selected</cfif>>#local.qryNumericCustomFields.columnName#</option>
									</cfloop>
								</select>
							</div>
						</div>
					</cfif>

					<div id="divDashboardObjectCustom" class="d-none"></div>
				</div>
			</div>

			<div class="row mt-3">
				<div class="col-sm-12">
					<button type="button" name="btnSaveDashObject" id="btnSaveDashObject" class="btn btn-sm btn-primary" onclick="validateAndSaveDashObject();">Save</button>
					<button type="button" name="btnCancelDashObject" id="btnCancelDashObject" class="btn btn-sm btn-secondary" onclick="returnToDashboardView();">Cancel</button>
				</div>
			</div>
		</form>
	</div>
</div>

<div id="dashboardObjDemo" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="dashboardObjDemoTitle" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="dashboardObjDemoTitle"></h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body p-1"></div>
		</div>
	</div>
</div>

<div id="divDashboardObjectFormLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<img src="/assets/common/images/indicator.gif" width="100" height="100">
		<br/><br/>
		<b>Please wait while we validate and save the details.</b>
		<br/>
	</div>
</div>

<script id="mc_dashboardsSectionTempate" type="text/x-handlebars-template">
	{{##each arrsections}}
		<div class="form-check pb-2">
			<input class="form-check-input" type="radio" name="dashObject" id="dashObject{{objecttypeid}}" value="{{objecttypeid}}" onclick="chooseDashboardObject();">
			<label class="form-check-label" for="dashObject{{objecttypeid}}">
				<b>{{objecttypetitle}}</b>
				<div class="font-weight-light">{{objecttypedesc}}</div>
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					{{##compare objecttypetechdesc.length '>' 0}}
						<div class="mt-1 mb-2">
							<a href="javascript:renderDashboardObjectDemo({{objecttypeid}},#local.qryDashboard.fiscalYearStartMonth#);">
								<i class="fa-regular fa-chart-line"></i> Sample Chart
							</a>
							<a href="javascript:showObjectTechInfo({{objecttypeid}});" class="ml-2">
								<i class="fa-regular fa-wrench"></i> Technical Description
							</a>
						</div>
						<div class="d-none" id="dashObject{{objecttypeid}}">
							<div class="alert d-flex align-items-center pl-2 align-content-center alert-info">
								<span class="font-size-lg d-block d-40 mr-2 text-center">
									<span class="superuser">&nbsp;</span>
								</span>
								<span>
									<strong class="d-block">Technical Info</strong> {{objecttypetechdesc}}
								</span>
							</div>
						</div>
					{{/compare}}
				<cfelse>
					<div class="mt-1 mb-2">
						<a href="javascript:renderDashboardObjectDemo({{objecttypeid}},#local.qryDashboard.fiscalYearStartMonth#);">
							<i class="fa-regular fa-chart-line"></i> Sample Chart
						</a>
					</div>
				</cfif>
			</label>
		</div>
	{{/each}}
</script>

<script id="mc_dashboardObjectVisualTypesTemplate" type="text/x-handlebars-template">
	<div class="form-group row">
		<label for="visualType" class="col-md-3 col-sm-12 col-form-label-sm font-size-md">Visualization</label>
		<div class="col-md-9 col-sm-12">
			<select name="visualType" id="visualType" class="form-control form-control-sm">
				{{##each arrvisualtypes}}
					<option value="{{this.visualtypeid}}" {{##compare ../selectedvisualtypeid '==' this.visualtypeid}}selected{{/compare}}>
						{{this.visualtype}}
					</option>
				{{/each}}
			</select>
		</div>
	</div>
</script>

<script id="mc_dashboardObjectDemoTemplate" type="text/x-handlebars-template">
	{{##switch objectvisualtype}}
		{{##case "barchart" "columnchart" "linechart" "piechart" "donutchart" "areachart" "radialbarchart"}}
			<div id="divDashboardObjectDemoChartBox"></div>
		{{/case}}
		{{##case "mcstatbox"}}
			<div id="divDashboardObjectDemoStatBox" class="pl-3 h-25"></div>
			<div id="divDemoChartContainer" class="h-75">	
				<div id="divDashboardObjectDemoChartBox" class="p-1"></div>
			</div>
		{{/case}}
		{{##case "mctable"}}
			<div id="divDashboardObjectDemoTable" class="h-100"></div>
		{{/case}}
		{{##case "mctableandbarchart" "mctableandcolumnchart" "mctableandlinechart" "mctableandpiechart" "mctableanddonutchart" "mctableandareachart" "mctableandradialbarchart"}}
			<div id="divDashboardObjectDemoTable" class="p-2 h-25"></div>
			<div id="divDemoChartContainer" class="h-75">
				<div id="divDashboardObjectDemoChartBox" class="p-1"></div>
			</div>
		{{/case}}
	{{/switch}}
</script>

<script id="mc_dashboardObjectFieldsTemplate" type="text/x-handlebars-template">
<div class="dashObjFieldsContainer">
{{##compare title.length '>' 0}}<b>{{title}}</b><br/>{{/compare}}
{{desc}}
{{##each fields}}
	<div class="form-group row mt-3">
		<label for="{{fieldname}}" class="col-sm-3 col-form-label-sm font-size-md">
			{{##if required}}*{{/if}}&nbsp; {{fieldlabel}}
		</label>
		{{##switch mode}}
			{{##case "daterange"}}
				<div class="col-md-9">
					<div class="row">
						<div class="col-sm-4 pr-sm-0">
							<div class="input-group input-group-sm">
								<input type="text" name="{{formfromname}}" id="{{formfromname}}" value="{{fieldvaluefrom}}" class="form-control form-control-sm dateControl MCDashObjField" placeholder="Date from" data-isrequired="{{required}}" data-chartfielddisplaycode="DATE" data-chartfielddatatypecode="DATE" data-chartfielddesc="Date From">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="{{formfromname}}"><i class="fa-solid fa-calendar"></i></span>
								</div>
							</div>
						</div>
						<div class="col-sm-4 pr-sm-0">
							<div class="input-group input-group-sm">
								<input type="text" name="{{formtoname}}" id="{{formtoname}}" value="{{fieldvalueto}}" class="form-control form-control-sm dateControl MCDashObjField" placeholder="Date to" data-isrequired="{{required}}" data-chartfielddisplaycode="DATE" data-chartfielddatatypecode="DATE" data-chartfielddesc="Date to">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="{{formtoname}}"><i class="fa-solid fa-calendar"></i></span>
								</div>
							</div>
						</div>
					</div>
				</div>
			{{/case}}
			{{##case "amount"}}
				<div class="col-sm-3">
					<div class="input-group input-group-sm">
						<div class="input-group-prepend">
							<span class="input-group-text">$</span>
						</div>
						<input type="text" name="{{fieldname}}" id="{{fieldname}}" value="{{fieldvalue}}" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control form-control-sm MCDashObjField" placeholder="{{fieldlabel}}" data-isrequired="{{required}}" data-chartfielddisplaycode="TEXTBOX" data-chartfielddatatypecode="DECIMAL2" data-chartfielddesc="{{fieldlabel}}">
					</div>
				</div>
			{{/case}}
			{{##case "number"}}
				<div class="col-sm-3">
					<div class="input-group input-group-sm">
						<input type="text" name="{{fieldname}}" id="{{fieldname}}" value="{{fieldvalue}}"  class="form-control form-control-sm MCDashObjField" placeholder="{{fieldlabel}}" data-isrequired="{{required}}" data-chartfielddisplaycode="TEXTBOX" data-chartfielddatatypecode="INTEGER" data-chartfielddesc="{{fieldlabel}}">
					</div>
				</div>
			{{/case}}
			{{##case "boolean"}}
				<div class="col-sm-3">
					<select name="{{fieldname}}" id="{{fieldname}}" class="form-control form-control-sm MCDashObjField" data-isrequired="{{required}}" data-chartfielddisplaycode="SELECT" data-chartfielddesc="{{fieldlabel}}">
						<option value="0"{{##compare fieldvalue '==' 0}} selected{{/compare}}>No</option>
						<option value="1"{{##compare fieldvalue '==' 1}} selected{{/compare}}>Yes</option>
					</select>
				</div>
			{{/case}}
		{{/switch}}
	</div>
{{/each}}
</div>
</script>
</cfoutput>