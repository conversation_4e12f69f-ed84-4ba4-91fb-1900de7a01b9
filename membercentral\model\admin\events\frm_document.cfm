<cfsavecontent variable="local.documentFormJS">
	<cfoutput>
		<script language="javascript">
			function validateDocumentForm() {
				mca_hideAlert('err_eventdoc');
				var arrReq = new Array();
				if ($('##docTitle').val() == '') arrReq[arrReq.length] = 'Enter a title for your document.';
				if ($('##docGrouping').val() == '' && $('##newDocGroupingName').val().trim() == '') arrReq[arrReq.length] = 'Enter the new document grouping name.';
				<cfif local.eventDocumentID eq 0>
					if ($('##newFile').val() == '') arrReq[arrReq.length] = 'Select a document to upload.';
				</cfif>
				
				if (arrReq.length > 0) {
					mca_showAlert('err_eventdoc', arrReq.join('<br/>'));
					return false;
				}

				top.$("##btnMCModalSave").prop('disabled',true).html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Saving...');
				return true;
			}
			function changeDocGroupings() {
				if($('##docGrouping').val() == "new")
					$('##newDocGroupingRow').removeClass('d-none');
				else {
					$('##newDocGroupingName').val('');
					$('##newDocGroupingRow').addClass('d-none');
				}
			}

			$(function() {
				mca_setupCustomFileControls('frmDocument');
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.documentFormJS)#">

<cfoutput>
<form name="frmDocument" id="frmDocument" action="#this.link.saveDocument#" class="p-3" method="POST" enctype="multipart/form-data" onsubmit="return validateDocumentForm();" autocomplete="off">
	<input type="hidden" name="documentID" id="documentID" value="#local.documentID#">
	<input type="hidden" name="eventDocumentID" id="eventDocumentID" value="#local.eventDocumentID#">
	<input type="hidden" name="eventID" id="eventID" value="#local.eventID#">

	<div id="err_eventdoc" class="alert alert-danger mb-2 d-none"></div>

	<div class="form-label-group">
		<input type="text" name="docTitle" id="docTitle" value="#local.qryDocument.docTitle#" class="form-control" maxlength="255">
		<label for="docTitle">Document Title *</label>
	</div>
	<div class="form-label-group">
		<textarea name="docDesc" id="docDesc" rows="3" class="form-control">#local.qryDocument.docDesc#</textarea>
		<label for="docDesc">Document Description</label>
	</div>
	<div class="form-label-group">
		<select name="docGrouping" id="docGrouping" class="custom-select" onchange="changeDocGroupings();">
			<option value="0">Default - No Grouping</option>
			<cfloop query="local.qryDocumentGroupings">
				<option value="#local.qryDocumentGroupings.eventDocumentGroupingID#"<cfif local.qryDocument.eventDocumentGroupingID EQ local.qryDocumentGroupings.eventDocumentGroupingID> selected</cfif>>#local.qryDocumentGroupings.eventDocumentGrouping#</option>
			</cfloop>
			<option value="new">Create a New Document Grouping</option>
		</select>
		<label for="docGrouping">Document Grouping</label>
	</div>
	<div id="newDocGroupingRow" class="form-label-group d-none">
		<input type="text" name="newDocGroupingName" id="newDocGroupingName" value="" class="form-control" maxlength="200">
		<label for="newDocGroupingName">Document Grouping Name</label>
	</div>
	<div class="form-label-group">
		<input type="text" name="docAuthor" id="docAuthor" value="#local.qryDocument.author#" class="form-control" maxlength="255">
		<label for="docAuthor">Document Author</label>
	</div>
	<cfif local.eventDocumentID gt 0>
		<input type="hidden" name="documentLanguageID" id="documentLanguageID" value="#local.qryDocument.documentLanguageID#">

		<div class="form-label-group">
			<input type="text" name="existingFileName" value="#local.qryDocument.fileName#" class="form-control-plaintext" readonly>
			<label for="existingFileName">Existing Filename</label>
		</div>
	</cfif>
	<div class="custom-file">
		<input type="file" name="newFile" id="newFile" class="custom-file-input" value="">
		<label for="newFile" class="custom-file-label">Select <cfif local.eventDocumentID gt 0>New </cfif>Document</label>
	</div>
	<button type="submit" id="btnSaveDocument" class="d-none">Save Document</button>
</form>
</cfoutput>