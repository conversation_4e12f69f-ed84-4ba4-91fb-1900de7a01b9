<cfif val(local.qryGroupSet.groupsetID)>
	<cfsavecontent variable="local.pageJS">
		<cfoutput>
		<script type="text/javascript">
			function previewMemberGS() {
				let previewMemberGSResult = function(r) {
					$('##btnPreviewGS').prop('disabled',false).html('Preview');
					if (r.success && r.success.toLowerCase() == 'true') {
						let previewGSMemberSource = $('##mc_previewGSMemberData').html();
						let previewGSMemberTemplate = Handlebars.compile(previewGSMemberSource);
						$('##previewMemGroupsTable tbody').html(previewGSMemberTemplate(r));
					} else {
						if (r.memberid == 0) mca_showAlert('err_GSPreview', 'Invalid MemberNumber', false);
						else mca_showAlert('err_GSPreview', 'We were unable to get the preview member group set details. Try again!', false);
					}
				};

				mca_hideAlert('err_GSPreview');
				$('##previewGSMemberNumber').removeClass('is-invalid');
				
				let memberNumber = $('##previewGSMemberNumber').val().trim();
				if (memberNumber.length) {
					$('##previewMemGroupsTable tbody').html('');
					$('##btnPreviewGS').prop('disabled',true).html('Please wait...');
					let objParams = { memberNumber:memberNumber, groupsetID:#val(local.qryGroupSet.groupsetID)# };
					TS_AJX('ADMREPORTS','getPreviewMemberGroupSetData',objParams,previewMemberGSResult,previewMemberGSResult,10000,previewMemberGSResult);
				} else {
					$('##previewGSMemberNumber').addClass('is-invalid');
				}
			}

			$(function() {
				previewMemberGS();
			});
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

	<cfoutput>
	<div id="err_GSPreview" class="alert alert-danger mb-3 d-none"></div>
	
	<h5>#EncodeForHTML(local.qryGroupSet.groupsetName)#</h5>

	<div class="form-row mt-4">
		<div class="col">
			<div class="form-group">
				<div class="form-label-group">
					<div class="input-group">
						<input type="text" name="previewGSMemberNumber" id="previewGSMemberNumber" value="#local.memberNumberForPreview#" class="form-control">
						<div class="input-group-append">
							<button type="button" name="btnPreviewGS" id="btnPreviewGS" class="btn input-group-text" onclick="previewMemberGS();">
								Preview
							</button>
						</div>
						<label for="previewGSMemberNumber">Preview Using MemberNumber</label>
					</div>
				</div>
			</div>
		</div>
	</div>
	<table id="previewMemGroupsTable" class="table table-sm table-striped table-bordered mt-2" style="width:100%">
		<thead>
			<tr>
				<th>Group Label</th>
				<th>Value</th>
			</tr>
		</thead>
		<tbody></tbody>
	</table>
	<script id="mc_previewGSMemberData" type="text/x-handlebars-template">
		{{##each strgroupsetdata}}
			{{##compare @key '!=' 'memberid'}}
				<tr>
					<td>{{@key}}</td>
					<td>{{this}}</td>
				</tr>
			{{/compare}}
		{{/each}}
	</script>
	</cfoutput>
<cfelse>
	<cfoutput>
	<h5>#EncodeForHTML(local.qryGroupSet.groupsetName)#</h5>
	<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show mt-3" role="alert">
		<span class="font-size-lg d-block d-40 mr-2 text-center">
			<i class="fa-regular fa-circle-info"></i>
		</span>
		<span>
			No groups have been added to this group set yet.
		</span>
	</div>
	</cfoutput>
</cfif>