<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// Load Objects for page -------------------------------------------------------------------- ::
			this.objMemberGroupSet							= CreateObject("component","model.admin.MemberGroupSets.MemberGroupSets");
			// use resourceID of the site for security -------------------------------------------------- ::
			this.siteResourceID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');

			// build quick links ------------------------------------------------------------------------ ::
			this.link.list 					= buildCurrentLink(arguments.event,"list");
			this.link.edit					= buildCurrentLink(arguments.event,"edit")& "&mode=direct";
			this.link.add					= buildCurrentLink(arguments.event,"add") & "&mode=direct";
			this.link.save					= buildCurrentLink(arguments.event,"save");
			this.link.addGroup				= buildCurrentLink(arguments.event,"addGroup") & "&mode=stream";
			this.link.previewGroupSet		= buildCurrentLink(arguments.event,"previewGroupSet") & "&mode=stream";

			// method to run ---------------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="prepResult" type="string" required="false">
	
		<cfset var local = structNew()>
		<cfset local.exportGroupSetStructureZIPLink = buildCurrentLink(arguments.event,"exportGroupSetStructureZIP") & "&mode=stream">
		<cfset local.prepareGroupSetImportLink = buildCurrentLink(arguments.event,"prepareGroupSetImport")>
		<cfset local.doImportGroupSetLink = buildCurrentLink(arguments.event,"doImportGroupSets") & "&mode=stream">
		<cfset local.groupSetLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberGroupSetsJSON&meth=getGroupSetList&mode=stream">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_list.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="add" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfparam name="local.groupSet.groupSetID" default="">
		<cfparam name="local.groupSet.groupSetName" default="">

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfinclude template="frm_groupSet.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
	
		<!--- Build breadCrumb Trail --->
		<cfset appendBreadCrumbs(arguments.event,{ link='', text="Edit Group Set" })>
		
		<cfset local.groupSet = this.objMemberGroupSet.getGroupSet(arguments.event.getValue('gsID',0))>
		<cfset local.groupListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberGroupSetsJSON&meth=getGroupListByGroupSet&mode=stream&groupsetID=#arguments.event.getValue('gsID')#">
				
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_groupSet.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="save" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
				
		<cfif val(arguments.event.getValue('gsid',0)) gt 0>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdate">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @uid uniqueidentifier, @newuid uniqueidentifier, @groupSetID int;
					set @groupSetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('gsid',0)#">;

					BEGIN TRAN;
						UPDATE dbo.ams_memberGroupSets
						SET groupSetName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('groupSetName')#">
						WHERE groupSetID = @groupSetID;
				
						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.event.getTrimValue('gsUID',''))>
							select @uid = [uid] from dbo.ams_memberGroupSets where groupSetID = @groupSetID;
							set @newuid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.event.getTrimValue('gsUID','')#">;

							if @uid <> @newuid
								update dbo.ams_memberGroupSets
								set [uid] = @newuid
								where groupSetID = @groupSetID;
						</cfif>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.MCModalUtils.hideModal();
					top.reloadGroupSetsTable();							
				</script>
				</cfoutput>
			</cfsavecontent>			
		</cfif>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="checkGroupSetName" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="gsID" type="numeric" required="true">
		<cfargument name="groupSetName" type="string" required="true">

		<cfset var qryCheckGSName = "">

		<cfquery name="qryCheckGSName" datasource="#application.dsn.membercentral.dsn#">
			select mgs.groupsetID
			from dbo.ams_memberGroupSets mgs
			where mgs.orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
			AND mgs.groupsetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupSetName#">
			<cfif arguments.gsID neq 0>
				AND mgs.groupsetID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.gsID#">
			</cfif>
		</cfquery>

		<cfreturn { "success":true, "gsnameinuse":qryCheckGSName.recordCount GT 0 }>
	</cffunction>

	<cffunction name="saveMemberGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="groupSetID" type="numeric" required="true">
		<cfargument name="groupSetName" type="string" required="true">
		<cfargument name="groupSetUID" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>

		<cftry>
			<cfif arguments.groupSetID GT 0>
				<!--- Update existing group set --->
				<cfquery name="local.qryGetExisting" datasource="#application.dsn.membercentral.dsn#">
					SELECT groupsetName, [uid]
					FROM dbo.ams_memberGroupSets
					WHERE groupsetID = <cfqueryparam value="#arguments.groupSetID#" cfsqltype="CF_SQL_INTEGER">
					AND orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>

				<cfif local.qryGetExisting.recordCount>
					<cfset local.hasChanges = (local.qryGetExisting.groupsetName NEQ arguments.groupSetName) OR
											  (trim(local.qryGetExisting.uid) NEQ trim(arguments.groupSetUID))>

					<cfif local.hasChanges>
						<cfquery datasource="#application.dsn.membercentral.dsn#">
							UPDATE dbo.ams_memberGroupSets
							SET groupsetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupSetName#">,
								[uid] = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.groupSetUID)#" null="#NOT len(trim(arguments.groupSetUID))#">
							WHERE groupsetID = <cfqueryparam value="#arguments.groupSetID#" cfsqltype="CF_SQL_INTEGER">
							AND orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
					</cfif>
				</cfif>
			<cfelse>
				<!--- Create new group set --->
				<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createMemberGroupSet">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupSetName#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newGroupSetID">
				</cfstoredproc>
				<cfset arguments.groupSetID = local.newGroupSetID>
				<cfset local.hasChanges = true>

				<!--- Update UID if provided --->
				<cfif len(trim(arguments.groupSetUID))>
					<cfquery datasource="#application.dsn.membercentral.dsn#">
						UPDATE dbo.ams_memberGroupSets
						SET [uid] = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.groupSetUID)#">
						WHERE groupsetID = <cfqueryparam value="#arguments.groupSetID#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
				</cfif>
			</cfif>

			<cfset local.data = { "success":true, "groupsetid":arguments.groupSetID, "haschanges":local.hasChanges }>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = { "success":false }>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doCreateMemberGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="groupSetName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createMemberGroupSet">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupSetName#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.groupSetID">
			</cfstoredproc>
			<cfset local.data.gsid = local.groupSetID>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doRemoveGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="gsid" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.qryGroupSet = CreateObject("component","MemberGroupSets").getGroupSet(arguments.gsid)>

		<cftry>
			<cfif local.qryGroupSet.groupSetBeingUsed gt 0>
				<cfthrow message="In Use">
			</cfif>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.deleteGroups">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @gsid int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.gsid#">;

					IF NOT EXISTS (SELECT 1 FROM dbo.ams_memberGroupSets WHERE groupSetID = @gsid AND orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">)
						RAISERROR('Invalid Group Set',16,1);

					BEGIN TRAN;
						DELETE FROM dbo.ams_memberGroupSetGroups
						WHERE groupSetID = @gsid;
				
						DELETE FROM dbo.ams_memberGroupSets
						WHERE groupSetID = @gsid;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doLabelOverrideGroupInGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="gsgid" type="numeric" required="true">
		<cfargument name="gsid" type="numeric" required="true">
		<cfargument name="labeloverride" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.updateMemberGroup">
			UPDATE dbo.ams_memberGroupSetGroups
			SET	labelOverride = nullif(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.labelOverride#">,'')
			WHERE groupsetGroupID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.gsgid#">
			AND groupSetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.gsid#">
		</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addGroup" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.showSubscriptionsTab = arguments.event.getValue('mc_siteinfo.sf_subscriptions')>

		<cfset local.availableMemberGroupsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberGroupSetsJSON&meth=availableGroupSetGroups&mode=stream&groupsetID=#arguments.event.getValue('gsID')#">

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_addgroup.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="doAddGroupToGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="gsid" type="numeric" required="true">
		<cfargument name="gid" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createMemberGroupSetGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.gsid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.gid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.groupSetGroupID">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doRemoveGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="gsgid" type="numeric" required="true">
		<cfargument name="gsid" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemove">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @groupSetID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.gsid#">;

					BEGIN TRAN;
						DELETE gsg
						FROM dbo.ams_memberGroupSetGroups AS gsg
						INNER JOIN dbo.ams_memberGroupSets AS gs ON gs.groupSetID = gsg.groupSetID
							AND gs.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
						WHERE gsg.groupSetGroupID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.gsgid#">
						AND gsg.groupSetID = @groupSetID;

						EXEC dbo.ams_reorderMemberGroupSetGroups @groupSetID=@groupSetID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<!--- export/import --->
	<cffunction name="exportGroupSetStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "MemberGroupsetStructure.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_exportMemberGroupSetStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="false">
		</cfif>	
	</cffunction>

	<cffunction name="prepareGroupSetImport" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>
		
		<cfsetting requesttimeout="500">
	
		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>
			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/MemberGroupsetStructure.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/MemberGroupsetStructure.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>
		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/MemberGroupsetStructure.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name in ('sync_ams_memberGroupSets.bcp','sync_ams_memberGroupSetGroups.bcp')
				</cfquery>
				<cfif local.qryFiles.recordcount neq 2>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain five.">
				<cfelseif local.qryFilesCheck.theCount neq 2>
					<cfthrow message="Required files in the backup file is missing.">
				</cfif>
				<cfzip file="#local.strImportFile.strFolder.folderPath#/MemberGroupsetStructure.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/MemberGroupsetStructure.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>
  		<!--- prepare import --->
  		<cfif local.rs.success>
			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Member Group Sets Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>
			<cfset local.groupSetImportStruct = application.mcCacheManager.sessionGetValue('GroupSetImportStruct',{})>
			<cfset local.GroupSetImportStruct[local.threadID] = local.strImportFile.strFolder>
			<cfset application.mcCacheManager.sessionSetValue('GroupSetImportStruct',local.GroupSetImportStruct)>
			
			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareGroupSetsImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>
			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isGroupSetImportCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button> 
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfset local.data = list(event=arguments.event, prepResult=local.prepResult)>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareGroupSetsImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @orgID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.orgID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.ams_prepareMemberGroupSetImport @orgID=@orgID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showGroupSetImportCompareResults(orgID=arguments.paramStruct.orgID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/GroupSetImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showGroupSetImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>
		
		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewGroupSets = XMLSearch(arguments.strResult.importResultXML,"/import/newgroupsets/groupset")>
			<cfset local.strImportResult.arrUpdateGroupSets = XMLSearch(arguments.strResult.importResultXML,"/import/updategroupsets/groupset")>
			<cfset local.strImportResult.arrRemoveGroupSets = XMLSearch(arguments.strResult.importResultXML,"/import/removegroupsets/groupset")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif local.hasChanges>
				<cfset local.strImportResult.arrGroupSetsInUse = XMLSearch(arguments.strResult.importResultXML,"/import/groupsetsinuse/groupset")>
				<cfset local.importReport = generateGroupSetImportResultsReport(orgID=arguments.orgID, threadID=arguments.threadID, strImportResult=local.strImportResult)>
			</cfif>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
			<cfset local.errorReport = generateGroupSetImportErrorReport(orgID=arguments.orgID, arrErrors=local.arrErrors)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Member Group Sets Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelGroupSetImport(orgID=arguments.orgID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Member Group Sets Import No Action Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
					<br/>
				</cfif>
				<br/>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Member Group Sets Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateGroupSetImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateGroupSets)>
			<cfquery name="local.qryImportFileUpdateGroupSets" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct groupSetID as syncGroupSetID, groupSetName, [uid]
				from dbo.sync_ams_memberGroupSets
				where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateGroupSets" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT groupSetID, groupSetName, [uid]
				FROM dbo.ams_memberGroupSets
				WHERE orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				AND uid in (#listQualify(valueList(local.qryImportFileUpdateGroupSets.uid), "'")#)
				ORDER BY groupSetName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateGroupSetGroups" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select distinct sgsg.groupSetID as syncGroupSetID, sgsg.groupSetGroupID as syncGroupSetGroupID, sgsg.groupPathExpanded, 
					sgsg.groupUID, sgs.uid as syncGroupSetUID, sgsg.labelOverride, sgsg.groupOrder, sgsg.finalAction
				from dbo.sync_ams_memberGroupSetGroups as sgsg
				inner join dbo.sync_ams_memberGroupSets as sgs on sgs.orgID = @orgID and sgs.groupSetID = sgsg.groupSetID
					and sgs.finalAction = 'C'
				where sgsg.orgID = @orgID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateGroupSetGroups" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				SELECT mgs.groupSetID, mgsg.groupSetGroupID, g.[uid] as groupUID, g.groupPathExpanded, mgsg.labelOverride, mgsg.groupOrder
				FROM dbo.ams_memberGroupSets as mgs
				INNER JOIN dbo.ams_memberGroupSetGroups as mgsg on mgsg.groupSetID = mgs.groupSetID
				INNER JOIN dbo.ams_groups as g on g.orgID = @orgID and g.groupID = mgsg.groupID
				WHERE mgs.orgID = @orgID
				AND mgs.groupSetID in (0#valueList(local.qryOrgUpdateGroupSets.groupSetID)#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>		

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateGroupSetImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="arrErrors" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doImportGroupSets" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">

		<cfsetting requesttimeout="500">
		<cfset local.GroupSetImportStruct = structNew()>
		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>
		<cfset local.strHasError = 0>
		<cfset local.groupSetImportStruct = application.mcCacheManager.sessionGetValue('GroupSetImportStruct',{})>
		<cfif NOT isStruct(local.GroupSetImportStruct) OR NOT structKeyExists(local.GroupSetImportStruct,local.threadID)>
			<cfset local.strHasError = 1>
		</cfif>
		<cfif local.strHasError eq 1>
			<cfset local.resultMessage = "There was a problem importing the Member Group Sets. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfstoredproc procedure="ams_importMemberGroupSets" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Member Group Sets file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- when done, remove from session --->
			<cfif structCount(local.GroupSetImportStruct) gt 0 >
				<cfset StructDelete(local.GroupSetImportStruct, local.threadID)>				
				<cfset application.mcCacheManager.sessionSetValue('GroupSetImportStruct',local.GroupSetImportStruct)>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelGroupSetImport" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_ams_memberGroupSets WHERE orgID = @orgID;
					DELETE FROM dbo.sync_ams_memberGroupSetGroups WHERE orgID = @orgID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		<cftry>
			<cfset local.GroupSetImportStruct = application.mcCacheManager.sessionGetValue('GroupSetImportStruct',{})>
			<cfif isStruct(local.GroupSetImportStruct) and structKeyExists(local.GroupSetImportStruct,arguments.reportuid)>
				<cfset local.reportFileName = local.GroupSetImportStruct[arguments.reportuid].folderPath & "/GroupSetImportReport.html">
				<cfset local.returnStruct.reportOutput = "">
				<cfif fileExists(local.reportFileName)>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>	
	
	<cffunction name="previewGroupSet" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryGroupSet = this.objMemberGroupSet.getGroupSet(arguments.event.getValue('gsID',0))>

		<!--- if a superuser, pick a random membernumber from the org instead of their own, since they aren't a member in this org --->
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('mc_siteinfo.orgID') is not 1>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetMemberNumber">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT TOP 1 memberNumber
				FROM dbo.ams_members
				WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
				AND status = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.memberNumberForPreview = local.qryGetMemberNumber.memberNumber>
		<cfelse>
			<cfset local.memberNumberForPreview = session.cfcUser.memberData.memberNumber>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_previewGroupset.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>