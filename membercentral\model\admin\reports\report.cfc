<cfcomponent extends="model.admin.admin" output="no">

	<cffunction name="reportController" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		// get the SRID and permissions of Site to grab the ManageAdvancedSettings permission
		local.SiteSRID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');
		local.myRightsSite = buildRightAssignments(local.SiteSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

		// set rights into this scope so we dont have to pass it everywhere
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		structInsert(local.tmpRights,"ManageAdvancedSettings",local.myRightsSite.ManageAdvancedSettings);
		this.myReportRights = local.tmpRights;

		// put report into event
		arguments.event.setValue('qryReportInfo',getReportInfo(rptid=arguments.event.getValue('rptId',0), toolTypeID=arguments.event.getValue('mca_tt',''), siteResourceID=this.siteResourceID));

		// links
		this.link.showReport = buildCurrentLink(arguments.event,"showReport");
		this.link.runReport = buildCurrentLink(arguments.event,"runReport") & "&mode=stream";
		this.link.showSavedReports = buildCurrentLink(arguments.event,"showSavedReports") & "&mode=direct";
		this.link.copyReport = buildCurrentLink(arguments.event,"copyReport") & "&mode=stream";
		this.link.saveNewReport = buildCurrentLink(arguments.event,"saveNewReport") & "&mode=stream";
		this.link.saveFieldsetSettings = buildCurrentLink(arguments.event,"saveFieldsetSettings") & "&mode=stream";
		this.link.csvSettings = buildCurrentLink(arguments.event,"csvSettings") & "&mode=stream";
		this.link.csvSaveSettings = buildCurrentLink(arguments.event,"csvSaveSettings") & "&mode=stream";
		this.link.saveReportExtra = buildCurrentLink(arguments.event,"saveReportExtra") & "&mode=stream";
		this.link.listReportRuleVersions = buildCurrentLink(arguments.event,"listReportRuleVersions") & "&rptID=#arguments.event.getValue('rptID',0)#&mode=direct";

		this.link.addReferralPanel = buildCurrentLink(arguments.event,"addReferralPanel") & "&mode=direct";
		this.link.saveMemberHistoryPanelFilter = buildCurrentLink(arguments.event,"saveMemberHistoryPanelFilter") & "&mode=direct";
		
		this.link.removeGroupset = buildCurrentLink(arguments.event,"removeGroupset") & "&mode=stream";
		this.link.addGroupset = buildCurrentLink(arguments.event,"addGroupset") & "&mode=stream";		

		local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);
		this.reportImageExt = xmlSearch(local.appInstanceSettings.settingsXML,'string(/settings/setting[@name="reportHeaderImage"]/@value)');

		// append report specific breadcrumbs
		if (NOT listFindNoCase('stream,direct',arguments.event.getValue("mc_pageDefinition.layoutmode"))) {
			reportBreadcrumbs(event=arguments.event);
		}

		// reports can be scheduled unless overridden in the report itself
		if (NOT isDefined("variables.AllowScheduling")) variables.AllowScheduling = true;
		</cfscript>
	</cffunction>

	<cffunction name="getReportInfo" access="public" output="false" returntype="query">
		<cfargument name="rptId" type="numeric" required="yes">
		<cfargument name="toolTypeID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryReport" datasource="#application.dsn.membercentral.dsn#">
			select rsr.reportID, rsr.ruleID, rsr.reportName, 
				rsr.memberID, tt.toolType, rsr.otherXML, rsr.isReadOnly,
				(isnull((
					select fieldset.fieldsetid, fieldset.fieldsetname, fieldset.uid
					from dbo.rpt_SavedReports as sr
					CROSS APPLY otherXML.nodes('/report/fieldsets/fieldset') as F(fs)
					INNER JOIN dbo.ams_memberFieldSets as fieldset on fieldset.uid = F.fs.value('@uid','uniqueidentifier')
					where sr.reportID = rsr.reportID
					FOR XML AUTO, ROOT('fieldsets'), TYPE
				),'<fieldsets/>')) as fieldsetsXML,
				(isnull((
					select fieldset.fieldsetid, fieldset.fieldsetname, fieldset.uid
					from dbo.rpt_SavedReports as sr
					CROSS APPLY otherXML.nodes('/report/linkedfieldsets/fieldset') as F(fs)
					INNER JOIN dbo.ams_memberFieldSets as fieldset on fieldset.uid = F.fs.value('@uid','uniqueidentifier')
					where sr.reportID = rsr.reportID
					FOR XML AUTO, ROOT('linkedfieldsets'), TYPE
				),'<linkedfieldsets/>')) as linkedfieldsetsXML,
				(isnull((
					select 
						groupset.groupSetID,
						groupset.groupSetName,
						groupset.uid
					from dbo.rpt_SavedReports as sr
					CROSS APPLY otherXML.nodes('/report/groupsets/groupset') as G(gs)
					INNER JOIN dbo.ams_memberGroupSets as groupset
						on groupset.uid = G.gs.value('@uid','uniqueidentifier')
					where sr.reportID = rsr.reportID
					FOR XML AUTO, ROOT('groupsets'), TYPE
				),'<groupsets/>')) as groupsetXML,
				logData.runCount, logData.lastRunDate, logData.lastRunBy
			from dbo.rpt_SavedReports as rsr
			inner join dbo.admin_toolTypes tt on tt.toolTypeID = rsr.toolTypeID
			outer apply (
				select tmp.runCount, runlog.dateRun as lastRunDate, m2.firstname + ' ' + m2.lastname as lastRunBy
				from (
					select count(*) as runCount, max(logID) as lastRunID
					from platformstatsMC.dbo.rpt_runLog
					where reportID = rsr.reportID
				) as tmp
				inner join platformstatsMC.dbo.rpt_runLog as runlog on runlog.logID = tmp.lastRunID
				inner join membercentral.dbo.ams_members as m on m.memberID = runlog.memberID
				inner join membercentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
				) as logData
			where rsr.reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rptId#">
			and rsr.toolTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.toolTypeID#">
			and rsr.controllingSiteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
		</cfquery>

		<cfreturn local.qryReport>
	</cffunction>

	<cffunction name="reportBreadcrumbs" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfscript>
		if (isDefined('session.mcastruct.strNavKeys') and NOT structIsEmpty(session.mcastruct.strNavKeys)) {
			// if we have mca_lt this is level 4 tool
			if (arguments.event.getValue('mca_lt','') gt 0) {
				if (arguments.event.valueExists('rptId') and arguments.event.getValue('qryReportInfo').recordcount is 1) {
					appendBreadCrumbs(arguments.event,{ link="/?pg=admin&mca_s=#arguments.event.getValue('mca_s','')#&mca_a=#arguments.event.getValue('mca_a','')#&mca_tt=#arguments.event.getValue('mca_tt','')#&mca_ta=#arguments.event.getValue('mca_ta','')#&mca_lt=#arguments.event.getValue('mca_lt','')#", text=arguments.event.getValue('mc_adminNav.currentNavigationItem.navName') });
					appendBreadCrumbs(arguments.event,{ link='', text=htmlEditFormat(arguments.event.getValue('qryReportInfo').reportName) });
				} else {
					appendBreadCrumbs(arguments.event,{ link='', text=arguments.event.getValue('mc_adminNav.currentNavigationItem.navName') });
				}
			
			// else this is a level 3 tool
			} else {
				if (arguments.event.valueExists('rptId') and arguments.event.getValue('qryReportInfo').recordcount is 1) {
					appendBreadCrumbs(arguments.event,{ link="/?pg=admin&mca_s=#arguments.event.getValue('mca_s','')#&mca_a=#arguments.event.getValue('mca_a','')#&mca_tt=#arguments.event.getValue('mca_tt','')#&mca_ta=#arguments.event.getValue('mca_ta','')#", text=arguments.event.getValue('mc_adminNav.currentNavigationItem.navName') });
					appendBreadCrumbs(arguments.event,{ link='', text=htmlEditFormat(arguments.event.getValue('qryReportInfo').reportName) });
				} else {
					appendBreadCrumbs(arguments.event,{ link='', text=arguments.event.getValue('mc_adminNav.currentNavigationItem.navName') });
				}
			}
		}
		</cfscript>
	</cffunction>

	<cffunction name="showCommonTop" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>

		<cfif NOT arguments.event.valueExists('rptID') OR arguments.event.getValue('qryReportInfo').recordcount is 0>
			<cfquery name="local.qryCountSavedReports" datasource="#application.dsn.memberCentral.dsn#">
				select count(*) as theCount
				from dbo.rpt_SavedReports sr
				inner join dbo.admin_toolTypes tt on tt.toolTypeID = sr.toolTypeID 
					and sr.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
					and tt.toolTypeID = <cfqueryparam value="#arguments.event.getValue('mca_tt',0)#" cfsqltype="CF_SQL_INTEGER">
					and sr.controllingSiteResourceID = <cfqueryparam value="#this.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfset local.resultsList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=reportsJSON&meth=getSavedReports&mode=stream&tt=#arguments.event.getValue('mca_tt')#&srID=#this.siteResourceID#">
		<cfelse>
			<cfset local.strScheduledReports = getScheduledReports(mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteid'), reportID=arguments.event.getValue('qryReportInfo').reportID)>
		</cfif>

		<cfset local.canEditReport = hasReportEditRights(event=arguments.event, disregardReadOnlySetting=1)>
		<cfif local.canEditReport>
			<cfset local.editScheduledReportLink = buildLinkToTool(toolType='rpt_SavedReports',mca_ta='editScheduledReport') & "&mode=direct">
		</cfif>

		<cfinclude template="dsp_commonTop.cfm">

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="showButtonBar" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfargument name="validateFunction" type="string" required="no" default="">
	
		<cfset var local = structNew()>

		<cfset local.canEditReport = hasReportEditRights(event=arguments.event)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_buttonbar.cfm">
		</cfsavecontent>

		<cfreturn application.objCommon.minText(local.data)>
	</cffunction>	

	<cffunction name="showRawSQL" access="private" output="false" returntype="string">
		<cfargument name="reportAction" type="string" required="yes">
		<cfargument name="qryName" type="string" required="yes">
		<cfargument name="strQryResult" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = "">
		
		<cfif listFindNoCase("screen,customcsv",arguments.reportAction) and structKeyExists(session,"cfcuser") and application.objUser.isSuperUser(cfcuser=session.cfcuser) and isDefined("this.myReportRights.ManageAdvancedSettings") and this.myReportRights.ManageAdvancedSettings is 1>
			<cftry>
				<cfobject action="CREATE" type="JAVA" class="coldfusion.server.ServiceFactory" name="local.factory">
				<cfset local.cfdebugger = local.factory.getDebuggingService()>
				<cfset local.qEvents = local.cfdebugger.getDebugger().getData()>
				<cfquery dbType="query" name="local.cfdebug_queries" debug="false" maxrows="1">
					SELECT *, (endTime - startTime) AS executionTime
					FROM [local].qEvents
					WHERE type = 'SqlQuery'
					AND name = '#arguments.qryName#'
				</cfquery>
				<cfif local.cfdebug_queries.recordCount eq 1 and not len(trim(local.cfdebug_queries.executionTime))>
					<cfset querySetCell(local.cfdebug_queries, "executionTime", "0", 1)>
				</cfif>
				<cfset local.sql = trim(arguments.strQryResult.sql)>
				<cfloop array="#local.cfdebug_queries.attributes#" index="local.thisParam">
					<cfswitch expression="#local.thisParam.sqlType#">
						<cfcase value="cf_sql_timestamp,cf_sql_date,cf_sql_integer" delimiters=",">
							<cfset local.thisParamVal = local.thisParam.value>
						</cfcase>
						<cfcase value="cf_sql_bit">
							<cfif local.thisParam.value>
								<cfset local.thisParamVal = "'1'">
							<cfelse>
								<cfset local.thisParamVal = "'0'">
							</cfif>
						</cfcase>
						<cfdefaultcase>
							<cfset local.thisParamVal = "'#local.thisParam.value#'">
						</cfdefaultcase>
					</cfswitch>
					<cfset local.sql = replace(local.sql,"?",local.thisParamVal)>
				</cfloop>
				<cfcatch type="Any">
					<cfset local.sql = trim(arguments.strQryResult.sql)>
					<cfif structKeyExists(arguments.strQryResult,"SQLPARAMETERS")>
						<cfloop array="#arguments.strQryResult.SQLPARAMETERS#" index="local.thisParam">
							<cfset local.sql = replace(local.sql,"?",local.thisParam)>
						</cfloop>
					</cfif>
				</cfcatch>
			</cftry>

			<cfsavecontent variable="local.data">
				<cfoutput>
				<br/><br/><a href="##" onclick="javascript:$('##sqlView').toggle();$('html,body').animate({scrollTop: $('##sqlView').offset().top},100);">Toggle SQL</a><br/>
				<div id="sqlView" class="mt-3" style="display:none;"><textarea class="form-control form-control-sm" rows="8">#local.sql#</textarea></div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>	

	<cffunction name="copyReport" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" required="yes">
		
		<cfset var local = structNew()>

		<cfstoredproc procedure="rpt_copySavedReport" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.event.getValue('rptId',0)#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="out" variable="local.newReportID">
		</cfstoredproc>

		<cfset local.reportLink = buildLinkToTool(toolType='#arguments.event.getValue('toolType','')#',mca_ta='showReport')>
		<cflocation url="#local.reportLink#&rptID=#local.newReportID#" addtoken="no">
	</cffunction>
					
	<cffunction name="showStepMemberCriteria" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="title" type="string" required="no" default="Define Report Filter">
		<cfargument name="desc" type="string" required="no" default="">
		<cfargument name="gridwidth" type="numeric" required="no" default="660">
		<cfargument name="gridheight" type="numeric" required="no" default="150">
		<cfargument name="gridNum" type="numeric" required="true" default="1">
	
		<cfset var local = structNew()>
		<!--- get ruleID for report --->
		<cfset local.ruleID = val(arguments.event.getValue('qryReportInfo').ruleID)>

		<!--- hide icons if unable to change report --->
		<cfset local.readOnly = NOT hasReportEditRights(event=arguments.event)>

		<cfset local.qryRuleVersions = CreateObject("component","model.admin.virtualGroups.virtualGroups").getRuleVersions(orgID=arguments.event.getValue('mc_siteInfo.orgID'),
			ruleID=local.ruleID, mode='ruleversions', committedVersionsOnly=1)>

		<cfset local.data = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").showStepMemberCriteria(orgID=arguments.event.getValue('mc_siteInfo.orgID'),
				ruleID=local.ruleID, title=arguments.title, desc=arguments.desc, gridNum=arguments.gridNum, gridwidth=arguments.gridwidth, gridheight=arguments.gridheight,
				readOnly=local.readOnly, enableSimpleRuleBuilder=true, saveRuleVersionMessage='Run Report to Save Changes', usageMode="report",
				showRuleVersionsFn=local.qryRuleVersions.recordCount gt 1 ? "viewReportRuleVersionsList" : "")>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="listReportRuleVersions" access="public" output="false" returntype="struct" hint="List Rule Versions">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.ruleID = int(val(arguments.event.getValue('ruleID',0)))>
		<cfset local.qryRule = CreateObject("component","model.admin.virtualGroups.virtualGroups").getRules(orgID=arguments.event.getValue('mc_siteInfo.orgID'), ruleID=local.ruleID)>
		<cfset local.ruleVersionsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=virtualGroupsJSON&meth=getRuleVersions&ruleID=#local.ruleID#&committedVersionsOnly=1&mode=stream">
		<cfset local.viewReportRuleVersionLink = buildCurrentLink(arguments.event,"viewReportRuleVersion") & '&rptID=#arguments.event.getValue('rptID',0)#&ruleID=#local.qryRule.ruleID#&mode=direct'>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_reportRuleVersions.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewReportRuleVersion" access="public" output="false" returntype="struct" hint="View Rule Version">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objVirtualGroups = CreateObject("component","model.admin.virtualGroups.virtualGroups")>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgID')>
		<cfset local.reportID = arguments.event.getValue('rptID',0)>
		<cfset local.ruleID = int(val(arguments.event.getValue('ruleID',0)))>
		<cfset local.qryRule = local.objVirtualGroups.getRules(orgID=local.orgID, ruleID=local.ruleID, ruleVersionID=val(arguments.event.getValue('ruleVersionID',0)))>

		<cfset var ruleVersionID = local.qryRule.ruleVersionID>
		<cfset local.ruleVersionIDList = listRemoveDuplicates("#local.qryRule.activeVersionID#,#ruleVersionID#")>
		<cfset local.qryRuleVersions = local.objVirtualGroups.getRuleVersions(orgID=local.orgID, ruleID=local.ruleID, ruleVersionIDList=local.ruleVersionIDList, mode='ruleversions')>
		<cfset local.qryActiveRuleVersion = local.qryRuleVersions.filter(function(thisRow) { return arguments.thisRow.isActiveVersion EQ 1; })>
		<cfset local.qryRuleVersion = local.qryRuleVersions.filter(function(thisRow) { return arguments.thisRow.ruleVersionID EQ ruleVersionID; })>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_reportRuleVersion.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="activateReportRuleVersion" access="public" output="false" returntype="struct" hint="Activate Rule Version">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objVirtualGroupsAdmin = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin")>
		<cfset local.qryRule = CreateObject("component","model.admin.virtualGroups.virtualGroups").getRules(orgID=arguments.mcproxy_orgID, ruleID=arguments.ruleID)>

		<cftry>
			<cfif arguments.ruleVersionID eq 0 OR NOT hasEditReportRightsByReportID(siteID=arguments.mcproxy_siteID, reportID=arguments.reportID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.objVirtualGroupsAdmin.acceptRuleVersion(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>
			<cfif local.qryRule.isActive eq 0>
				<cfset local.objVirtualGroupsAdmin.activateRule(ruleID=arguments.ruleID, forceCache=true)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showStepReferralPanelCriteria" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="title" type="string" required="no" default="Define Report Criteria">
		<cfargument name="desc" type="string" required="no" default="">
		<cfargument name="gridwidth" type="numeric" required="no" default="660">
		<cfargument name="gridheight" type="numeric" required="no" default="150">

		<cfset var local = structNew()>
		<cfset local.reportID = arguments.event.getValue('qryReportInfo').reportID>
		<cfset local.reportTT = arguments.event.getValue('mca_tt')>

		<!--- links --->
		<cfset local.referralPanelList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=reportsJSON&meth=getReferralPanelsInReport&mode=stream&rptID=#local.reportID#&rptTT=#local.reportTT#&srID=#this.siteResourceID#">
		
		<!--- hide icons if unable to change report --->
		<cfif NOT hasReportEditRights(event=arguments.event)>
			<cfset local.referralPanelList = local.referralPanelList & '&rohi=1'>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_referralPanelCriteria.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>	

	<cffunction name="showStepFieldsets" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="title" type="string" required="no" default="Select Field Sets to Include in This Report">
		<cfargument name="desc" type="string" required="no" default="">
		<cfargument name="mode" type="numeric" required="no" default="1">
	
		<cfset var local = structNew()>
		<cfset local.qryReportInfo = arguments.event.getValue('qryReportInfo')>

		<cfif arguments.mode is 2>
			<cfset local.fieldsetNodeName = "linkedfieldsets">
		<cfelse>
			<cfset local.fieldsetNodeName = "fieldsets">
		</cfif>

		<cfset local.qryAvailableFieldsets = getAvailableFieldSetsForSavedReport(siteID=arguments.Event.getValue('mc_siteinfo.siteID'), rptID=local.qryReportInfo.reportID, fieldsetNodeName=local.fieldsetNodeName)>
		<cfset local.arrSelectedFieldsets = XMLSearch(local.qryReportInfo["#local.fieldsetNodeName#XML"][1],"/#local.fieldsetNodeName#/fieldset")>
		
		<cfset local.ovNameFormat = XMLSearch(local.qryReportInfo.otherXML[1],"string(/report/#local.fieldsetNodeName#/@nf)")>
		<cfif NOT len(local.ovNameFormat)>
			<cfset local.ovNameFormat = "LSXPFM">
		</cfif>

		<cfset local.ovShowPhotos = XMLSearch(local.qryReportInfo.otherXML[1],"string(/report/#local.fieldsetNodeName#/@img)")>
		<cfif NOT len(local.ovShowPhotos)>
			<cfset local.ovShowPhotos = "0">
		</cfif>
	
		<cfset local.ovShowMemberNumber = XMLSearch(local.qryReportInfo.otherXML[1],"string(/report/#local.fieldsetNodeName#/@mn)")>
		<cfif NOT len(local.ovShowMemberNumber)>
			<cfset local.ovShowMemberNumber = "0">
		</cfif>

		<cfset local.ovShowMemberCompany = XMLSearch(local.qryReportInfo.otherXML[1],"string(/report/#local.fieldsetNodeName#/@mc)")>
		<cfif NOT len(local.ovShowMemberCompany)>
			<cfset local.ovShowMemberCompany = "0">
		</cfif>

		<cfset local.ovMask = XMLSearch(local.qryReportInfo.otherXML[1],"string(/report/#local.fieldsetNodeName#/@mk)")>
		<cfif NOT len(local.ovmask)>
			<cfset local.ovMask = "0">
		</cfif>

		<cfif local.qryAvailableFieldsets.recordcount or arrayLen(local.arrSelectedFieldsets)>
			<cfset local.hasEditRights = hasReportEditRights(event=arguments.event)>

			<cfset local.strReportFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getMultipleFieldSetSelector(
				selectorID="fsSelectorReport_#local.fieldsetNodeName#",
				getFieldSetDataFunc="getAvailableAndSelectedFieldSets_#local.fieldsetNodeName#",
				addFieldSetUsageFunc="createMemberFieldUsage_#local.fieldsetNodeName#",
				removeFieldSetUsageFunc="removeMemberFieldUsage_#local.fieldsetNodeName#",
				usageMode="fsWidgetMultipleSavedRpt",
				hasEditRights=local.hasEditRights)>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_fieldsets.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "">
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAvailableAndSelectedFieldSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="rptID" type="numeric" required="true">
		<cfargument name="toolTypeID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="fsNodeName" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": true, "availablefieldsetscount": 0, "arravailablefieldsets": [], "arrselectedfieldsets": [] }>

		<cfset local.qryReportInfo = getReportInfo(rptid=arguments.rptID, toolTypeID=arguments.toolTypeID, siteResourceID=arguments.siteResourceID)>
		<cfset local.qryAvailableFieldSets = getAvailableFieldSetsForSavedReport(siteID=arguments.mcproxy_siteID, rptID=local.qryReportInfo.reportID, fieldsetNodeName=arguments.fsNodeName)>
		<cfset local.arrSelectedFieldSets = XMLSearch(local.qryReportInfo["#arguments.fsNodeName#XML"][1],"/#arguments.fsNodeName#/fieldset")>
		
		<cfset local.returnStruct["availablefieldsetscount"] = local.qryAvailableFieldSets.recordCount>

		<cfoutput query="local.qryAvailableFieldSets" group="categoryID">
			<cfset local.tmpStr = { "categoryid":local.qryAvailableFieldSets.categoryID, "categoryname":local.qryAvailableFieldSets.categoryName, "arrfieldsets":[] }>
			<cfoutput>
				<cfset arrayAppend(local.tmpStr.arrfieldsets,{ "fieldsetid":local.qryAvailableFieldSets.fieldsetID, "fieldsetname":local.qryAvailableFieldSets.fieldsetName, "fieldsetuid":local.qryAvailableFieldSets.fieldSetUID })>
			</cfoutput>
			<cfset arrayAppend(local.returnStruct.arravailablefieldsets,local.tmpStr)>
		</cfoutput>

		<cfloop from="1" to="#Arraylen(local.arrSelectedFieldSets)#" index="local.thisFSidx">
			<cfset local.tmpStr = {
				"useid": 0,
				"fieldsetid": local.arrSelectedFieldSets[local.thisFSidx].xmlAttributes.fieldsetID,
				"fieldsetname": local.arrSelectedFieldSets[local.thisFSidx].xmlAttributes.fieldsetname,
				"fieldsetuid": local.arrSelectedFieldSets[local.thisFSidx].xmlAttributes.uid,
				"hasrights": 0,
				"usesiteresourceid": 0,
				"hasremoveusagerights": 1
			}>
			<cfset arrayAppend(local.returnStruct.arrselectedfieldsets,local.tmpStr)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addFieldSetToSavedReport" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="fieldSetUID" type="string" required="true">
		<cfargument name="fieldSetNodeName" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditReportRightsByReportID(siteID=arguments.mcproxy_siteID, reportID=arguments.reportID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetUpdatedXML">
				DECLARE @otherXML xml, @logMsg varchar(max),
					@fieldSetUID uniqueidentifier = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#ucase(arguments.fieldSetUID)#">;

				SELECT @logMsg = 'Field Set [' + fieldsetName + '] has been added to the selections.'
				FROM dbo.ams_memberFieldSets
				WHERE siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">
				AND [uid] = @fieldSetUID;

				SELECT @otherXML = otherXML
				FROM dbo.rpt_savedReports
				WHERE reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">;

				SET @otherXML.modify('
					insert <fieldset uid="{sql:variable("@fieldSetUID")}" />
					into (/report/#arguments.fieldSetNodeName#)[1]
				');

				SELECT @otherXML AS otherXML, @logMsg AS logMsg;
			</cfquery>

			<cfif len(local.qryGetUpdatedXML.otherXML)>
				<cfset updateSavedReportWithOtherXML(reportID=arguments.reportID, otherXML=local.qryGetUpdatedXML.otherXML,
					changeSection="Field Sets Selection", arrayLogMessages=[local.qryGetUpdatedXML.logMsg])>
			</cfif>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeFieldSetFromSavedReport" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="fieldSetUID" type="string" required="true">
		<cfargument name="fieldSetNodeName" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditReportRightsByReportID(siteID=arguments.mcproxy_siteID, reportID=arguments.reportID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetUpdatedXML">
				DECLARE @otherXML xml, @logMsg varchar(max),
					@fieldSetUID uniqueidentifier = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#ucase(arguments.fieldSetUID)#">;
				
				SELECT @logMsg = 'Field Set [' + fieldsetName + '] has been removed from the selections.'
				FROM dbo.ams_memberFieldSets
				WHERE siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">
				AND [uid] = @fieldSetUID;

				SELECT @otherXML = otherXML
				FROM dbo.rpt_savedReports
				WHERE reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">;

				SET @otherXML.modify('delete /report/#arguments.fieldSetNodeName#/fieldset[@uid=sql:variable("@fieldSetUID")]');

				SELECT @otherXML AS otherXML, @logMsg AS logMsg;
			</cfquery>

			<cfif len(local.qryGetUpdatedXML.otherXML)>
				<cfset updateSavedReportWithOtherXML(reportID=arguments.reportID, otherXML=local.qryGetUpdatedXML.otherXML,
					changeSection="Field Sets Selection", arrayLogMessages=[local.qryGetUpdatedXML.logMsg])>
			</cfif>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasReportEditRights" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">
		<cfargument name="disregardReadOnlySetting" type="numeric" required="false" default="0">

		<cfreturn (arguments.disregardReadOnlySetting eq 1 OR val(arguments.event.getValue('qryReportInfo').isReadOnly) eq 0)
			AND (this.myReportRights.EditAnyReport or (this.myReportRights.EditOwnReport and arguments.event.getValue('qryReportInfo').memberid eq session.cfcuser.memberdata.memberid))>
	</cffunction>

	<cffunction name="hasEditReportRightsByReportID" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="disregardReadOnlySetting" type="numeric" required="false" default="0">

		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryCheckEditRights" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @reportID int, @reportSRID int, @rightsXML xml, @siteID int,
					@isReadOnly bit, @ownerMemberID int, @actorMemberID int, @hasPermission bit = 0;
				
				SET @reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @actorMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
				
				SELECT @reportSRID = controllingSiteResourceID, @ownerMemberID = memberID, @isReadOnly = isReadOnly
				FROM dbo.rpt_SavedReports
				WHERE siteID = @siteID
				AND reportID = @reportID;

				IF @reportSRID IS NULL
					RAISERROR('invalid request.', 16, 1);
				
				SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@reportSRID,@actorMemberID,@siteID);

				<cfif arguments.disregardReadOnlySetting>
					SET @isReadOnly = 0;
				</cfif>

				IF @isReadOnly = 0 AND (
					@rightsXML.exist('/rights/right[@functionName="EditAnyReport"][@allowed="1"]') = 1 OR
					(@rightsXML.exist('/rights/right[@functionName="EditOwnReport"][@allowed="1"]') = 1 AND @actorMemberID = @ownerMemberID)
				)
					SET @hasPermission = 1;

				SELECT @hasPermission AS hasPermission;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.hasPermission = val(local.qryCheckEditRights.hasPermission) eq 1>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.hasPermission = false>
		</cfcatch>
		</cftry>

		<cfreturn local.hasPermission>
	</cffunction>
	
	<cffunction name="showStepGroupsets" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="title" type="string" required="no" default="Select Group Sets to Include in This Report">
		<cfargument name="desc" type="string" required="no" default="">
	
		<cfset var local = structNew()>
		<cfset local.arrSelectedGroupsets = XMLSearch(arguments.event.getValue('qryReportInfo').groupsetXML,"groupsets/groupset")>
		<cfset local.qryAvailableGroupsets = getAvailableGroupSetsForSavedReport(arguments.Event)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_groupsets.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>		

	<cffunction name="showGroupsetGridSelected" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="arrSelectedGroupsets" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.SELGRID">
			<cfoutput>
			<cfif Arraylen(arguments.arrSelectedGroupsets) is 0>
				<li class="list-group-item d-flex align-items-center">
					<em>none selected</em>
				</li>
			<cfelse>	
				<cfloop from="1" to="#Arraylen(arguments.arrSelectedGroupsets)#" index="local.thisFSidx">
					<cfif hasReportEditRights(event=arguments.event)>
						<li class="list-group-item list-group-item-action d-flex align-items-center">
							<a href="javascript:removeGroupset('#arguments.arrSelectedGroupsets[local.thisFSidx].xmlAttributes.uid#');">
								#arguments.arrSelectedGroupsets[local.thisFSidx].xmlAttributes.groupsetname#
								<span>
									<i class="fa-solid fa-angle-right"></i>
								</span>
							</a>
						</li>
					<cfelse>
						<li class="list-group-item d-flex align-items-center">
							#arguments.arrSelectedGroupsets[local.thisFSidx].xmlAttributes.groupsetname#
						</li>
					</cfif>
				</cfloop>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.SELGRID>
	</cffunction>

	<cffunction name="showGroupsetGridAvailable" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="qryAvailableGroupsets" type="query" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.AVAILGRID">
			<cfoutput>
			<cfif arguments.qryAvailableGroupsets.recordcount is 0>
				<li class="list-group-item d-flex align-items-center">
					<em>none available</em>
				</li>
			<cfelse>
				<cfloop query="arguments.qryAvailableGroupsets">
					<cfif hasReportEditRights(event=arguments.event)>
						<li class="list-group-item list-group-item-action d-flex align-items-center">
							<a href="javascript:addGroupset('#arguments.qryAvailableGroupsets.uid#');">
								<span class="pr-3">
									<i class="fa-solid fa-angle-left"></i>
								</span>
								#arguments.qryAvailableGroupsets.groupsetname#
							</a>
						</li>
					<cfelse>
						<li class="list-group-item d-flex align-items-center">
							#arguments.qryAvailableGroupsets.groupsetname#
						</li>
					</cfif>
				</cfloop>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.AVAILGRID>
	</cffunction>

	<cffunction name="addReferralPanel" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.reportID = arguments.event.getValue('qryReportInfo').reportID>
		<cfset local.reportTT = arguments.event.getValue('mca_tt')>
		<cfset local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML)>
		<cfset local.siteResourceID = this.siteResourceID>
		
		<cfset local.referralPanelList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=reportsJSON&meth=getReferralPanels&mode=stream&rptID=#local.reportID#&rptTT=#local.reportTT#&srID=#local.siteResourceID#">

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_referralPanel.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveReferralPanelFilter" access="public" output="true" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="rptId" type="numeric" required="true">
		<cfargument name="rptTT" type="string" required="true">
		<cfargument name="rplist" type="string" required="true">
		<cfargument name="csrID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditReportRightsByReportID(siteID=arguments.mcproxy_siteID, reportID=arguments.rptId)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset arguments.rplist= listRemoveDuplicates(arguments.rplist)>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetOtherXML">
				DECLARE @referralID int, @panelNameList varchar(max);
				
				SELECT TOP 1 @referralID = r.referralID
				FROM dbo.ref_referrals AS r
				INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
					AND sr.siteResourceStatusID = 1
				WHERE ai.siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">;

				SELECT @panelNameList = COALESCE(@panelNameList + ', ' ,'') + '[' + p.thePathExpanded + ']'
				FROM dbo.fn_getRecursiveReferralPanels(@referralID,NULL) AS p
				INNER JOIN dbo.fn_intListToTable(<cfqueryparam value="0#arguments.rplist#" cfsqltype="CF_SQL_VARCHAR">,',') AS dg ON dg.listitem = p.panelID
				ORDER BY p.thePathExpanded;

				SELECT otherXML, 'Filter now includes the following panel(s): ' + @panelNameList AS logMsg
				FROM dbo.rpt_savedReports
				WHERE reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rptId#">
				AND toolTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rptTT#">
				AND controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrID#">;
			</cfquery>

			<cfset local.otherXML = XMLParse(local.qryGetOtherXML.otherXML)>
			
			<!--- get a handle on the extra node --->
			<cfset local.extraNode = XMLSearch(local.otherXML,"/report/extra")>
			<cfset local.rplistNode = XMLSearch(local.extraNode[1],"rplist")>
			
			<!--- add rplist node if doesnt exist --->
			<cfif NOT arrayLen(local.rplistNode)>
				<cfset arrayAppend(local.extraNode[1].xmlChildren,XMLElemNew(local.otherXML,"rplist"))>
				<cfset local.rplistNode = XMLSearch(local.extraNode[1],"rplist")>
			</cfif>

			<!--- set new value --->
			<cfset local.rplistNode[1].xmlText = arguments.rplist>

			<!--- remove the <xml> tag, specifically the encoding. It breaks under Railo. --->
			<cfset local.otherXML = replaceNoCase(toString(local.otherXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfset updateSavedReportWithOtherXML(reportID=arguments.rptId, otherXML=local.otherXML,
				changeSection="Referral Panel Filter", arrayLogMessages=[local.qryGetOtherXML.logMsg])>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="removeReferralPanelFilter" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="rptId" type="numeric" required="true">
		<cfargument name="rptTT" type="string" required="true">
		<cfargument name="rpid" type="numeric" required="true">
		<cfargument name="csrID" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditReportRightsByReportID(siteID=arguments.mcproxy_siteID, reportID=arguments.rptId)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetOtherXML">
				DECLARE @referralID int, @panelName varchar(max);
				
				SELECT TOP 1 @referralID = r.referralID
				FROM dbo.ref_referrals AS r
				INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
					AND sr.siteResourceStatusID = 1
				WHERE ai.siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">;

				SELECT @panelName = thePathExpanded
				FROM dbo.fn_getRecursiveReferralPanels(@referralID,NULL)
				WHERE panelID = <cfqueryparam value="#arguments.rpid#" cfsqltype="CF_SQL_INTEGER">;

				SELECT otherXML, 'Panel [' + @panelName + '] is removed from the filter.' AS logMsg
				FROM dbo.rpt_savedReports
				WHERE reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rptId#">
				AND toolTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rptTT#">
				AND controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrID#">;
			</cfquery>

			<cfset local.otherXML = XMLParse(local.qryGetOtherXML.otherXML)>
			<cfset local.rpListNode = XMLSearch(local.otherXML,"/report/extra/rplist")>

			<cfset local.origArr = ListToArray(local.rpListNode[1].xmlText)>
			<cfset local.origArr.removeAll(listToArray(arguments.rpid))>
			<cfset local.rpListNode[1].xmlText = ArrayToList(local.origArr)>

			<!--- remove the <xml> tag, specifically the encoding. It breaks under Railo. --->
			<cfset local.otherXML = replaceNoCase(toString(local.otherXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfset updateSavedReportWithOtherXML(reportID=arguments.rptId, otherXML=local.otherXML,
				changeSection="Referral Panel Filter", arrayLogMessages=[local.qryGetOtherXML.logMsg])>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getAvailableFieldSetsForSavedReport" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="rptID" type="numeric" required="yes">
		<cfargument name="fieldsetNodeName" type="string" required="yes">
	
		<cfset var local = structNew()>
		
		<cfquery name="local.qryAvailableFieldsets" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			DECLARE @tblFieldSets TABLE (fieldSetID int PRIMARY KEY, fieldSetName varchar(200), fieldSetUID uniqueidentifier, categoryID int, categoryName varchar(200));

			-- get fieldsets linked to report settings
			INSERT INTO @tblFieldSets
			select mfs.fieldsetID, mfs.fieldsetName, mfs.uid, c.categoryID, c.categoryName
			from dbo.ams_memberFieldsets as mfs
			inner join dbo.cms_categories as c on c.categoryID = mfs.categoryID
			inner join dbo.ams_memberFieldUsage as u on u.fieldSetID = mfs.fieldSetID
			INNER JOIN dbo.cms_siteResources sr on u.siteResourceID = sr.siteResourceID 
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_siteResourceTypes as srt on sr.resourceTypeID = srt.resourceTypeID and srt.resourceType = 'rpt_ReportSettings'
			where mfs.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and u.area = 'custom';

			-- if there are none, then use all defined fieldsets
			IF @@ROWCOUNT = 0
				INSERT INTO @tblFieldSets
				select mfs.fieldsetID, mfs.fieldsetName, mfs.uid, c.categoryID, c.categoryName
				from dbo.ams_memberFieldsets as mfs
				inner join dbo.cms_categories as c on c.categoryID = mfs.categoryID
				where mfs.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			-- return only those not already in the report
			select mfs.fieldsetID, mfs.fieldsetName, mfs.fieldSetUID, mfs.categoryID, mfs.categoryName
			from @tblFieldSets as mfs
			where not exists (
				select fieldset.uid
				from dbo.rpt_SavedReports as sr 
				CROSS APPLY otherXML.nodes('/report/#arguments.fieldsetNodeName#/fieldset') as F(fs)
				INNER JOIN dbo.ams_memberFieldSets as fieldset on fieldset.uid = F.fs.value('@uid','uniqueidentifier')
				where sr.reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rptID#">
				and fieldset.uid = mfs.fieldSetUID
			)
			ORDER BY mfs.categoryName, mfs.fieldsetName;
		</cfquery>
		
		<cfreturn local.qryAvailableFieldsets>
	</cffunction>
	
	<cffunction name="getAvailableGroupSetsForSavedReport" access="private" output="false" returntype="query">
		<cfargument name="Event" type="any" required="yes">
	
		<cfset var local = structNew()>
		
		<cfquery name="local.qryAvailableGroupsets" datasource="#application.dsn.membercentral.dsn#">			
			select mgs.groupSetID, mgs.groupSetName, mgs.uid
			from dbo.ams_memberGroupSets as mgs 
			where mgs.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
			and not exists (
				select groupset.uid
				from dbo.rpt_SavedReports as sr 
				CROSS APPLY otherXML.nodes('/report/groupsets/groupset') as G(gs)
				INNER JOIN dbo.ams_memberGroupSets as groupset on groupset.uid = G.gs.value('@uid','uniqueidentifier')
				where sr.reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('qryReportInfo').reportID#">
				and groupset.uid = mgs.uid
			)
			order by mgs.groupSetName			
		</cfquery>
		
		<cfreturn local.qryAvailableGroupsets>
	</cffunction>

	<cffunction name="addGroupset" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetUpdatedXML">
			DECLARE @otherXML xml, @logMsg varchar(max),
				@groupSetUID uniqueidentifier = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#ucase(arguments.event.getValue("uid"))#">;
			
			SELECT @logMsg = 'Group Set [' + groupSetName + '] has been added to the selections.'
			FROM dbo.ams_memberGroupSets
			WHERE orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
			AND [uid] = @groupSetUID;

			SELECT @otherXML = otherXML
			FROM dbo.rpt_savedReports
			WHERE reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('qryReportInfo').reportID#">;

			SET @otherXML.modify('
				insert <groupset uid="{sql:variable("@groupSetUID")}" />
				into (/report/groupsets)[1]
			');

			SELECT @otherXML AS otherXML, @logMsg AS logMsg;
		</cfquery>

		<cfif len(local.qryGetUpdatedXML.otherXML)>
			<cfset updateSavedReportWithOtherXML(reportID=arguments.event.getValue('qryReportInfo').reportID, otherXML=local.qryGetUpdatedXML.otherXML,
				changeSection="Group Sets Selection", arrayLogMessages=[local.qryGetUpdatedXML.logMsg])>
		</cfif>

		<cfset local.data.success = true>

		<cfset local.qryReportInfo = getReportInfo(rptid=arguments.event.getValue('rptId',0), toolTypeID=arguments.event.getValue('mca_tt',''), siteResourceID=this.siteResourceID)>
		<cfset local.qryGroupSets = getAvailableGroupSetsForSavedReport(arguments.event)>
		<cfset local.arrSelectedGroupSets = XMLSearch(local.qryReportInfo["groupsetXML"][1],"/groupsets/groupset")>

		<cfset local.strGrids = { 
			SELGRID=trim(showGroupsetGridSelected(event=arguments.event, arrSelectedGroupSets=local.arrSelectedGroupSets)),
			AVAILGRID=trim(showGroupsetGridAvailable(event=arguments.event, qryAvailableGroupsets=local.qryGroupSets))
			}>

		<cfset local.data.data = serializeJSON(local.strGrids)>
		<cfreturn local.data>
	</cffunction>	
	
	<cffunction name="removeGroupset" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetUpdatedXML">
			DECLARE @otherXML xml, @logMsg varchar(max),
				@groupSetUID uniqueidentifier = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#ucase(arguments.event.getValue("uid"))#">;
			
			SELECT @logMsg = 'Group Set [' + groupSetName + '] has been removed from the selections.'
			FROM dbo.ams_memberGroupSets
			WHERE orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
			AND [uid] = @groupSetUID;

			SELECT @otherXML = otherXML
			FROM dbo.rpt_savedReports
			WHERE reportID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('qryReportInfo').reportID#">;

			SET @otherXML.modify('delete /report/groupsets/groupset[@uid=sql:variable("@groupSetUID")]');

			SELECT @otherXML AS otherXML, @logMsg AS logMsg;
		</cfquery>

		<cfif len(local.qryGetUpdatedXML.otherXML)>
			<cfset updateSavedReportWithOtherXML(reportID=arguments.event.getValue('qryReportInfo').reportID, otherXML=local.qryGetUpdatedXML.otherXML,
				changeSection="Group Set Selection", arrayLogMessages=[local.qryGetUpdatedXML.logMsg])>
		</cfif>
		
		<cfset local.data.success = true>

		<cfset local.qryReportInfo = getReportInfo(rptid=arguments.event.getValue('rptId',0), toolTypeID=arguments.event.getValue('mca_tt',''), siteResourceID=this.siteResourceID)>
		<cfset local.qryGroupSets = getAvailableGroupSetsForSavedReport(arguments.event)>
		<cfset local.arrSelectedGroupSets = XMLSearch(local.qryReportInfo["groupsetXML"][1],"/groupsets/groupset")>

		<cfset local.strGrids = { 
			SELGRID=trim(showGroupsetGridSelected(event=arguments.event, arrSelectedGroupSets=local.arrSelectedGroupSets)),
			AVAILGRID=trim(showGroupsetGridAvailable(event=arguments.event, qryAvailableGroupsets=local.qryGroupSets))
			}>

		<cfset local.data.data = serializeJSON(local.strGrids)>
		<cfreturn local.data>	
	</cffunction>	

	<cffunction name="saveNewReport" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc procedure="rpt_createSavedReport" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mca_tt')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#this.siteResourceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frmRN')#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.reportID">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.reportID = 0>
		</cfcatch>
		</cftry>
		
		<cfif local.reportID gt 0>
			<cflocation url="#this.link.showReport#&rptID=#local.reportID#" addtoken="no">
		<cfelse>
			<cflocation url="#this.link.showReport#&err=1" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="showReportHeader" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="reportAction" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.showImage = false>

		<cfif arguments.reportAction eq "pdf" and len(this.reportImageExt) and FileExists('#application.paths.RAIDUserAssetRoot.path#common/reports/#arguments.siteid#.#this.reportImageExt#')>
			<cfset local.showImage = true>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_reportHeader.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getLocalTime" access="private" output="false" returntype="struct">
		<cfargument name="timeZoneID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.qryTZ = local.objTSTZ.getTZAllFromTZID(arguments.timeZoneID)>
		<cfset local.strReturn.localTime = local.objTSTZ.convertTimeZone(dateToConvert=now(), fromTimeZone='US/Central', toTimeZone=local.qryTZ.timezoneCode)>
		<cfset local.strReturn.localTimeZoneAbbr = local.qryTZ.timeZoneAbbr>
		<cfset local.strReturn.timezoneCode = local.qryTZ.timezoneCode>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="showReportFooter" access="private" output="false" returntype="string">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="defaultTimeZoneID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = "">

		<cfif arguments.reportAction eq "screen">
			<cfset local.strTime = getLocalTime(arguments.defaultTimeZoneID)>
			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_reportFooterScreen.cfm">
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="runReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = "">
		
		<!--- extend timeout --->
		<cfsetting requesttimeout="300">

		<cfset local.reportAction = arguments.event.getTrimValue('reportAction','')>
		<cfset local.reportRuleID = arguments.event.getValue('qryReportInfo').ruleID>

		<cfset local.loggingStartTime = getTickCount()>

		<cfswitch expression="#local.reportAction#">
			<cfcase value="screen">
				<cfif hasReportEditRights(event=arguments.event)>
					<cfset saveReportRuleVersion(ruleID=local.reportRuleID, ruleVersionID=val(arguments.event.getValue('useRuleVersionID_#local.reportRuleID#',0)))>
					<cfset saveReportExtra(event=arguments.event)>
				</cfif>
				<cfset local.strReport = screenReport(reportAction=local.reportAction, qryReportInfo=arguments.event.getValue('qryReportInfo'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
				<cfif NOT local.strReport.success>
					<cfset local.data = '{ "err": ' & serializeJSON(local.strReport.errMsg) & ' }'>
				<cfelse>
					<cfset local.data = local.strReport.data>
				</cfif>

				<cfset recordRunLog(reportID=arguments.event.getValue('qryReportInfo').reportID, memberID=val(session.cfcuser.memberdata.memberid), format="screen", totalMS=getTickCount() - local.loggingStartTime)>
			</cfcase>
			<cfcase value="pdf">
				<cfif hasReportEditRights(event=arguments.event)>
					<cfset saveReportRuleVersion(ruleID=local.reportRuleID, ruleVersionID=val(arguments.event.getValue('useRuleVersionID_#local.reportRuleID#',0)))>
					<cfset saveReportExtra(event=arguments.event)>
				</cfif>
				<cfset local.strReport = pdfReport(reportAction=local.reportAction, qryReportInfo=arguments.event.getValue('qryReportInfo'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
				<cfif NOT local.strReport.success>
					<cfset local.errmsg = "There was an error producing the PDF report.">
					<cfif len(local.strReport.errMsg)>
						<cfset local.errmsg &= "<br/><br/>#local.strReport.errMsg#">
					</cfif>
					<cfif structKeyExists(local.strReport,"format") and local.strReport.format eq "json">
						<cfset local.data = '{ "err": ' & serializeJSON(local.strReport.errmsg) & ' }'>
					<cfelse>
						<cfset local.data = "<script language='Javascript'>top.rptShowAlert('#JSStringFormat(local.errmsg)#');</script>">
					</cfif>
				<cfelse>
					<cfset local.data = local.strReport.data>
				</cfif>

				<cfset recordRunLog(reportID=arguments.event.getValue('qryReportInfo').reportID, memberID=val(session.cfcuser.memberdata.memberid), format="pdf", totalMS=getTickCount() - local.loggingStartTime)>
			</cfcase>
			<cfcase value="customcsv">
				<cfset local.data = csvFromBCP(qryReportInfo=arguments.event.getValue('qryReportInfo'), bcp=arguments.event.getValue('bcp',''), logID=val(arguments.event.getValue('rptlgi',0)))>
			</cfcase>
			<cfdefaultcase>
				<cflocation url="#this.link.showReport#&rptID=#arguments.event.getValue('rptId')#" addtoken="no">
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="autoRunReport" access="public" output="false" returntype="struct">
		<cfargument name="reportUID" type="string" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="runByMemberID" type="numeric" required="true">
		<cfargument name="filename" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.strReport = { "success":"false" }>
		
		<!--- extend timeout --->
		<cfsetting requesttimeout="300">

		<cftry>
			<!--- get Report Info by UID --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetReportID">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select top 1 sr.reportID, sr.toolTypeID, sr.controllingSiteResourceID, s.siteCode, ai.settingsXML
					from dbo.rpt_SavedReports as sr
					inner join dbo.sites as s on s.siteID = sr.siteID
					inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID and ai.applicationInstanceName = 'admin'
					where sr.[uid] = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.reportUID#">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.qryReportInfo = getReportInfo(rptid=local.qryGetReportID.reportID, toolTypeID=local.qryGetReportID.toolTypeID, siteResourceID=local.qryGetReportID.controllingSiteResourceID)>	

			<cfif arrayFind(variables.runformats,arguments.reportAction)>
				<cfset local.loggingStartTime = getTickCount()>

				<cfswitch expression="#arguments.reportAction#">
					<cfcase value="screen">
						<cfset local.strReport = screenReport(reportAction=arguments.reportAction, qryReportInfo=local.qryReportInfo, siteCode=local.qryGetReportID.siteCode)>
						<cfset recordRunLog(reportID=local.qryReportInfo.reportID, memberID=arguments.runByMemberID, format="screen", totalMS=getTickCount() - local.loggingStartTime)>
					</cfcase>
					<cfcase value="pdf">
						<cfset this.reportImageExt = xmlSearch(local.qryGetReportID.settingsXML,'string(/settings/setting[@name="reportHeaderImage"]/@value)')>
						<cfset local.strReport = pdfReport(reportAction=arguments.reportAction, qryReportInfo=local.qryReportInfo, siteCode=local.qryGetReportID.siteCode)>
						<cfif local.strReport.success and len(arguments.filename) and len(local.strReport.pdfDownloadPath)>
							<cffile action="rename" source="#local.strReport.pdfDownloadPath#" destination="#arguments.filename#">
							<cfset local.strReport.pdfDownloadPath = listSetAt(local.strReport.pdfDownloadPath,listLen(local.strReport.pdfDownloadPath,"/"),arguments.filename,"/")>
						</cfif>
						<cfset recordRunLog(reportID=local.qryReportInfo.reportID, memberID=arguments.runByMemberID, format="pdf", totalMS=getTickCount() - local.loggingStartTime)>
					</cfcase>
					<cfcase value="customcsv">
						<cfset local.strReport = csvReport(reportAction=arguments.reportAction, qryReportInfo=local.qryReportInfo, siteCode=local.qryGetReportID.siteCode)>
						<cfset local.reportLogID = recordRunLog(reportID=local.qryReportInfo.reportID, memberID=arguments.runByMemberID, format="customcsv", totalMS=getTickCount() - local.loggingStartTime)>
						<cfif local.strReport.success>
							<cfset local.bcpfilenameEnc = encrypt(local.strReport.data.bcpFilename,"M@!6T$","CFMX_COMPAT","Hex")>
							<cfset local.strReport = doCSVFromBCP(reportID=local.qryReportInfo.reportID, reportName=local.qryReportInfo.reportName, encBCPPath=local.bcpfilenameEnc, logID=local.reportLogID)>
							<cfif local.strReport.success and len(arguments.filename) and len(local.strReport.csvDownloadPath)>
								<cffile action="rename" source="#local.strReport.csvDownloadPath#" destination="#arguments.filename#">
								<cfset local.strReport.csvDownloadPath = listSetAt(local.strReport.csvDownloadPath,listLen(local.strReport.csvDownloadPath,"/"),arguments.filename,"/")>
							</cfif>
						</cfif>
					</cfcase>
				</cfswitch>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.strReport.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReport>	
	</cffunction>

	<cffunction name="doCSVFromBCP" access="private" output="false" returntype="struct">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="reportName" type="string" required="false">
		<cfargument name="encBCPPath" type="string" required="true">
		<cfargument name="logID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strArgs = arguments>
		<cfset local.reportFileName = "">
		<cfset local.csvDownloadPath = "">
		<cfset local.folderPath = "">
		<cfset local.csvRowCount = 0>
		<cfset local.loggingStartTime = getTickCount()>

		<cftry>
			<cfset local.bcpFileName = decrypt(arguments.encBCPPath,"M@!6T$","CFMX_COMPAT","Hex")>
			<cfset local.tableName = '####' & listLast(application.objCommon.convertFileSeparator(local.bcpFileName,'/'),'/')>
			<cfset local.reportFileName = ReReplaceNoCase(arguments.reportName,'[^A-Z0-9\_\-]','','ALL') & '.csv'>

			<cfstoredproc procedure="rpt_bcpToCSV" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.reportID#">
				<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.bcpFileName#">
				<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#local.tableName#">
				<cfprocresult name="local.qryCSVRowCount">
			</cfstoredproc>

			<cfstoredproc procedure="rpt_updateRunLog" datasource="#application.dsn.platformstatsMC.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.logID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#getTickCount() - local.loggingStartTime#">
			</cfstoredproc>

			<cfset local.csvRowCount = local.qryCSVRowCount.csvRowCount>

			<!--- Kludge to convert pathUNC to path for our Lucee servers --->
			<cfset local.csvDownloadPath = application.objCommon.convertFileSeparator(replacenocase(local.bcpFileName,"#application.paths.SharedTempNoWeb.pathUNC#","#application.paths.SharedTempNoWeb.path#"),'/') & ".csv">
			<cfset local.folderPath = local.csvDownloadPath.listDeleteAt(listLen(local.csvDownloadPath,"/"),'/')>

			<cfset local.success = true>
			<cfset local.errMsg = "">
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfset local.errMsg = "Error producing final csv file.">
			<cfif NOT findNoCase("Invalid column name",cfcatch.detail)>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn { success=local.success, csvDownloadPath=local.csvDownloadPath, folderPath=local.folderPath, reportFileName=local.reportFileName, csvRowCount=local.csvRowCount, errMsg=local.errMsg }>
	</cffunction>

	<cffunction name="csvFromBCP" access="private" output="false" returntype="string">
		<cfargument name="qryReportInfo" required="true" type="query">
		<cfargument name="bcp" required="true" type="string">
		<cfargument name="logID" required="true" type="numeric">

		<cfset var local = structNew()>
		<cfset local.strBCP = doCSVFromBCP(reportID=arguments.qryReportInfo.reportID, reportName=arguments.qryReportInfo.reportName, encBCPPath=arguments.bcp, logID=arguments.logID)>

		<cfif local.strBCP.success and len(local.strBCP.csvDownloadPath)>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strBCP.csvDownloadPath#", displayName=local.strBCP.reportFileName, deleteSourceFile=0)>
			<cfset local.data = "<script language='Javascript'>top.rpt_download('#local.stDownloadURL#');</script>">
		<cfelse>
			<cfset local.data = "<script language='Javascript'>top.rptShowAlert('There was a problem generating the CSV report.');</script>">
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="pdfOrientation" required="false" type="string" default="portrait">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", folderPath="", pdfDownloadPath="", isReportEmpty=false }>

		<cftry>
			<cfset local.screenReport = screenReport(reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>
			<cfif NOT local.screenReport.success>
				<cfreturn local.screenReport>
			</cfif>

			<cfsavecontent variable="local.data">
				<cfoutput>
				<html>
				<head>
					<style type="text/css">
						<cfinclude template="/assets/admin/css/pdfstylesheet.css">
					</style>
				</head>
				<body>
				#local.screenReport.data#
				</body>
				</html>
				</cfoutput>
			</cfsavecontent>
	
			<cfset local.strTime = getLocalTime(application.objSiteInfo.getSiteInfo(arguments.siteCode).defaultTimeZoneID)>
			<cfset local.reportFileName = ReReplaceNoCase(arguments.qryReportInfo.reportname,'[^A-Z0-9\_\-]','','ALL') & ".pdf">
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.siteCode)>

			<cfsavecontent variable="local.rptFooter">
				<cfoutput>
				<html>
				<head></head>
				<body>
					<div style="font-family:Verdana, Arial, Helvetica, sans-serif; font-size:9px; color:##bbb; border-top:1px solid ##ccc; padding-top:7px; text-align:center; width:100%;">
						<!-- cpp --> Generated #datetimeformat(local.strTime.localTime,"m/d/yyyy h:nn tt",'#local.strTime.timezoneCode#')# #local.strTime.localTimeZoneAbbr# by #session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname#
					</div>
				</body>
				</html>	
				</cfoutput>
			</cfsavecontent>

			<cfset local.footercol = { type="footer", evalAtPrint=true, txt=local.rptFooter } >
			<cfdocument filename="#local.strFolder.folderPath#/#local.reportFileName#" pagetype="letter" margintop="0.5" marginbottom="0.5" marginright="0.5" format="PDF" marginleft="0.5" backgroundvisible="Yes" orientation="#arguments.pdfOrientation#" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<cfdocumentsection>
						#local.data#
						<cfdocumentitem attributeCollection="#local.footercol#">
							#replace(local.rptFooter,'<!-- cpp -->','Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#
						</cfdocumentitem>
					</cfdocumentsection>
				</cfoutput>
			</cfdocument>

			<cfset local.strReturn.isReportEmpty = local.screenReport.isReportEmpty>
			<cfset local.strReturn.folderPath = local.strFolder.folderPath>
			<cfset local.strReturn.pdfDownloadPath = "#local.strFolder.folderPath#/#local.reportFileName#">
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath=local.strReturn.pdfDownloadPath, displayName=local.reportFileName, deleteSourceFile=1)>
			<cfset local.strReturn.data = "<script language='Javascript'>top.rpt_download('#local.stDownloadURL#');</script>">
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="csvSettings" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.allowSave = hasReportEditRights(event=arguments.event)>
		
		<!--- save report rule version and extra filters --->
		<cfif local.allowSave>
			<cfset local.reportRuleID = arguments.event.getValue('qryReportInfo').ruleID>
			<cfset saveReportRuleVersion(ruleID=local.reportRuleID, ruleVersionID=val(arguments.event.getValue('useRuleVersionID_#local.reportRuleID#',0)))>
			<cfset saveReportExtra(event=arguments.event)>
		</cfif>

		<cfset local.loggingStartTime = getTickCount()>

		<!--- extend timeout --->
		<cfsetting requesttimeout="500">

		<cftry>
			<cfset local.strReport = csvReport(reportAction="customcsv", qryReportInfo=arguments.event.getValue('qryReportInfo'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
			<cfif NOT local.strReport.success>
				<cfset local.data = '{ "err": ' & serializeJSON(local.strReport.errMsg) & ' }'>
			<cfelse>
				<cfset local.strCSVReport = local.strReport.data>
				<cfset local.arrFields = local.strCSVReport.arrFields>
				<cfset local.jsonfields = SerializeJSON(local.strCSVReport.arrFields)>
				<cfset local.arrSortFields = local.strCSVReport.arrSortFields>
				<cfset local.jsonSortfields = SerializeJSON(local.strCSVReport.arrSortFields)>
				<cfset local.bcpfilenameEnc = encrypt(local.strCSVReport.bcpFilename,"M@!6T$","CFMX_COMPAT","Hex")>

				<!--- split into two arrays --->
				<cfset local.arrFieldsIncluded = arrayNew(1)>
				<cfset local.arrFieldsExcluded = arrayNew(1)>
				<cfloop array="#local.arrFields#" index="local.thisCSVField">
					<cfif isdefined("local.thisCSVField.drop")>
						<cfset arrayAppend(local.arrFieldsExcluded, local.thisCSVField)>
					<cfelse>
						<cfset arrayAppend(local.arrFieldsIncluded, local.thisCSVField)>
					</cfif>
				</cfloop>

				<cfset local.reportLogID = recordRunLog(reportID=arguments.event.getValue('qryReportInfo').reportID, memberID=val(session.cfcuser.memberdata.memberid), format="customcsv", totalMS=getTickCount() - local.loggingStartTime)>

				<cfsavecontent variable="local.data">
					<cfinclude template="dsp_csvSettings.cfm">
				</cfsavecontent>
			</cfif>
		<cfcatch type="any">
			<cfset recordRunLog(reportID=arguments.event.getValue('qryReportInfo').reportID, memberID=val(session.cfcuser.memberdata.memberid), format="customcsv", totalMS=getTickCount() - local.loggingStartTime)>
			<cfset local.data = '{ "err": "" }'>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="recordRunLog" access="private" output="false" returntype="numeric">
		<cfargument name="reportID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="format" type="string" required="true">
		<cfargument name="totalMS" type="numeric" required="true">
		
		<cfset var logID = 0>

		<cfstoredproc procedure="rpt_recordRunLog" datasource="#application.dsn.platformstatsMC.dsn#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.reportID#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.memberid#">
			<cfprocparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#arguments.format#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.totalMS#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="out" variable="logID">
		</cfstoredproc>

		<cfreturn logID>
	</cffunction>

	<cffunction name="updateReportName" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="reportName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditReportRightsByReportID(siteID=arguments.mcproxy_siteID, reportID=arguments.reportID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryUpdateReportName" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @reportID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">,
						@currentReportName varchar(200),
						@reportName varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.reportName#">;
					
					SELECT @currentReportName = reportName FROM dbo.rpt_SavedReports WHERE reportID = @reportID;

					IF @currentReportName <> @reportName BEGIN
						UPDATE dbo.rpt_SavedReports
						SET reportName = @reportName
						WHERE reportID = @reportID;

						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						SELECT '{ "c":"auditLog", "d": {
							"AUDITCODE":"RPT",
							"ORGID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#"> AS VARCHAR(10)) + ',
							"SITEID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#"> AS VARCHAR(10)) + ',
							"ACTORMEMBERID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#"> as varchar(20)) + ',
							"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
							"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars('Report Name for ' + @currentReportName + ' [' + tt.toolDesc + ']' + ' has been updated to ' + @reportName + '.'),'"','\"') + '" } }'
						FROM dbo.rpt_SavedReports AS r
						INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
						WHERE r.reportID = @reportID;
					END
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="checkMemberFilterLength" access="public" output="false" returntype="struct">
		<cfargument name="rptID" type="numeric" required="true">
		<cfargument name="rptTT" type="string" required="true">
		<cfargument name="csrID" type="string" required="true">
		<cfargument name="rvID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfset local.qryReportInfo = getReportInfo(rptId=arguments.rptID, toolTypeID=arguments.rptTT, siteResourceID=arguments.csrID)>
		<cfset local.data.filtercount = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin").countRuleConditions(ruleID=local.qryReportInfo.ruleID, ruleVersionID=arguments.rvID)>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getPreviewMemberFieldSetData" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="memberNumber" type="string" required="true">
		<cfargument name="fieldSetID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 
			"success":true, 
			"memberid":application.objMember.getMemberIDByMemberNumber(memberNumber=arguments.membernumber, orgID=arguments.mcproxy_orgID),
			"strfieldsetdata":{} }>

		<cfif local.returnStruct.memberID GT 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPreviewMemberFieldSetData">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
					DROP TABLE ##tmp_membersForFS;
				CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
				CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
					@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.returnStruct.memberID#">,
					@fieldSetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">,
					@outputFieldsXML xml;

				-- get fieldset data
				INSERT INTO ##tmp_membersForFS (memberID) VALUES (@memberID);

				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
					@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;

				SELECT * FROM ##tmpMembers;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryPreviewMemberFieldSetData.recordCount>
				<cfset local.returnStruct.strfieldsetdata = QueryRowData(qryPreviewMemberFieldSetData,1)>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getPreviewMemberGroupSetData" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="memberNumber" type="string" required="true">
		<cfargument name="groupSetID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = {
			"success":true,
			"memberid":application.objMember.getMemberIDByMemberNumber(memberNumber=arguments.membernumber, orgID=arguments.mcproxy_orgID),
			"strgroupsetdata":{} }>

		<cfif local.returnStruct.memberID GT 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPreviewMemberGroupSetData">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmp_membersForGS') IS NOT NULL
					DROP TABLE ##tmp_membersForGS;
				CREATE TABLE ##tmpMembers (MGSAutoID int IDENTITY(1,1) not null);
				CREATE TABLE ##tmp_membersForGS (memberID int PRIMARY KEY);

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
					@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.returnStruct.memberID#">,
					@groupSetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupSetID#">,
					@outputGroupsXML xml;

				-- get groupset data
				INSERT INTO ##tmp_membersForGS (memberID) VALUES (@memberID);

				EXEC dbo.ams_getMemberDataByGroupSets @orgID=@orgID, @groupSetIDList=@groupSetID,
					@membersTableName='##tmp_membersForGS', @membersResultTableName='##tmpMembers',
					@mode='view', @outputGroupsXML=@outputGroupsXML OUTPUT;
				
				SELECT * FROM ##tmpMembers;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryPreviewMemberGroupSetData.recordCount>
				<cfset local.returnStruct.strgroupsetdata = QueryRowData(qryPreviewMemberGroupSetData,1)>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveFieldsetSettings" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strResult = structNew()>
		<cfset local.fsNodeName = arguments.event.getValue("fieldsetNodeName")>
		<cfset local.strResult.data = "">

		<cftry>
			<cfif hasReportEditRights(event=arguments.event)>
				<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.updateFieldSetNode">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
							DROP TABLE ##tmpLogMessages;
						CREATE TABLE ##tmpLogMessages (rowID int IDENTITY(1,1), msg varchar(max));

						DECLARE @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('qryReportInfo').reportID#">,
							@actorMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">,
							@otherXML xml, @nfOld varchar(10), @imgOld char(1),  @mnOld char(1), @mcOld char(1),  @mkOld char(1),
							@nfNew varchar(10) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ovfsnf','PFMLSX')#">,
							@imgNew char(1) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ovfsimg','0')#">,
							@mnNew char(1) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ovfsmn','0')#">,
							@mcNew char(1) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ovfsmc','0')#">,
							@mkNew char(1) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('ovfsmk','0')#">,
							@crlf varchar(10), @msgjson varchar(max);
						
						SET @crlf = CHAR(13) + CHAR(10);

						SELECT @otherXML = otherXML FROM dbo.rpt_SavedReports WHERE reportID = @reportID;

						SET @nfOld = @otherXML.value('(report/#local.fsNodeName#/@nf)[1]', 'VARCHAR(50)');
						SET @imgOld = @otherXML.value('(report/#local.fsNodeName#/@img)[1]', 'INT');
						SET @mnOld = @otherXML.value('(report/#local.fsNodeName#/@mn)[1]', 'INT');
						SET @mcOld = @otherXML.value('(report/#local.fsNodeName#/@mc)[1]', 'INT');
						SET @mkOld = @otherXML.value('(report/#local.fsNodeName#/@mk)[1]', 'INT');

						BEGIN TRAN;
							IF @nfNew <> @nfOld BEGIN
								UPDATE dbo.rpt_savedReports
								SET otherXML.modify('
									replace value of (/report/#local.fsNodeName#/@nf)[1]
									with sql:variable("@nfNew")
								')
								WHERE reportID = @reportID;

								INSERT INTO ##tmpLogMessages(msg)
								VALUES(CASE WHEN @nfOld = 'LSXPFM' THEN '[Show Member Names as Last, First Middle] changed to [Show Member Names as First Middle Last]' ELSE '[Show Member Names as First Middle Last] changed to [Show Member Names as Last, First Middle]' END + '.')
							END

							IF @imgNew <> @imgOld BEGIN
								UPDATE dbo.rpt_savedReports
								SET otherXML.modify('
									replace value of (/report/#local.fsNodeName#/@img)[1]
									with sql:variable("@imgNew")
								')
								WHERE reportID = @reportID;

								INSERT INTO ##tmpLogMessages(msg)
								VALUES(CASE WHEN @imgOld = '1' THEN '[Include Member Photos] changed to [Don''t Include Member Photos]' ELSE '[Don''t Include Member Photos] changed to [Include Member Photos]' END + '.');
							END

							IF @mnNew <> @mnOld BEGIN
								UPDATE dbo.rpt_savedReports
								SET otherXML.modify('
									replace value of (/report/#local.fsNodeName#/@mn)[1]
									with sql:variable("@mnNew")
								')
								WHERE reportID = @reportID;

								INSERT INTO ##tmpLogMessages(msg)
								VALUES(CASE WHEN @mnOld = '1' THEN '[Include MemberNumber] changed to [Don''t Include MemberNumber]' ELSE '[Don''t Include MemberNumber] changed to [Include MemberNumber]' END + '.');
							END

							IF @mcNew <> @mcOld BEGIN
								UPDATE dbo.rpt_savedReports
								SET otherXML.modify('
									replace value of (/report/#local.fsNodeName#/@mc)[1]
									with sql:variable("@mcNew")
								')
								WHERE reportID = @reportID;

								INSERT INTO ##tmpLogMessages(msg)
								VALUES(CASE WHEN @mcOld = '1' THEN '[Include Member Company] changed to [Don''t Include Member Company]' ELSE '[Don''t Include Member Company] changed to [Include Member Company]' END + '.')
							END

							IF @mkNew <> @mkOld BEGIN
								UPDATE dbo.rpt_savedReports
								SET otherXML.modify('
									replace value of (/report/#local.fsNodeName#/@mk)[1]
									with sql:variable("@mkNew")
								')
								WHERE reportID = @reportID;

								INSERT INTO ##tmpLogMessages(msg)
								VALUES(CASE WHEN @mkOld = '1' THEN '[Mask Email Addresses] changed to [Show Email Addresses]' ELSE '[Show Email Addresses] changed to [Mask Email Addresses]' END + '.')
							END
						COMMIT TRAN;

						IF EXISTS(SELECT 1 FROM ##tmpLogMessages) BEGIN
							SELECT @msgjson = 'Field Set Options for ' + r.reportName + ' [' + tt.toolDesc + '] has been updated.'
							FROM dbo.rpt_SavedReports AS r
							INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
							WHERE r.reportID = @reportID;

							SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

							SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
							FROM ##tmpLogMessages
							WHERE msg IS NOT NULL;

							INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
							VALUES ('{ "c":"auditLog", "d": {
								"AUDITCODE":"RPT",
								"ORGID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#"> AS VARCHAR(10)) + ',
								"SITEID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#"> AS VARCHAR(10)) + ',
								"ACTORMEMBERID":' + CAST(@actorMemberID as varchar(20)) + ',
								"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
								"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
							
							EXEC platformQueue.dbo.queue_schedReportChangeNotify_addEntry @reportID=@reportID, @actorMemberID=@actorMemberID, @changeSection='Field Set Options';
						END

						IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
							DROP TABLE ##tmpLogMessages;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>

			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfset local.strResult.data = local.success ? "success" : "error">
		<cfreturn local.strResult>
	</cffunction>

	<cffunction name="generateFinalBCPTable" access="private" output="false" returntype="string">
		<cfargument name="tblName" type="string" required="yes">
		<cfargument name="dropFields" type="string" required="no" default="">

		<cfset var local = structNew()>
		<cfset local.arrPrep = arrayNew(1)>

		<!--- drop columns provided by the specific report --->
		<cfset local.arrDropFields = listToArray(arguments.dropFields)>
		<cfloop array="#local.arrDropFields#" index="local.thisField">
			<cfset arrayAppend(local.arrPrep,"IF EXISTS(select top 1 column_id from tempdb.sys.columns where [name] = '#local.thisField#' and object_id = object_id('tempdb..#arguments.tblName#')) ALTER TABLE #arguments.tblName# DROP COLUMN [#local.thisField#];")>
		</cfloop>		

		<!--- bcp code --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder()>
		<cfset arrayAppend(local.arrPrep,"EXEC memberCentral.dbo.up_exportBCP @filepath='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/",'\')#', @tblName='#arguments.tblName#';")>

		<cfreturn arrayToList(local.arrPrep,chr(10))>
	</cffunction>

	<cffunction name="getCurrentCSVSettings" access="private" output="false" returntype="struct">
		<cfargument name="strReportQry" type="struct" required="yes">
		<cfargument name="arrInitialReportSort" type="array" required="yes">
		<cfargument name="otherXML" type="xml" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.arrFields = arrayNew(1)>
		<cfset local.arrSortFields = arrayNew(1)>
		<cfset local.arrXMLFields = XMLSearch(arguments.otherXML,"/report/customcsv/fldorder/fld")>
		<cfset local.arrXMLSortFields = XMLSearch(arguments.otherXML,"/report/customcsv/fldsort/fld")>

		<!--- put qryReportFields into local array of structs. --->
		<cfset local.arrReportFields = arrayNew(1)>
		<cfloop query="arguments.strReportQry.qryReportFields">
			<cfset local.tmpStr = { pos=arguments.strReportQry.qryReportFields.currentrow, field=arguments.strReportQry.qryReportFields.column_name, label=arguments.strReportQry.qryReportFields.column_name }>
			<cfset arrayAppend(local.arrReportFields,local.tmpStr)>
		</cfloop>
		<cfset local.arrReportFieldsForSort = duplicate(local.arrReportFields)>

		<!--- ensure field in XML is still a valid field for the report --->
		<cfset local.newPOS = 0>
		<cfloop array="#local.arrXMLFields#" index="local.thisXMLField">
			<cfset local.thisXMLFieldOK = false>
			<cfloop from="#arrayLen(local.arrReportFields)#" to="1" step="-1" index="local.thisReportFieldIdx">
				<cfif local.thisXMLField.xmlAttributes.field eq local.arrReportFields[local.thisReportFieldIdx].field>
					<cfset local.thisXMLFieldOK = true>
					<cfbreak>
				</cfif>
			</cfloop>
			<cfif local.thisXMLFieldOK>
				<cfset local.newPOS = local.newPOS + 1>
				<cfset local.strTemp = { pos=local.newPOS, field=local.thisXMLField.xmlAttributes.field, label=local.thisXMLField.xmlAttributes.label }>
				<cfset arrayAppend(local.arrFields,local.strTemp)>
				<cfset arrayDeleteAt(local.arrReportFields, local.thisReportFieldIdx)>
			</cfif>
		</cfloop>

		<!--- now include all other report fields at the bottom. if arrFields is empty, all fields included at top instead. --->
		<cfset local.dropFlag = 1>
		<cfif arrayLen(local.arrFields) is 0>
			<cfset local.dropFlag = 0>
		</cfif>
		<cfloop array="#local.arrReportFields#" index="local.thisReportField">
			<cfset local.newPOS = local.newPOS + 1>
			<cfset local.strTemp = { pos=local.newPOS, field=local.thisReportField.field, label=local.thisReportField.label }>
			<cfif local.dropFlag>
				<cfset local.strTemp.drop = "1">
			</cfif>
			<cfset arrayAppend(local.arrFields,local.strTemp)>
		</cfloop>

		<!--- ensure field in XML is still a valid field for the report --->
		<cfset local.newPOS = 0>
		<cfloop array="#local.arrXMLSortFields#" index="local.thisXMLField">
			<cfset local.thisXMLFieldOK = false>
			<cfloop from="#arrayLen(local.arrReportFieldsForSort)#" to="1" step="-1" index="local.thisReportFieldIdx">
				<cfif local.thisXMLField.xmlAttributes.field eq local.arrReportFieldsForSort[local.thisReportFieldIdx].field>
					<cfset local.thisXMLFieldOK = true>
					<cfbreak>
				</cfif>
			</cfloop>
			<cfif local.thisXMLFieldOK>
				<cfset local.newPOS = local.newPOS + 1>
				<cfset local.strTemp = { pos=local.newPOS, field=local.thisXMLField.xmlAttributes.field, dir=local.thisXMLField.xmlAttributes.dir }>
				<cfset arrayAppend(local.arrSortFields,local.strTemp)>
			</cfif>
		</cfloop>

		<!--- if arrSortFields is empty, use default specified by report --->
		<cfif arraylen(local.arrSortFields) is 0>
			<cfloop array="#arguments.arrInitialReportSort#" index="local.thisSortField">
				<cfset local.newPOS = local.newPOS + 1>
				<cfset local.strTemp = { pos=local.newPOS, field=local.thisSortField.field, dir=local.thisSortField.dir }>
				<cfset arrayAppend(local.arrSortFields,local.strTemp)>
			</cfloop>
		</cfif>

		<cfset local.rawSQL = showRawSQL(reportAction='customcsv', qryName="arguments.strReportQry.qryReportFields", strQryResult=arguments.strReportQry.strQryResult)>

		<cfset local.strReturn = { arrFields=local.arrFields, arrSortFields=local.arrSortFields, bcpfilename=arguments.strReportQry.qryReportFields.bcpFileName, rawSQL=local.rawSQL }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="csvSaveSettings" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cftry>
			<cfif hasReportEditRights(event=arguments.event)>
				<cfscript>
				// get the otherXML
				local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);

				// get existing fields order & sort, to compare for audit logging
				local.arrColumnsOrderOld = [];
				local.arrColumnsSortOld = [];
				if(structKeyExists(local.otherXML.XMLRoot, "customcsv")){
					if(structKeyExists(local.otherXML.XMLRoot.customcsv, "fldorder"))
						for (local.thisField in local.otherXML.XMLRoot.customcsv.fldorder.xmlChildren)
							arrayAppend(local.arrColumnsOrderOld, "#local.thisField.XmlAttributes.field# (#local.thisField.XmlAttributes.label#)");
					if(structKeyExists(local.otherXML.XMLRoot.customcsv, "fldsort"))
						for (local.thisField in local.otherXML.XMLRoot.customcsv.fldsort.xmlChildren)
							arrayAppend(local.arrColumnsSortOld, "#local.thisField.XmlAttributes.field# (#local.thisField.XmlAttributes.dir#)");
				}

				// get a handle on the customcsv node
				local.csvsettingsNode = XMLSearch(local.otherXML,"/report/customcsv");
				
				// delete the existing customcsv node and any children
				application.objCommon.XmlDeleteNodes(local.otherXML,local.csvsettingsNode);
				
				// add a new empty customcsv node tree
				arrayAppend(local.otherXML.XMLRoot.xmlChildren,XMLElemNew(local.otherXML,"customcsv"));
				local.newNode = XMLElemNew(local.otherXML,"fldorder"); arrayAppend(local.otherXML.XMLRoot.customcsv.xmlChildren,local.newNode);
				local.newNode = XMLElemNew(local.otherXML,"fldsort"); arrayAppend(local.otherXML.XMLRoot.customcsv.xmlChildren,local.newNode);

				// get fields array
				local.arrFields = deserializeJSON(arguments.event.getValue('jsonFields'));
				local.arrSortFields = deserializeJSON(arguments.event.getValue('jsonSortFields'));

				// put new order into array
				local.arrOrder = listToArray(arguments.event.getValue('neworder'));
				local.arrSortOrder = deserializeJSON(arguments.event.getValue('newsortorder'));
				
				local.arrColumnsOrderNew = [];
				local.arrColumnsSortNew = [];

				// add children in selected field order
				for (local.i=1; local.i lte arrayLen(local.arrOrder); local.i=local.i+1) {
					if (arguments.event.valueExists('csvfld_'&local.arrOrder[local.i])) {
						local.newNode = XMLElemNew(local.otherXML,"fld");
						local.newNode.XMLAttributes["pos"] = local.i;
						local.newNode.XMLAttributes["field"] = local.arrFields[local.arrOrder[local.i]].field;
						local.newNode.XMLAttributes["label"] = arguments.event.getTrimValue('csvfld_'&local.arrOrder[local.i]);
						arrayAppend(local.otherXML.XMLRoot.customcsv.fldorder.xmlChildren,local.newNode);
						arrayAppend(local.arrColumnsOrderNew, "#local.newNode.XMLAttributes["field"]# (#local.newNode.XMLAttributes["label"]#)");
					}
				}
				for (local.i=1; local.i lte arrayLen(local.arrSortOrder); local.i=local.i+1) {
					local.newNode = XMLElemNew(local.otherXML,"fld");
					local.newNode.XMLAttributes["pos"] = local.i;
					local.newNode.XMLAttributes["field"] = local.arrSortOrder[local.i].field;
					local.newNode.XMLAttributes["dir"] = local.arrSortOrder[local.i].dir;
					arrayAppend(local.otherXML.XMLRoot.customcsv.fldsort.xmlChildren,local.newNode);
					arrayAppend(local.arrColumnsSortNew, "#local.newNode.XMLAttributes["field"]# (#local.newNode.XMLAttributes["dir"]#)");
				}

				// remove the <xml> tag, specifically the encoding. It breaks under Railo. --->
				local.otherXML = replaceNoCase(toString(local.otherXML),'<?xml version="1.0" encoding="UTF-8"?>','');
				
				// audit log if columns or sorting is changed
				local.arrayLogMessages = [];
				if (arrayToList(local.arrColumnsOrderNew) NEQ arrayToList(local.arrColumnsOrderOld) OR arrayToList(local.arrColumnsSortNew) NEQ arrayToList(local.arrColumnsSortOld)){
					if (arrayToList(local.arrColumnsOrderNew) NEQ arrayToList(local.arrColumnsOrderOld))
						arrayAppend(local.arrayLogMessages, arrayLen(local.arrColumnsOrderNew) ? ("Specified Included Columns in the following order: " & arrayToList(local.arrColumnsOrderNew,", ") & ".") : "Reset Included Columns.");

					if (arrayToList(local.arrColumnsSortNew) NEQ arrayToList(local.arrColumnsSortOld))
						arrayAppend(local.arrayLogMessages, arrayLen(local.arrColumnsSortNew) ? ("Specified Sort Order in the following order: " & arrayToList(local.arrColumnsSortNew,", ") & ".") : "Reset Columns for Sort Order.");
				}
				</cfscript>

				<cfset updateSavedReportWithOtherXML(reportID=arguments.event.getValue('qryReportInfo').reportID, otherXML=local.otherXML,
					changeSection="CSV Column Settings", arrayLogMessages=local.arrayLogMessages, notifyOnlyIfChangeLogs=1)>
			</cfif>	
		
			<cfset local.data = "{ ""success"":true }">

		<cfcatch type="Any">
			<cfset local.data = "{ ""success"":false }">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- this is a placeholder function that is overridden by each report. --->
	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfreturn returnAppStruct('',"echo")>
	</cffunction>

	<cffunction name="reportSaveReportExtra" access="private" output="false" returntype="void">
		<cfargument name="qryReportInfo" type="query" required="yes">
		<cfargument name="strFields" type="struct" required="yes">
		<cfargument name="Event" type="any" required="false">

		<cfset var local = structNew()>

		<cfif hasReportEditRights(event=arguments.event)>
			<cfscript>
			// get field labels
			local.strFieldLabels = structNew();
			for (local.thisKey in arguments.strFields) {
				if (isStruct(arguments.strFields[local.thisKey]) and structKeyExists(arguments.strFields[local.thisKey], "label")) {
					local.strFieldLabels[local.thisKey] = arguments.strFields[local.thisKey]["label"];
				}
			}

			// get the otherXML
			local.otherXML = XMLParse(arguments.qryReportInfo.otherXML);
			
			// get a handle on the extra node
			local.currentExtraNode = XMLSearch(local.otherXML,"/report/extra");

			local.strExtraNodeChanges = structNew();

			// storing current data for audit logging before deleting the extraNode from otherXML
			setExtraNodeValuesForAuditing(strExtraNodeChanges=local.strExtraNodeChanges, extraNode=local.currentExtraNode, strFields=arguments.strFields, valueType="oldVal");
			
			// delete the existing extra node and any children
			application.objCommon.XmlDeleteNodes(local.otherXML,local.currentExtraNode);
			
			// add a new empty extra node
			arrayAppend(local.otherXML.XMLRoot.xmlChildren,XMLElemNew(local.otherXML,"extra"));

			// check for rollDates
			local.useRollDates = false;
			if (isDefined("arguments.event")) {
				local.rollDate = arguments.event.getValue('rollDate',0);
				local.rollAdv = arguments.event.getValue('roll_adv','');
				if (local.rollDate is 1 AND len(local.rollAdv)) {
					for (local.fld in arguments.event.getCollection()) {
						if (local.fld neq "roll_adv_afid" and right(local.fld,5) eq "_afid" and val(arguments.event.getValue(local.fld,'0'))) {
							local.useRollDates = true;
							break;
						}
					}
				}
			}
			if (local.useRollDates) {
				local.newNode = XMLElemNew(local.otherXML,'afrundate');
				local.newNode.xmlText = local.rollAdv;
				if (val(arguments.event.getValue('roll_adv_afid','0'))) {
					structInsert(local.newNode.xmlAttributes,'afid',val(arguments.event.getValue('roll_adv_afid','0')));
				}
				arrayAppend(local.otherXML.XMLRoot.extra.xmlChildren,local.newNode);
			}

			// add children
			for (local.key in arguments.strFields) {
				if (isStruct(arguments.strFields[local.key]) and isSimpleValue(arguments.strFields[local.key]["value"])) {
					local.newNode = XMLElemNew(local.otherXML,lcase(local.key));
					local.newNode.xmlText = arguments.strFields[local.key]["value"];
					if (local.useRollDates and len(local.newNode.xmlText) and val(arguments.event.getValue('roll_#local.key#_afid','0'))) {
						structInsert(local.newNode.xmlAttributes,'afid',val(arguments.event.getValue('roll_#local.key#_afid','0')));
					}
					arrayAppend(local.otherXML.XMLRoot.extra.xmlChildren,local.newNode);
				} else if (isStruct(arguments.strFields[local.key]) and isXML(arguments.strFields[local.key]["value"])) {
					local.newNode = XMLElemNew(local.otherXML,'INSERTHERE');
					arrayAppend(local.otherXML.XMLRoot.extra.xmlChildren,local.newNode);

					local.tmp = ToString(local.otherXML);
					local.tmp = ReplaceNoCase(local.tmp,"<?xml version=""1.0"" encoding=""UTF-8""?>","");
					local.tmp2 = ToString(arguments.strFields[local.key]["value"]);
					local.tmp2 = ReplaceNoCase(local.tmp2,"<?xml version=""1.0"" encoding=""UTF-8""?>","");

					local.newXML = Replace(local.tmp,"<INSERTHERE/>",local.tmp2);
					local.otherXML = XMLParse(local.newXML);
				}
			}
			</cfscript>

			<!--- storing new data for audit logging after updating extraNode within otherXML --->
			<cfset local.newExtraNode = XMLSearch(local.otherXML,"/report/extra")>
			<cfset setExtraNodeValuesForAuditing(strExtraNodeChanges=local.strExtraNodeChanges, extraNode=local.newExtraNode, strFields=arguments.strFields, valueType="newVal")>

			<cfset updateExtraNodeValuesAndLabelsForAuditing(siteID=arguments.event.getValue('mc_siteInfo.siteID'), strExtraNodeChanges=local.strExtraNodeChanges, strFieldLabels=local.strFieldLabels, useRollDates=local.useRollDates)>

			<!--- remove the <xml> tag, specifically the encoding. It breaks under Railo. --->
			<cfset local.otherXML = replaceNoCase(toString(local.otherXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<!--- prepare log messages by checking for modified values --->
			<cfset local.arrLogMessages = arrayNew(1)>
			<cfset local.sortedExtraNodeKeys = StructKeyArray(local.strExtraNodeChanges)>
			<cfset arraySort(local.sortedExtraNodeKeys, "textnocase")>
			<cfloop array="#local.sortedExtraNodeKeys#" index="local.thisKey">
				<cfif NOT structKeyExists(local.strExtraNodeChanges[local.thisKey], "oldVal") OR NOT len(local.strExtraNodeChanges[local.thisKey]["oldVal"])>
					<cfset local.strExtraNodeChanges[local.thisKey]["oldVal"] = "blank">
				</cfif>
				<cfif NOT structKeyExists(local.strExtraNodeChanges[local.thisKey], "newVal") OR NOT len(local.strExtraNodeChanges[local.thisKey]["newVal"])>
					<cfset local.strExtraNodeChanges[local.thisKey]["newVal"] = "blank">
				</cfif>
				<cfif local.strExtraNodeChanges[local.thisKey]["oldVal"] NEQ local.strExtraNodeChanges[local.thisKey]["newVal"]>
					<cfset local.thisKeyLabel = structKeyExists(local.strFieldLabels, local.thisKey) ?  local.strFieldLabels[local.thisKey] : local.thisKey>
					<cfset arrayAppend(local.arrLogMessages, "#local.thisKeyLabel# changed from [#local.strExtraNodeChanges[local.thisKey]["oldVal"]#] to [#local.strExtraNodeChanges[local.thisKey]["newVal"]#].")>
				</cfif>
			</cfloop>

			<cfset local.notifyChangeSectionsList = "">
			<cfset local.reportRuleID = val(arguments.event.getValue('qryReportInfo').ruleID)>
			<cfif val(arguments.event.getValue('useRuleVersionID_#local.reportRuleID#',0)) GT 0 AND val(arguments.event.getValue('useRuleVersionID_#local.reportRuleID#',0)) NEQ val(arguments.event.getValue('activeRuleVersionID_#local.reportRuleID#',0))>
				<cfset local.notifyChangeSectionsList = listAppend(local.notifyChangeSectionsList,"Rule Builder")>
			</cfif>
			<cfif arrayLen(local.arrLogMessages)>
				<cfset local.notifyChangeSectionsList = listAppend(local.notifyChangeSectionsList,"Extra Report Criteria")>
			</cfif>

			<cfquery name="local.qrySaveReport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;

					CREATE TABLE ##tmpLogMessages (rowID int IDENTITY(1,1), msg varchar(max));
					
					DECLARE @reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.qryReportInfo.reportID#">,
						@actorMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">,
						@crlf varchar(10), @msgjson varchar(max);
					
					SET @crlf = CHAR(13) + CHAR(10);
					
					UPDATE dbo.rpt_SavedReports
					SET otherXML = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.otherXML#">
					WHERE reportID = @reportID;
					
					<cfloop array="#local.arrLogMessages#" index="local.thisMsg">
						INSERT INTO ##tmpLogMessages(msg)
						VALUES(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisMsg#">);
					</cfloop>

					IF EXISTS(SELECT 1 FROM ##tmpLogMessages) BEGIN
						SELECT @msgjson = 'Extra Report Criteria for ' + r.reportName + ' [' + tt.toolDesc + '] has been updated.'
						FROM dbo.rpt_SavedReports AS r
						INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
						WHERE r.reportID = @reportID;

						SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

						SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
						FROM ##tmpLogMessages
						WHERE msg IS NOT NULL;

						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						VALUES ('{ "c":"auditLog", "d": {
							"AUDITCODE":"RPT",
							"ORGID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#"> AS VARCHAR(10)) + ',
							"SITEID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#"> AS VARCHAR(10)) + ',
							"ACTORMEMBERID":' + CAST(@actorMemberID as varchar(20)) + ',
							"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
							"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
					END

					<cfif listLen(local.notifyChangeSectionsList)>
						EXEC platformQueue.dbo.queue_schedReportChangeNotify_addEntry @reportID=@reportID, @actorMemberID=@actorMemberID, @changeSection='#ReReplaceNoCase(local.notifyChangeSectionsList,',',' & ')#';
					</cfif>

					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<!--- reload the qryReportInfo into event now that the otherXML has changed --->
			<cfset arguments.event.getCollection()['qryReportInfo']['otherXML'] = local.otherXML>
		</cfif>	
	</cffunction>

	<cffunction name="setExtraNodeValuesForAuditing" access="public" output="false" returntype="void">
		<cfargument name="strExtraNodeChanges" type="struct" required="yes">
		<cfargument name="extraNode" type="array" required="yes">
		<cfargument name="strFields" type="struct" required="yes">
		<cfargument name="valueType" type="string" required="yes" hint="oldVal or newVal">
		
		<cfscript>
			var local = structNew();

			if(arrayLen(arguments.extraNode)){
				for (local.currentNode in arguments.extraNode[1].XmlChildren) {
					local.thisNodeKey = local.currentNode.XmlName;
					if(structKeyExists(arguments.strFields, local.thisNodeKey)){
						arguments.strExtraNodeChanges[local.thisNodeKey][arguments.valueType] = local.currentNode.XmlText;
						if(StructKeyExists(local.currentNode.XmlAttributes, "afid")){
							local.useRollDatesOld = true;
							arguments.strExtraNodeChanges['rollFld_#local.thisNodeKey#_afid'][arguments.valueType] = local.currentNode.XmlAttributes.afid;
						}
					}
					else if(local.thisNodeKey eq "afrundate"){
						arguments.strExtraNodeChanges['rollAdv_rundate'][arguments.valueType] = local.currentNode.XmlText;
						if(StructKeyExists(local.currentNode.XmlAttributes, "afid")){
							arguments.strExtraNodeChanges['rollAdv_afid'][arguments.valueType] = local.currentNode.XmlAttributes.afid;
						}
					}
				}
			}
		</cfscript>
	</cffunction>

	<cffunction name="updateExtraNodeValuesAndLabelsForAuditing" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="strExtraNodeChanges" type="struct" required="yes">
		<cfargument name="strFieldLabels" type="struct" required="yes">
		<cfargument name="useRollDates" type="boolean" required="yes">

		<cfset var local = structNew()>
		
		<!--- check if rolling date is enabled presently --->
		<cfset local.strOldRollingFields = arguments.strExtraNodeChanges.filter(function(key,value){ return listFindNoCase("rollAdv_,rollFld_", left(key, 8)) AND structKeyExists(value, "oldVal"); })>
		<cfset local.useRollDatesOld = StructCount(local.strOldRollingFields) gt 0>

		<!--- get all rolling date fields --->
		<cfset local.strAllRollingFields = arguments.strExtraNodeChanges.filter(function(key,value){ return listFindNoCase("rollAdv_,rollFld_", left(key, 8)); })>

		<cfif local.useRollDatesOld AND NOT arguments.useRollDates>
			<!--- when rolling date support is turned off, no need to log each field going empty. So clearing related fields from compare struct --->
			<cfloop list="#StructKeyList(local.strAllRollingFields)#" item="local.thisKey">
				<cfset StructDelete(arguments.strExtraNodeChanges, local.thisKey)>
			</cfloop>

			<cfset arguments.strExtraNodeChanges["Rolling Date Support"] = { "oldVal": "Yes", "newVal": "No" } >
		<cfelse>
			<!--- check if there is atleast one rolling date field --->
			<cfif StructCount(local.strAllRollingFields)>
				<!--- adding field label mappings for Date of Advancement fields --->
				<cfset structAppend(arguments.strFieldLabels, {
					"rollAdv_rundate": "Date of Advancement",
					"rollAdv_afid": "Advance Formula for the Date of Advancement"
				}, true)>

				<cfset local.qryAllAFs = getAdvanceFormulas(siteID=arguments.siteID)>
				<cfset local.strAdvFormula = structNew()>
				<cfloop query="local.qryAllAFs">
					<cfset local.strAdvFormula[local.qryAllAFs.AFID] = local.qryAllAFs.afName>
				</cfloop>

				<!--- replacing AFID values with afNames --->
				<cfloop collection="#arguments.strExtraNodeChanges#" item="local.thisKey">
					<cfif local.thisKey EQ "rollAdv_afid" OR (getToken(local.thisKey,1,"_") EQ "rollFld" AND getToken(local.thisKey,3,"_") eq "afid")>
						<cfloop list="oldVal,newVal" item="local.valType">
							<cfif structKeyExists(arguments.strExtraNodeChanges[local.thisKey], local.valType)>
								<cfset arguments.strExtraNodeChanges[local.thisKey][local.valType] = structKeyExists(local.strAdvFormula, arguments.strExtraNodeChanges[local.thisKey][local.valType]) ? local.strAdvFormula[arguments.strExtraNodeChanges[local.thisKey][local.valType]] : "">
							<cfelse>
								<cfset arguments.strExtraNodeChanges[local.thisKey][local.valType] = "No Advance">
							</cfif>
						</cfloop>
					</cfif>

					<!--- adding field label mappings for Advance Method on different Report Criteria Fields --->
					<cfif getToken(local.thisKey,1,"_") EQ "rollFld" AND getToken(local.thisKey,3,"_") EQ "afid" AND structKeyExists(arguments.strFieldLabels, getToken(local.thisKey,2,"_"))>
						<cfset arguments.strFieldLabels[local.thisKey] = "Advance Formula For [#arguments.strFieldLabels[getToken(local.thisKey,2,"_")]#]">
					</cfif>
				</cfloop>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="updateSavedReportWithOtherXML" access="public" output="false" returntype="void">
		<cfargument name="reportID" type="numeric" required="true">
		<cfargument name="otherXML" type="xml" required="true">
		<cfargument name="csrID" type="string" required="false" default="0">
		<cfargument name="changeSection" type="string" required="false" default="">
		<cfargument name="arrayLogMessages" type="array" required="false" default="#arrayNew(1)#">
		<cfargument name="notifyOnlyIfChangeLogs" type="boolean" required="false" default="0">

		<cfset var local = structNew()>

		<cfquery name="local.qrySaveReport" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @siteID int,
					@reportID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">,
					@actorMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">,
					@changeSection varchar(200) = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.changeSection#">,''),
					@crlf varchar(10), @msgjson varchar(max);

				SET @crlf = char(13) + char(10);
				
				SELECT @orgID = s.orgID, @siteID = s.siteID
				FROM dbo.rpt_SavedReports AS r
				INNER JOIN dbo.sites AS s ON s.siteID = r.siteID
				WHERE r.reportID = @reportID
				<cfif val(arguments.csrID)>
					AND r.controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrID#">
				</cfif>;

				IF @siteID IS NULL
					RAISERROR('Report not found.',16,1);
				
				UPDATE dbo.rpt_SavedReports
				SET otherXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.otherXML#">
				WHERE reportID = @reportID;

				<cfif arrayLen(arguments.arrayLogMessages)>
					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;

					CREATE TABLE ##tmpLogMessages (rowID int IDENTITY(1,1), msg varchar(max));

					<cfloop array="#arguments.arrayLogMessages#" index="local.thisMessage">
						INSERT INTO ##tmpLogMessages(msg)
						VALUES(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(reReplace(local.thisMessage, "[[:space:]]{2,}", " ", "all"))#">);
					</cfloop>
					
					SELECT @msgjson = CASE WHEN LEN(@changeSection) > 0 THEN @changeSection ELSE 'Report Settings' END + ' for ' + r.reportName + ' [' + tt.toolDesc + '] has been updated.'
					FROM dbo.rpt_SavedReports AS r
					INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
					WHERE r.reportID = @reportID;

					<cfif arrayLen(arguments.arrayLogMessages) gt 1>
						SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';
					</cfif>

					SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
					FROM ##tmpLogMessages
					WHERE msg IS NOT NULL;

					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES ('{ "c":"auditLog", "d": {
						"AUDITCODE":"RPT",
						"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
						"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
						"ACTORMEMBERID":' + CAST(@actorMemberID as varchar(20)) + ',
						"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
						"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');

					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;
				</cfif>
				
				<cfif NOT arguments.notifyOnlyIfChangeLogs OR arrayLen(arguments.arrayLogMessages)>
					EXEC platformQueue.dbo.queue_schedReportChangeNotify_addEntry
						@reportID=@reportID,
						@actorMemberID=@actorMemberID,
						@changeSection=@changeSection;
				</cfif>
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="saveReportRuleVersion" access="private" output="false" returntype="void">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfif arguments.ruleVersionID>
			<cfset CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin").acceptRuleVersion(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>
		</cfif>
	</cffunction>

	<cffunction name="prepSQL_memberrule" access="private" output="false" returntype="struct">
		<cfargument name="ruleID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.ruleErr = false>
		<cfset local.ruleSQL = "">
		<cfset local.arrJoins = "">
		<cfset local.arrJoinsNoMemberData = "">

		<!--- if rule has no conditions, dont run rule. --->
		<cfset local.objVGRAdmin = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin")>
		<cfset local.numConditions = local.objVGRAdmin.countRuleConditions(ruleID=arguments.ruleID)>
		<cfif local.numConditions gt 0>
			<!--- ensure rule is active and cache refreshed. show error if necessary --->
			<cfset local.msg = local.objVGRAdmin.activateRule(ruleID=arguments.ruleID, forceCache=true)>
			<cfif local.msg is not 0>
				<cfset local.ruleErr = true>
			<cfelse>
				<cfsavecontent variable="local.ruleSQL">
					<cfoutput>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
					CREATE TABLE ##tmpVGRMembers (memberID int PRIMARY KEY);

					EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=#arguments.orgID#, @ruleID=#arguments.ruleID#;
					</cfoutput>
				</cfsavecontent>
				
				<!--- If you change this, search in reports for places that alter it --->
				<cfset local.arrJoins = "inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID">
				<cfset local.arrJoinsNoMemberData = "inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID">
			</cfif>
		</cfif>
		
		<cfset local.returnStruct = { 
				ruleErr = local.ruleErr,
				ruleSQL = local.ruleSQL,
				arrJoins = local.arrJoins,
				arrJoinsNoMemberData = local.arrJoinsNoMemberData
			}>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="prepSQL" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="reportRuleID" type="numeric" required="yes">
		<cfargument name="reportOtherXML" type="string" required="yes">
		<cfargument name="existingFields" type="string" required="no" default="">
		<cfargument name="existingAliases" type="string" required="no" default="">
		<cfargument name="combineAddressDelim" type="string" required="no" default=", ">
		<cfargument name="splitMultiValueDelim" type="string" required="no" default=", ">
		<cfargument name="dropTblName" type="string" required="no" default="">
		<cfargument name="dropColList" type="string" required="no" default="">
		
		<cfset var local = structNew()>
		<cfset local.otherXML = XMLParse(arguments.reportOtherXML)>
		<cfset local.lstAliases = arguments.existingAliases>

		<!--- --------------- --->
		<!--- PREP FIELDSETS  --->
		<!--- --------------- --->
		<cfset local.arrFS = arrayNew(1)>
		<cfloop array="#xmlsearch(local.otherXML,"/report/fieldsets/fieldset")#" index="local.thisFS">
			<cfset arrayAppend(local.arrFS,local.thisFS.xmlAttributes.uid)>
		</cfloop>
		<cfset local.ovNameFormat = XMLSearch(local.otherXML,"string(/report/fieldsets/@nf)")>
		<cfif NOT len(local.ovNameFormat)>
			<cfset local.ovNameFormat = "LSXPFM">
		</cfif>
		<cfset local.ovMaskEmails = XMLSearch(local.otherXML,"string(/report/fieldsets/@mk)")>
		<cfif NOT len(local.ovMaskEmails)>
			<cfset local.ovMaskEmails = "0">
		</cfif>
		<cfset local.arrJoins = []>
		<cfset local.arrJoinsNoMemberData = []>

		<cfif arrayLen(local.arrFS)>
			<!--- get fieldSetIDs --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFS">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT mfs.fieldsetID, MIN(tmp.autoID) AS fieldSetOrder
				FROM dbo.ams_memberFieldSets AS mfs
				INNER JOIN dbo.fn_varcharListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.arrFS)#">,',') AS tmp ON tmp.listitem = mfs.[uid]
				GROUP BY mfs.fieldsetID
				ORDER BY fieldSetOrder;
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.fieldSetIDList = valueList(local.qryFS.fieldSetID)>
		<cfelse>
			<cfset local.fieldSetIDList = "">
		</cfif>

		<!--- --------------------- --->
		<!--- PREP LINKEDFIELDSETS  --->
		<!--- --------------------- --->
		<cfset local.arrFS = arrayNew(1)>
		<cfloop array="#xmlsearch(local.otherXML,"/report/linkedfieldsets/fieldset")#" index="local.thisFS">
			<cfset arrayAppend(local.arrFS,local.thisFS.xmlAttributes.uid)>
		</cfloop>
		<cfif listFindNoCase(local.lstAliases,"mLink")>
			<cfset local.linked_ovNameFormat = XMLSearch(local.otherXML,"string(/report/linkedfieldsets/@nf)")>
			<cfif NOT len(local.linked_ovNameFormat)>
				<cfset local.linked_ovNameFormat = "LSXPFM">
			</cfif>
			<cfset local.linked_ovMaskEmails = XMLSearch(local.otherXML,"string(/report/linkedfieldsets/@mk)")>
			<cfif NOT len(local.linked_ovMaskEmails)>
				<cfset local.linked_ovMaskEmails = "0">
			</cfif>

			<cfif arrayLen(local.arrFS)>
				<!--- get fieldSetIDs --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryLinkedFS">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT mfs.fieldsetID, MIN(tmp.autoID) AS fieldSetOrder
					FROM dbo.ams_memberFieldSets AS mfs
					INNER JOIN dbo.fn_varcharListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.arrFS)#">,',') AS tmp ON tmp.listitem = mfs.[uid]
					GROUP BY mfs.fieldsetID
					ORDER BY fieldSetOrder;
					
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.linked_fieldSetIDList = valueList(local.qryLinkedFS.fieldSetID)>
			<cfelse>
				<cfset local.linked_fieldSetIDList = "">
			</cfif>
		</cfif>

		<!--- -------------- --->
		<!--- NON-CUSTOM CSV --->
		<!--- -------------- --->
		<cfset local.arrCSVDrop = arrayNew(1)>
		<cfif len(arguments.dropTblName)>
			<cfloop list="#arguments.dropColList#" index="local.thisDropCol">
				<cfset local.renameOrDropPrefix = "IF EXISTS(select top 1 column_id from tempdb.sys.columns where [name] = '#local.thisDropCol#' and object_id = object_id('tempdb..#arguments.dropTblName#')) ">
				<cfset arrayAppend(local.arrCSVDrop,"#local.renameOrDropPrefix# ALTER TABLE #arguments.dropTblName# DROP COLUMN #local.thisDropCol#;")>
			</cfloop>
		</cfif>

		<!--- --------------- --->
		<!--- RUN MEMBER RULE --->
		<!--- --------------- --->
		<cfset local.prepMemberRule = prepSQL_memberrule(ruleID=arguments.reportRuleID, orgID=arguments.orgID)>
		<cfset local.ruleSQL = local.prepMemberRule.ruleSQL>
		<cfif len(local.prepMemberRule.arrJoins)>
			<cfset arrayAppend(local.arrJoins,local.prepMemberRule.arrJoins)>
		</cfif>
		<cfif len(local.prepMemberRule.arrJoinsNoMemberData)>
			<cfset arrayAppend(local.arrJoinsNoMemberData,local.prepMemberRule.arrJoinsNoMemberData)>
		</cfif>
		
		<!--- ------------------- --->
		<!--- RUN SUBSCRIBER RULE --->
		<!--- ------------------- --->
		<!--- if rule has no conditions, dont run rule. --->
		<cfif XMLSearch(local.otherXML,"count(/report/subrule//condition)") gt 0>
			<cfsavecontent variable="local.subRuleXSL">
				<cfoutput>
				<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
					<xsl:strip-space elements="subrule conditionset condition" />
					<xsl:output method="xml" encoding="UTF-8" cdata-section-elements="subrule" />
					<xsl:template match="extra" />
					<xsl:template match="subrule">
						<subrule><xsl:apply-templates/></subrule>
					</xsl:template>	
					<xsl:template match="conditionset">
						<xsl:if test="@act = 'exclude'"><xsl:text>NOT </xsl:text></xsl:if>
						<xsl:text>( </xsl:text><xsl:apply-templates/><xsl:text> )</xsl:text>
						<xsl:if test="position() != last()"><xsl:value-of select="concat(' ',../@op,' ')"/></xsl:if>
					</xsl:template>
					<xsl:template match="condition">
						<xsl:text>( </xsl:text>
						<xsl:text>( subType.typeID in (</xsl:text><xsl:value-of select="t"/><xsl:text>) )</xsl:text>
						<xsl:if test="string-length(s) &gt; 0">
							<xsl:text> AND ( sub.subscriptionID in (</xsl:text><xsl:value-of select="s"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(r) &gt; 0">
							<xsl:text> AND ( subrate.rateID in (</xsl:text><xsl:value-of select="r"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(ss) &gt; 0">
							<xsl:text> AND ( substatus.statusID in (</xsl:text><xsl:value-of select="ss"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(ps) &gt; 0">
							<xsl:text> AND ( subpaystatus.statusID in (</xsl:text><xsl:value-of select="ps"/><xsl:text>) )</xsl:text>
						</xsl:if>
						<xsl:if test="string-length(pm) &gt; 0">
							<xsl:text> AND ( 1 = CASE WHEN ('</xsl:text><xsl:value-of select="pm"/><xsl:text>' = 'Y' AND s.payProfileID is not null) OR ('</xsl:text><xsl:value-of select="pm"/><xsl:text>' = 'N' AND s.payProfileID is null) THEN 1 ELSE 0 END )</xsl:text>
						</xsl:if>
						<xsl:choose>
							<xsl:when test="string-length(d/s0) &gt; 0">
								<xsl:text> AND ( s.subStartDate &gt;= '</xsl:text><xsl:value-of select="d/s0"/><xsl:text>' )</xsl:text>
							</xsl:when>
							<xsl:when test="string-length(d/s1) &gt; 0">
								<xsl:text> AND ( s.subStartDate between '</xsl:text><xsl:value-of select="d/s1"/><xsl:text>' and '</xsl:text><xsl:value-of select="d/s2"/><xsl:text>' )</xsl:text>
							</xsl:when>
						</xsl:choose>
						<xsl:choose>
							<xsl:when test="string-length(d/e0) &gt; 0">
								<xsl:text> AND ( s.subEndDate &lt;= '</xsl:text><xsl:value-of select="d/e0"/><xsl:text> 23:59:59.997' )</xsl:text>
							</xsl:when>
							<xsl:when test="string-length(d/e1) &gt; 0">
								<xsl:text> AND ( s.subEndDate between '</xsl:text><xsl:value-of select="d/e1"/><xsl:text>' and '</xsl:text><xsl:value-of select="d/e2"/><xsl:text> 23:59:59.997' )</xsl:text>
							</xsl:when>
						</xsl:choose>
						<xsl:text> )</xsl:text>
						<xsl:if test="position() != last()"><xsl:value-of select="concat(' ',../@op,' ')"/></xsl:if>
					</xsl:template>
					<xsl:template match="/">
						<xsl:apply-templates />
					</xsl:template>
				</xsl:stylesheet>
				</cfoutput>
			</cfsavecontent>

			<cfset local.subscriberRuleXML = XMLTransform(local.otherXML.report.subrule,local.subRuleXSL)>
			<cfset local.subscriberRuleSQL = XMLSearch(local.subscriberRuleXML,"string(subrule)")>

			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblS TABLE (subscriberID int PRIMARY KEY);
		
				insert into @tblS (subscriberID)
				select s.subscriberID
				from dbo.sub_subscribers as s
				inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_types as subType on subType.typeID = sub.typeID
				inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID
				inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
				inner join dbo.sub_rateFrequencies subrf on subrf.RFID = s.RFID
				inner join dbo.sub_rates subrate on subrate.rateID = subrf.rateID
				where subType.siteID = #arguments.siteid#
				and #local.subscriberRuleSQL#;
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrJoins,"inner join @tblS as tblS on tblS.subscriberID = s.subscriberID")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"inner join @tblS as tblS on tblS.subscriberID = s.subscriberID")>
		</cfif>
		
		<!--- ----------------- --->
		<!--- RUN EVENTS FILTER --->
		<!--- ----------------- --->
		<cfif ListLen(XMLSearch(local.otherXML,"string(/report/extra/eidlist/text())"))>
			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblE TABLE (eventID int PRIMARY KEY);
		
				-- get events on site
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
					startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
					displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
					displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300),
					eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
					categoryIDList varchar(max));
				EXEC dbo.ev_getEventsOnSite @siteID=#arguments.siteid#, @startDate=null, @endDate=null, @categoryIDList='';

				insert into @tblE (eventID)
				SELECT distinct tmp.eventid
				FROM ##tmpEventsOnSite as tmp
				inner join dbo.fn_intListToTable('0#XMLSearch(local.otherXML,"string(/report/extra/eidlist/text())")#',',') dg on dg.listitem = tmp.eventID;

				<!--- dont drop the temp table here because we need it in reports --->
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrJoins,"inner join @tblE as tblE on tblE.eventID = e.eventID")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"inner join @tblE as tblE on tblE.eventID = e.eventID")>
		</cfif>
		
		<!--- ------------- --->
		<!--- RUN GL FILTER --->
		<!--- ------------- --->
		<cfif ListLen(XMLSearch(local.otherXML,"string(/report/extra/gllist/text())"))>
			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblGL TABLE (GLAccountID int PRIMARY KEY);
		
				insert into @tblGL (GLAccountID)
				SELECT gl.GLAccountID
				FROM dbo.tr_GLAccounts as gl
				inner join dbo.fn_intListToTable('0#XMLSearch(local.otherXML,"string(/report/extra/gllist/text())")#',',') dg on dg.listitem = gl.GLAccountID
				where gl.orgID = #arguments.orgID#
				and gl.AccountTypeID = 3
				and gl.status <> 'D';
				</cfoutput>
			</cfsavecontent>
			<cfset arrayAppend(local.arrJoins,"inner join @tblGL as tblGL on tblGL.GLAccountID = gl.GLAccountID")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"inner join @tblGL as tblGL on tblGL.GLAccountID = gl.GLAccountID")>
		</cfif>

		<!--- ------------------------- --->
		<!--- RUN REFERRAL PANEL FILTER --->
		<!--- ------------------------- --->
		<cfif ListLen(XMLSearch(local.otherXML,"string(/report/extra/rplist/text())"))>
			<cfsavecontent variable="local.ruleSQL">
				<cfoutput>
				#local.ruleSQL#

				declare @tblRP TABLE (panelID int PRIMARY KEY, panelParentID int);
						
				insert into @tblRP (panelID, panelParentID)
				SELECT distinct p.panelID, p.panelParentID
				FROM dbo.ref_panels as p
				inner join dbo.fn_intListToTable('0#XMLSearch(local.otherXML,"string(/report/extra/rplist/text())")#',',') dg on dg.listitem = p.panelID
				inner join dbo.ref_referrals as r on r.referralID = p.referralID
				inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = r.applicationInstanceID
					and ai.siteID = #arguments.siteID#
				inner join dbo.ref_panelStatus as ps on ps.panelStatusID = p.statusID
					and ps.statusName in ('Inactive','Active');
				</cfoutput>
			</cfsavecontent>
			 
			<cfset arrayAppend(local.arrJoins,"INNER JOIN (SELECT tblRP1.panelid FROM @tblRP tblRP1 INNER JOIN (SELECT tblRP2.panelid FROM @tblRP tblRP2 WHERE tblRP2.panelparentid IS NULL) tblRP3 ON CASE WHEN tblRP1.panelparentid IS NULL THEN tblRP1.panelid ELSE tblRP1.panelparentid END = tblRP3.panelid) AS tblRP ON tblRP.panelid = p.panelid")>
			<cfset arrayAppend(local.arrJoinsNoMemberData,"INNER JOIN (SELECT tblRP1.panelid FROM @tblRP tblRP1 INNER JOIN (SELECT tblRP2.panelid FROM @tblRP tblRP2 WHERE tblRP2.panelparentid IS NULL) tblRP3 ON CASE WHEN tblRP1.panelparentid IS NULL THEN tblRP1.panelid ELSE tblRP1.panelparentid END = tblRP3.panelid) AS tblRP ON tblRP.panelid = p.panelid")>
		</cfif>
		
		<!--- return variables for use in report --->
		<cfset local.sql = { 
			fieldSetIDList=local.fieldSetIDList,
			ovNameFormat=local.ovNameFormat,
			ovMaskEmails=local.ovMaskEmails,
			ruleErr=local.prepMemberRule.ruleErr,
			ruleSQL=local.ruleSQL,
			joinSQLNoMemberData=arrayToList(local.arrJoinsNoMemberData,' '), 
			CSVDropSQL=arrayToList(local.arrCSVDrop,chr(10))
		}>

		<cfif listFindNoCase(local.lstAliases,"mLink")>
			<cfset structAppend(local.sql, {
				linked_fieldSetIDList=local.linked_fieldSetIDList,
				linked_ovNameFormat=local.linked_ovNameFormat,
				linked_ovMaskEmails=local.linked_ovMaskEmails
			})>
		</cfif>

		<cfreturn local.sql>
	</cffunction>

	<cffunction name="getOutputFieldsFromXML" access="private" output="false" returntype="query">
		<cfargument name="outputFieldsXML" type="xml" required="true">

		<cfset var qryFSFields = QueryNew("fieldOrder,fieldcodeSect,allowMultiple,fieldcode,dataTypeCode,dbobject,dbobjectAlias,dbField,fieldLabel,isGrouped","integer,varchar,bit,varchar,varchar,varchar,varchar,varchar,varchar,bit")>
		<cfset var arrFields = XMLSearch(arguments.outputFieldsXML,'/fields/field')>
		<cfset arrFields.map(function(item) {
			qryFSFields.addRow(arguments.item.XmlAttributes);
		})>
		<cfreturn qryFSFields>
	</cffunction>
	
	<cffunction name="makeTempTableAcceptNULLs" access="private" output="false" returntype="string">
		<cfargument name="tableName" required="true" type="string">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.returnSQL">
			<cfoutput>
			/* make all cols in temp table accept nulls */
			declare @dynSQL nvarchar(max);
			declare @coltypes table (datatype varchar(16));

			insert into @coltypes 
			values ('bit'), ('binary'), ('bigint'), ('int'), ('float'), ('date'), ('datetime'), ('text'), ('image'), 
				('money'), ('uniqueidentifier'), ('smalldatetime'), ('tinyint'), ('smallint'), ('sql_variant');

			set @dynSQL = ''
			select @dynSQL = @dynSQL + 
				'ALTER TABLE ###arguments.tableName# ALTER COLUMN ' + quotename(column_name) + ' ' + Data_Type +
				case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
				case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
				case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
				when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
				when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
				case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+'; '
			from tempdb.INFORMATION_SCHEMA.COLUMNS
			where table_name like '###arguments.tableName#%'
			and is_nullable = 'NO'
			order by ordinal_position;
				if len(@dynSQL) > 0 EXEC(@dynSQL);
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.returnSQL>
	</cffunction>

	<cffunction name="markReportAsEditable" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="reportID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditReportRightsByReportID(siteID=arguments.mcproxy_siteID, reportID=arguments.reportID, disregardReadOnlySetting=1)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryMarkReportAsEditable" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">,
						@reportID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">,
						@nowDate datetime = GETDATE();

					UPDATE dbo.rpt_SavedReports
					SET isReadOnly = 0,
						readOnlyUpdateDate = @nowDate
					WHERE siteID = @siteID
					AND reportID = @reportID;

					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					SELECT '{ "c":"auditLog", "d": {
						"AUDITCODE":"RPT",
						"ORGID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#"> AS VARCHAR(10)) + ',
						"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
						"ACTORMEMBERID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#"> as varchar(20)) + ',
						"ACTIONDATE":"' + CONVERT(varchar(20),@nowDate,120) + '",
						"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars('The read-only report ' + r.reportName + ' [' + tt.toolDesc + ']' + ' has been marked as editable again.'),'"','\"') + '" } }'
					FROM dbo.rpt_SavedReports AS r
					INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
					WHERE r.siteID = @siteID
					AND r.reportID = @reportID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFormsForSeminarWebReports" access="public" output="false" returntype="struct">
		<cfargument name="CSALinkIDList" type="string" required="true">
		<cfargument name="loadPoint" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryforms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select distinct f.formID, f.formTitle
			from dbo.tblCreditSponsorsAndAuthorities as csa
			inner join dbo.tblSeminarsAndCredit as sc on sc.CSALinkID = csa.CSALinkID
			inner join dbo.tblSeminars as s on s.seminarID = sc.seminarID
				and s.isDeleted = 0
			inner join dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
			inner join dbo.tblSeminarsAndForms as sf on sf.seminarID = swod.seminarID
				and sf.loadPoint = <cfqueryparam value="#trim(arguments.loadPoint)#" cfsqltype="CF_SQL_VARCHAR">
			inner join formBuilder.dbo.tblForms as f on f.formID = sf.formID and f.isDeleted = 0
			where csa.CSALinkID in (<cfqueryparam value="#arguments.CSALinkIDList#" cfsqltype="CF_SQL_INTEGER" list="true">)
			order by f.formTitle
		</cfquery>

		<cfset local.data.formcount = local.qryforms.recordCount>
		<cfset local.data.arrforms = application.objCommon.queryToArrayOfStructures(local.qryforms)>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showStepRollingDates" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="title" type="string" required="no" default="Rolling Date Support">
		<cfargument name="desc" type="string" required="no" default="">
	
		<cfset var local = structNew()>
		<cfset local.qryReportInfo = arguments.event.getValue('qryReportInfo')>
		<cfset local.otherXML = XMLParse(local.qryReportInfo.otherXML)>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_rollingDates.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getScheduledReports" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="reportID" type="numeric" required="true">

		<cfset var arrschedulereports = arrayNew(1)>

		<cfquery name="arrschedulereports" datasource="#application.dsn.memberCentral.dsn#" returntype="array">
			SELECT sr.reportID as reportid, sr.itemID as itemid, case sr.reportAction when 'customcsv' then 'CSV' else 'PDF' end as reportaction,
				case when sr.interval = 1 then '' else cast(sr.interval as varchar(3)) end as interval, 
				case when sr.interval = 1 then stt.singular else stt.[name] end as intervaltypename, 
				replace(sr.toEmail,';','; ') as toemail, sr.nextRunDate as nextrundate, sr.endRunDate as endrundate
			FROM dbo.rpt_scheduledReports AS sr
			INNER JOIN dbo.scheduledTaskIntervalTypes AS stt ON stt.intervalTypeID = sr.intervalTypeID
			WHERE sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			AND sr.reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">
			ORDER BY sr.nextRunDate, sr.interval
		</cfquery>

		<cfset arrschedulereports.map(function(thisRow) {
			arguments.thisRow['nextrundate'] = len(arguments.thisRow.nextrundate?:'') ? DateTimeFormat(arguments.thisRow.nextrundate,"m/d/yyyy h:nn tt") : '';
			arguments.thisRow['endrundate'] = structKeyExists(arguments.thisRow,'endrundate') ? DateTimeFormat(arguments.thisRow.endrundate,"m/d/yyyy h:nn tt"):'';
			arguments.thisRow['toemaillength'] = listLen(arguments.thisRow.toemail,'; ');
		})>

		<cfreturn { "success":true, "arrschedulereports":arrschedulereports }>
	</cffunction>

	<cffunction name="getAvailableReportFieldSets" access="public" output="false" returntype="query" hint="Returns report field sets">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
			
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryFieldSets">
			SELECT c.categoryID, c.categoryName,mfs.fieldsetID, mfs.fieldsetName, mfs.uid
			FROM dbo.ams_memberFieldsets as mfs
			INNER JOIN cms_categories as c on c.categoryID = mfs.categoryID
			INNER JOIN dbo.ams_memberFieldUsage as u on u.fieldSetID = mfs.fieldSetID
			INNER JOIN dbo.cms_siteResources sr on u.siteResourceID = sr.siteResourceID 
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_siteResourceTypes as srt on sr.resourceTypeID = srt.resourceTypeID and srt.resourceType = 'rpt_ReportSettings'
			WHERE mfs.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			and u.area = 'custom'
			ORDER BY c.categoryName,mfs.fieldsetName
		</cfquery>
		
		<cfreturn local.qryFieldSets>
	</cffunction>

	<cffunction name="getAvailableReportFieldSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": true, "arravailablefieldsets": [] }>

		<cfset local.qryFieldSets = getAvailableReportFieldSets(siteID=arguments.mcproxy_siteID)>
		
		<cfoutput query="local.qryFieldSets" group="categoryID">
			<cfset local.tmpStr = { "categoryid":local.qryFieldSets.categoryID, "categoryname":local.qryFieldSets.categoryName, "arrfieldsets":[] }>
			<cfoutput>
				<cfset arrayAppend(local.tmpStr.arrfieldsets,{ "fieldsetid":local.qryFieldSets.fieldsetID, "fieldsetname":local.qryFieldSets.fieldsetName })>
			</cfoutput>
			<cfset arrayAppend(local.returnStruct.arravailablefieldsets,local.tmpStr)>
		</cfoutput>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAdvanceFormulas" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryAdvanceFormulas = "">
		
		<cfquery name="qryAdvanceFormulas" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="integer">
			ORDER BY afName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryAdvanceFormulas>
	</cffunction>

	<cffunction name="getSubscriptionTypes" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qrySubTypes = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubTypes">
			select typeid, typeName
			from dbo.sub_types
			where siteid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			order by typeName
		</cfquery>

		<cfreturn qrySubTypes>
	</cffunction>

</cfcomponent>