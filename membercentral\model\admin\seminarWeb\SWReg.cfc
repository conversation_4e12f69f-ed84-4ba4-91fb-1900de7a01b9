<cfcomponent output="no">
	
	<cffunction name="doSWReg" access="package" output="false" returntype="string">
		<cfargument name="event" type="any">
		<cfargument name="qryProgram" type="query" required="yes">
		<cfargument name="programType" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset arguments.event.paramValue('regAction','')>
		<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.memberAdmin")>
		<cfset local.objSWCredit = CreateObject("component","model.seminarWeb.SWCredits")>
		
		<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteinfo.sitecode')).qryAssociation>
		
		<!--- switch depending on regAction --->
		<cfswitch expression="#arguments.event.getValue('regAction')#">
			<!--- locator --->
			<cfcase value="locator">
				<cfset local.resultsData = local.objLocator.locateMemberAdmin(event=arguments.event)>
				<cfset local.xmlResultFields = local.resultsData.qryMemberLocator.mc_outputFieldsXML[1]>
				<cfset arguments.event.paramValue('programType',arguments.programType)>
				<cfsavecontent variable="local.data">
					<cfinclude template="SWReg_step1_results.cfm">
				</cfsavecontent>
				<cfreturn local.data>
			</cfcase>
			<!--- usemid (selected a member from step 1) --->
			<cfcase value="usemid">
				<cfset arguments.event.paramValue('mid',int(val(arguments.event.getValue('mid',0))))>
				<cfif arguments.event.getValue('mid') gt 0>
					<cflocation url="#arguments.event.getValue('mainregurl')#&mode=direct&mid=#arguments.event.getValue('mid')#" addtoken="no">
				</cfif>
			</cfcase>
			<!--- newacct (new account from step 1) --->
			<cfcase value="newacct">
				<cfset local.newMemID = local.objLocator.createAccount(event=arguments.event)>
				<cfif local.newMemID gt 0>
					<cfsavecontent variable="local.data">
						<cfoutput>
						Please wait... we will refresh this page automatically.
						<script language="javascript">useMember(#local.newMemID#);</script>
						</cfoutput>
					</cfsavecontent>
					<cfreturn local.data>
				</cfif>
			</cfcase>
			<!--- insert registrant --->
			<cfcase value="insertRegistrant">
				<cftry>
					<cfif arguments.qryProgram.offerCredit is 1>
						<cfset local.strCreditData = getSelectedCreditDataArray(event=arguments.event)>
					<cfelse>
						<cfset local.strCreditData = { structCreditData=structNew(), creditIDList="" }>
					</cfif>

					<cfset local.emailTo = "">
					<cfif len(arguments.event.getValue('chkEmailRegistrant',''))>
						<cfif listFind(arguments.event.getValue('chkEmailRegistrant'),1) and len(arguments.event.getTrimValue('txtEmailRegistrant1',''))>
							<cfset local.emailTo = arguments.event.getTrimValue('txtEmailRegistrant1','')>
						</cfif>
					</cfif>

					<cfset local.overrideSWRatePrice = "">
					<cfif arguments.event.valueExists('override_swRatePrice')>
						<cfset local.overrideSWRatePrice = rereplace(arguments.event.getValue('override_swRatePrice'),"[^0-9.]","","ALL")>
					</cfif>

					<!--- program custom fields --->
					<cfset local.arrCustomFields = []>
					<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
						<cfset local.SWAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
						<cfset local.regCustomFieldsXML = createObject("component","model.admin.common.modules.customFields.customFields").getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='SemWebCatalog', 
							areaName='#arguments.programType#Enrollment', csrid=local.SWAdminSiteResourceID, detailID=arguments.qryProgram.programID, hideAdminOnly=0)>
						<cfset local.strCustomFields = xmlParse(local.regCustomFieldsXML.returnXML).xmlRoot>

						<cfif arrayLen(local.strCustomFields.xmlChildren)>
							<cfloop array="#local.strCustomFields.xmlChildren#" index="local.thisfield">
								<cfset local.tmpAtt = local.thisfield.xmlattributes>
								<cfif len(arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_',''))>
									<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
															displayTypeCode=local.tmpAtt.displayTypeCode, 
															dataTypeCode=local.tmpAtt.dataTypeCode, 
															value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_') }>
									<cfset arrayAppend(local.arrCustomFields,local.tmpStr)>
								</cfif>
							</cfloop>
						</cfif>
					</cfif>

					<cfset local.data = addSWRegistrant(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), programID=arguments.event.getValue('pid'), 
							programType=arguments.programType, rateID=arguments.event.getValue('sw_rate',0), qryProgram=arguments.qryProgram, 
							qryAssociation=local.qryAssociation, memberID=val(arguments.event.getValue('mid')), depoMemberDataID=arguments.event.getValue('depomemberdataid',0), 
							billingState=arguments.event.getValue('billingState',''), stateIDForTax=arguments.event.getValue('stateIDForTax',0),
							zipforTax=arguments.event.getValue('zipforTax',''), arrCustomFields=local.arrCustomFields, strCreditData=local.strCreditData, 
							transactionDate=arguments.event.getValue('transactionDate',now()), overrideSWRatePrice=local.overrideSWRatePrice,
							couponID=int(val(arguments.event.getValue('mcsw_couponID',0))), discountAmt=val(arguments.event.getValue('mcsw_discount',0)), 
							emailTo=local.emailTo, paymentMethod=arguments.event.getValue('SWRegPaymentMethod',''), 
							paymentDescription=arguments.event.getTrimValue('offlinePaymentDesc',''), tspaymentMethod=arguments.event.getTrimValue('tspaymentMethod',''))>
					<cfif local.data.success>
						<cfsavecontent variable="local.data">
							<cfoutput>
							<script language="javascript">
								top.closeUpdateRegistration('#arguments.programType#','grid');
							</script>
							</cfoutput>
						</cfsavecontent>						
					<cfelse>
						<cfsavecontent variable="local.data">
							<cfoutput>
								<div class="p-3">
									<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-danger mb-0" role="alert">
										#urlEncodedFormat(local.data.response)#
									</div>
								</div>
							</cfoutput>
						</cfsavecontent>						
					</cfif>
					<cfreturn local.data>
				<cfcatch type="any">
					<cfreturn application.objError.showAndSendError(cfcatch=cfcatch,objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfcase>
			<cfdefaultcase></cfdefaultcase>
		</cfswitch>
		
		<!--- no program found --->
		<cfif arguments.qryProgram.recordcount is 0> 
			<cfsavecontent variable="local.data">
				<cfoutput>That program does not exist.</cfoutput>
			</cfsavecontent>
		
		<!--- good program --->
		<cfelse>				
			
			<cfset local.qryCurrentRegMember = getRegistrantInfo(mid=arguments.event.getValue('mid',0))>
			<cfset local.qryMemberGroups = CreateObject("component","model.admin.members.members").getMember_groups(memberID=arguments.event.getValue('mid',0), orgID=val(arguments.event.getValue('mc_siteinfo.orgid')), featuredOnly=0)>
			<cfset local.qryMemberActiveSubs = CreateObject("component","model.admin.subscriptions.subscriptions").getMemberSubscriptions(orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=arguments.event.getValue('mid',0), statusCodeList='A')>
			
			<cfif local.qryCurrentRegMember.recordCount>
				<cfset local.qryDepoMemberData = getDepoMemberData(memberID=arguments.event.getValue('mid'), siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
													orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'))>
				<cfset local.strProgramRates = getProgramRates(participantID=local.qryAssociation.participantID, programID=arguments.qryProgram.programID, mid=arguments.event.getValue('mid'), programType=arguments.programType)>

				<cfif local.qryAssociation.handlesOwnPayment is 1>
					<cfset local.qryStateZipForTax = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=arguments.event.getValue('mid'))>
					<cfset local.stateIDForTax = val(local.qryStateZipForTax.stateID)>
					<cfset local.zipForTax = local.qryStateZipForTax.postalCode>
					<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip>
						<cfset local.zipForTax = "">
					</cfif>
				<cfelse>
					<cfset local.billingState = local.qryDepoMemberData.billingState>
					<cfset local.zipForTax = local.qryDepoMemberData.billingZip>
					<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingState=local.billingState).isvalidzip>
						<cfset local.zipForTax = "">
					</cfif>
				</cfif>

				<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
					<cfset local.SWAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
					<cfset local.strProgramRegFields = createObject("component","model.admin.common.modules.customFields.customFields").renderResourceFields(
								siteID=arguments.event.getValue('mc_siteinfo.siteid'), viewMode='bs4', resourceType='SemWebCatalog', areaName='#arguments.programType#Enrollment', 
								csrid=local.SWAdminSiteResourceID, detailID=arguments.qryProgram.programID, hideAdminOnly=0, itemType='#arguments.programType#RegCustom', 
								itemID=0, trItemType='', trApplicationType='')>
				</cfif>
				
				<cfset local.tmpRights = arguments.event.getValue('mc_adminToolInfo.myRights')>
				<cfset local.isProgramPublisher = arguments.qryProgram.publisherOrgCode eq arguments.event.getValue('mc_siteinfo.siteCode')>
				<cfset local.isMemberInSiteAdminGroup = 0>
				<cfif not local.isProgramPublisher>
					<cfset local.isMemberInSiteAdminGroup = CreateObject("component","model.admin.members.memberAdmin").isMemberInSiteAdminGroup(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=arguments.event.getValue('mid'))>
				</cfif>			
				<cfif (local.tmpRights.allowStaffCompRatePublish is 1 and (local.isProgramPublisher or local.isMemberInSiteAdminGroup eq 1)) or local.tmpRights.allowStaffCompRateAll is 1>
					<cfset addStaffCompRateToQuery(local.strProgramRates.qryRatesEligible)>
				</cfif>

				<cfif arguments.qryProgram.offerCredit is 1>
					<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
						<cfset local.qryCredit = local.objSWCredit.getCreditsforSeminar(seminarID=arguments.qryProgram.programID,siteCode=arguments.event.getValue('mc_siteinfo.siteCode')).qryCredit>

						<cfquery name="local.qryIDRequired" dbtype="query">
							SELECT DISTINCT seminarCreditID
							FROM [local].qryCredit
							WHERE isIDRequired = 1
							AND creditIDText <> '';
						</cfquery>
					<cfelseif arguments.programType eq 'SWB'>
						<cfset local.qryItems = CreateObject("component","model.seminarweb.SWBundles").getBundledItemsForCatalog(bundleID=arguments.qryProgram.programID, MCMemberID=0)>
						<cfset local.qryCredit = local.objSWCredit.getCreditsforBundle(bundleID=arguments.qryProgram.programID).qryCredit>

						<cfquery name="local.qryIDRequired" dbtype="query">
							select distinct seminarCreditID
							from [local].qryCredit
							where isIDRequired = 1
							and creditIDText <> ''
						</cfquery>
						<cfquery name="local.qryDistSeminars" dbtype="query">
							select distinct contentID, contentName, offerCertificate
							from [local].qryItems
						</cfquery>
					</cfif>
				</cfif>
			</cfif>
			
			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfinclude template="SWReg.cfm">
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="addStaffCompRateToQuery" access="private" output="no" returntype="void">
		<cfargument name="qryRates" type="query" required="yes">

		<cfset local.rateOrder = 1>

		<cfif arguments.qryRates.recordCount gt 0>
			<cfquery name="local.qryTmpRates" dbtype="query">
				SELECT rateid
				FROM [arguments].qryRates
				WHERE rateGroupingOrder = 0;
			</cfquery>
			<cfset local.rateOrder += local.qryTmpRates.recordCount>
		</cfif>

		<cfif QueryAddRow(arguments.qryRates)>
			<cfset QuerySetCell(arguments.qryRates,"rateid","0")>
			<cfset QuerySetCell(arguments.qryRates,"rateGroupingOrder","0")>
			<cfset QuerySetCell(arguments.qryRates,"rateGrouping","")>
			<cfset QuerySetCell(arguments.qryRates,"rateName","Staff Comp Rate")>
			<cfset QuerySetCell(arguments.qryRates,"rate","0")>
			<cfset QuerySetCell(arguments.qryRates,"rateOrder","#local.rateOrder#")>
			<cfset QuerySetCell(arguments.qryRates,"isHidden","0")>
			<cfset QuerySetCell(arguments.qryRates,"hasAccess","1")>
			<cfset QuerySetCell(arguments.qryRates,"rowNum","0")>
		</cfif>

		<cfif arguments.qryRates.recordCount gt 0>
			<cfset arguments.qryRates.sort('rateGroupingOrder, rateOrder')>
		</cfif>
	</cffunction>

	<cffunction name="isMemberRegisteredForProgram" access="private" output="no" returntype="boolean">
		<cfargument name="mid" type="numeric" required="yes">
		<cfargument name="type" type="string" required="yes">
		<cfargument name="pid" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryRegistered" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			DECLARE @memberID int, @programID int;
			SET @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pid#">;
			SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mid#">;

			<cfif ListFindNoCase("SWL,SWOD",arguments.type)>
				SELECT isRegistered = CASE WHEN dbo.fn_getEnrollmentIDForSeminar(@programID, @memberID) > 0 THEN 1 ELSE 0 END;
			<cfelseif arguments.type eq 'SWB'>
				SELECT isRegistered = CASE WHEN dbo.fn_getBundleOrderIDForBundle(@programID, @memberID) > 0 THEN 1 ELSE 0 END;
			</cfif>
		</cfquery>

		<cfreturn val(local.qryRegistered.isRegistered)>
	</cffunction>
	
	<cffunction name="getRegistrantInfo" access="package" output="no" returntype="query">
		<cfargument name="mid" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @memberID int;
			SET @memberID = <cfqueryparam value="#arguments.mid#" cfsqltype="CF_SQL_INTEGER">;
			SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID;

			select top 1 o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix, 
				mActive.memberID, mActive.firstname, mActive.lastname, me.email, mActive.memberNumber, o.orgcode, 
				case when o.hasPrefix = 1 then mActive.prefix else '' end as prefix, 
				case when o.hasMiddleName = 1 then mActive.middlename else '' end as middleName, 
				case when o.hasSuffix = 1 then mActive.suffix else '' end as suffix, 
				case when o.hasProfessionalSuffix = 1 then mActive.professionalsuffix else '' end as professionalsuffix, 
				mActive.company
			from dbo.ams_members as m
			inner join dbo.organizations as o on o.orgID = @orgID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			inner join dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = mActive.memberID
			inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
			inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
			where m.memberID = @memberID;
		</cfquery>

		<cfreturn local.qryMember>
	</cffunction>

	<cffunction name="getDepoMemberData" access="package" output="no" returntype="struct">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteCode" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.resultData = structNew()>
		<cfset local.TrialSmithAllowedRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Site", functionName="TrialSmithAllowed")>
		
		<!--- get depomemberdataid if there is one --->
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getTLASITESDepoMemberDataIDByMemberID">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.depomemberdataid">
		</cfstoredproc>
		
		<cfif local.depomemberdataid is 0>
			<cftry>
				<!--- create depoaccount on tlasites platform but link only via mcmemberidtemp --->
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createDepoTLASITESAccount">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.TrialSmithAllowedRFID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.depomemberdataid">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.TrialSmithMemberID">
				</cfstoredproc>
				<cfif val(local.depomemberdataid) gt 0>
					<cfquery name="local.qryUpdateDepoMCTemp" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						UPDATE dbo.depomemberdata
						SET MCmemberIDtemp = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
						WHERE depomemberdataid = <cfqueryparam value="#val(local.depomemberdataid)#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
				</cfif>							
			<cfcatch>
				<cfset local.depomemberdataid = 0>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>
		
		<cfquery name="local.qryGetDepoInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select billingState, billingZip, isnull(firstname + ' ','') + isnull(middlename + ' ','') + isnull(lastname,'') as registrantName
			from dbo.depomemberdata
			where depomemberdataid = <cfqueryparam value="#val(local.depomemberdataid)#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset local.resultData.memberid = arguments.memberID>
		<cfset local.resultData.depomemberdataid = val(local.depomemberdataid)>
		<cfset local.resultData.billingState = local.qryGetDepoInfo.billingState>
		<cfset local.resultData.billingZip = local.qryGetDepoInfo.billingZip>
		<cfset local.resultData.registrantName = local.qryGetDepoInfo.registrantName>

		<cfreturn local.resultData>
	</cffunction>

	<cffunction name="getProgramRates" access="private" output="no" returntype="struct">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="mid" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.programType#">
			<cfcase value="SWL">
				<cfstoredproc procedure="swl_getRatesBySeminarIDForAdmin" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.programID#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.mid#">
					<cfprocresult name="local.qryRatesEligible" resultset="1">
					<cfprocresult name="local.qryRatesIneligible" resultset="2">
				</cfstoredproc>
			</cfcase>
			<cfcase value="SWOD">
				<cfstoredproc procedure="swod_getRatesBySeminarIDForAdmin" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.programID#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.mid#">
					<cfprocresult name="local.qryRatesEligible" resultset="1">
					<cfprocresult name="local.qryRatesIneligible" resultset="2">
				</cfstoredproc>
			</cfcase>
			<cfcase value="SWB">
				<cfstoredproc procedure="swb_getRatesByBundleIDForAdmin" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.programID#">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.mid#">
					<cfprocresult name="local.qryRatesEligible" resultset="1">
					<cfprocresult name="local.qryRatesIneligible" resultset="2">
				</cfstoredproc>
			</cfcase>
		</cfswitch>
		
		<cfreturn local>
	</cffunction>

	<cffunction name="getSelectedCreditDataArray" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="true">

		<cfscript>
			var local = structNew();		
			local.structCreditData = structNew();
			local.creditIDList = "";		
		
			local.CreditLinks = arguments.event.getValue('frmcreditlink','');
			if (listLen(local.CreditLinks))
			{
				for (local.i = 1; local.i lte listLen(local.CreditLinks); local.i = local.i + 1) {
					local.thisNum = val(listGetAt(local.CreditLinks,local.i));
					StructInsert(local.structCreditData, local.thisNum, arguments.event.getTrimValue('scid_#local.thisNum#',''));
					local.creditIDList = listAppend(local.creditIDList, local.thisNum);
				}
			}
		</cfscript>

		<cfreturn local>		
	</cffunction>

	<cffunction name="loadRegConfirmation" access="package" output="false" returntype="string">
		<cfargument name="Event" type="any">	

		<cfscript>
			var local = structNew();
			local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteinfo.sitecode')).qryAssociation;

			local.programID = arguments.event.getTrimValue('pid',0);
			local.programType = arguments.event.getValue('ft','');

			switch(local.programType) {
				case "SWL":
					local.strProgram = CreateObject("component","seminarWebSWL").getSeminarForRegistrationByAdmin(seminarID=local.programID, 
						catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'), billingState='', billingZip='', depoMemberDataID=0, memberID=0);
					local.programName = local.strProgram.qrySeminar.seminarName;
					local.programSubTitle = local.strProgram.qrySeminar.seminarSubTitle;
					local.offerCredit = local.strProgram.qrySeminar.offerCredit;
					break;
				case "SWOD": 
					local.strProgram = CreateObject("component","seminarWebSWOD").getSeminarForRegistrationByAdmin(seminarID=local.programID, 
						catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'), billingState='', billingZip='', depoMemberDataID=0, memberID=0);
					local.programName = local.strProgram.qrySeminar.seminarName;
					local.programSubTitle = local.strProgram.qrySeminar.seminarSubTitle;
					local.offerCredit = 1;
					break;
				case "SWB":
					local.objSWBundles = CreateObject("component","model.seminarweb.SWBundles");
					local.qryBundle = local.objSWBundles.getBundleByBundleID(bundleID=local.programID, orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
					local.qryItems = local.objSWBundles.getBundledItemsForCatalog(bundleID=local.programID, MCMemberID=0);
					local.programName = local.qryBundle.bundleName;
					local.programSubTitle = local.qryBundle.bundleSubTitle;
					local.offerCredit = 1;
					break;
			}

			local.rateID = arguments.event.getValue('sw_rate',0);

			local.defaultCurrencyType = "";
			if (arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1)
				local.defaultCurrencyType = " #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#";
		</cfscript>

		<cfset local.qryCurrentRegMember = getRegistrantInfo(mid=arguments.event.getValue('mid',0))>
		<cfif isValid("regex",local.qryCurrentRegMember.email,application.regEx.email)>		
			<cfset local.chkEmailRegistrant = 1>
		<cfelse>
			<cfset local.chkEmailRegistrant = 0>
		</cfif>

		<cfset local.qryRate = getRegRateByRateID(participantID=local.qryAssociation.participantID, rateID=local.rateID, programType=local.programType)>
		<cfset local.rateName = "#local.programName# - #local.qryRate.rateName#">
		<cfset local.rateAmount = val(local.qryRate.rate)>
		<cfset local.ratePriceDisplay = "#dollarformat(local.qryRate.rate)##local.defaultCurrencyType#">

		<!--- total reg fee calc --->
		<cfset local.totalRegFee = local.rateAmount>	
		<cfset local.totalRegFeeConfAmt = "#DollarFormat(local.totalRegFee)##local.defaultCurrencyType#">

		<cfif listFindNoCase("SWL,SWOD",local.programType)>
			<cfset local.SWAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			<cfset local.qryProgramRegFields = QueryNew("fieldID,displayTypeCode,confTitle,confVal","integer,varchar,varchar,varchar")>
			<cfset local.regCustomFieldsXML = createObject("component","model.admin.common.modules.customFields.customFields").getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				resourceType='SemWebCatalog', areaName='#local.programType#Enrollment', csrid=local.SWAdminSiteResourceID, detailID=local.programID, hideAdminOnly=0)>
			<cfset local.regCustomFields = xmlParse(local.regCustomFieldsXML.returnXML).xmlRoot>
			
			<cfif arrayLen(local.regCustomFields.xmlChildren)>
				<cfset local.tmpStr = structNew()>
				
				<cfloop array="#local.regCustomFields.xmlChildren#" index="local.thisfield">
					<cfset local.tmpAtt = local.thisfield.xmlattributes>
					<cfset structInsert(local.tmpStr,local.tmpAtt.fieldID,arguments.event.getValue('cf_#local.tmpAtt.fieldID#_',''))>
					<cfif len(local.tmpStr[local.tmpAtt.fieldID])>
						<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpAtt.displayTypeCode)>
							<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
								<cfif listFind(local.tmpStr[local.tmpAtt.fieldID],local.thisoption.xmlAttributes.valueID) and QueryAddRow(local.qryProgramRegFields)>
									<cfset QuerySetCell(local.qryProgramRegFields,"fieldID",local.tmpAtt.fieldID)>
									<cfset QuerySetCell(local.qryProgramRegFields,"displayTypeCode",local.tmpAtt.displayTypeCode)>
									<cfset QuerySetCell(local.qryProgramRegFields,"confTitle",local.tmpAtt.fieldText)>
									<cfset QuerySetCell(local.qryProgramRegFields,"confVal",local.thisoption.xmlattributes.fieldValue)>
								</cfif>
							</cfloop>
						<cfelseif QueryAddRow(local.qryProgramRegFields)>
							<cfset QuerySetCell(local.qryProgramRegFields,"fieldID",local.tmpAtt.fieldID)>
							<cfset QuerySetCell(local.qryProgramRegFields,"displayTypeCode",local.tmpAtt.displayTypeCode)>
							<cfset QuerySetCell(local.qryProgramRegFields,"confTitle",local.tmpAtt.fieldText)>
							<cfset QuerySetCell(local.qryProgramRegFields,"confVal",local.tmpStr[local.tmpAtt.fieldID])>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>

		<cfif local.offerCredit is 1>
			<cfset local.objSWCredit = CreateObject("component","model.seminarWeb.SWCredits")>
			
			<cfif listFindNoCase("SWL,SWOD",local.programType)>			
				<cfset local.qryCreditTmp = local.objSWCredit.getCreditsforSeminar(seminarID=local.programID,siteCode=arguments.event.getValue('mc_siteinfo.siteCode')).qryCredit>
			<cfelseif local.programType eq 'SWB'>
				<cfset local.qryCreditTmp = local.objSWCredit.getCreditsforBundle(bundleID=local.programID).qryCredit>
			</cfif>
			
			<cfset local.strCreditData = getSelectedCreditDataArray(event=arguments.event)>

			<cfquery name="local.qryCredit" dbtype="query">
				select seminarCreditID, authorityJurisdiction, sponsorname, status, wddxCreditTypes, wddxcreditsAvailable
				from [local].qryCreditTmp
				where seminarCreditID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strCreditData.creditIDList#" list="true">)
				order by authorityCode, sponsorName;
			</cfquery>
		</cfif>

		<cfxml variable="local.cartItemsXML">
			<cfoutput>
				<cart>
					<item mid="#local.qryCurrentRegMember.memberID#" rateid="#local.qryRate.rateID#" itemtype="#local.programType#" />
				</cart>
			</cfoutput>
		</cfxml>
		<cfset local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

		<cfstoredproc procedure="tr_hasValidCoupons" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="SeminarWeb">
			<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.cartItemsXML#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="OUT" cfsqltype="CF_SQL_BIT" variable="local.hasQualifiedCoupons">
		</cfstoredproc>

		<cfsavecontent variable="local.data">
			<cfinclude template="SWReg_stepFinal.cfm">
			<cfinclude template="SWReg_stepConfirmation.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="loadRegPaymentScreen" access="package" output="false" returntype="string">
		<cfargument name="Event" type="any">	

		<cfscript>
			var local = structNew();
			local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteinfo.sitecode')).qryAssociation;

			local.programID = arguments.event.getTrimValue('pid',0);
			local.programType = arguments.event.getValue('ft','');

			switch(local.programType) {
				case "SWL":
					local.strProgram = CreateObject("component","seminarWebSWL").getSeminarForRegistrationByAdmin(seminarID=local.programID, 
						catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'), billingState='', billingZip='', depoMemberDataID=0, memberID=0);
					local.programName = local.strProgram.qrySeminar.seminarName;
					local.acctCode = "7001";
					break;
				case "SWOD": 
					local.strProgram = CreateObject("component","seminarWebSWOD").getSeminarForRegistrationByAdmin(seminarID=local.programID, 
						catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'), billingState='', billingZip='', depoMemberDataID=0, memberID=0);
					local.programName = local.strProgram.qrySeminar.seminarName;
					local.acctCode = "7000";
					break;
				case "SWB":
					local.qryBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleByBundleID(bundleID=local.programID, orgcode=arguments.event.getValue('mc_siteInfo.sitecode'));
					local.programName = local.qryBundle.bundleName;
					if (local.qryBundle.isSWOD is 1)
						local.acctCode = "7000";
					else
						local.acctCode = "7001";
					break;
			}

			local.defaultCurrencyType = "";
			if (arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1)
				local.defaultCurrencyType = " #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#";

			local.rateID = arguments.event.getValue('sw_rate',0);
			local.qryRate = getRegRateByRateID(participantID=local.qryAssociation.participantID, rateID=local.rateID, programType=local.programType);
			local.rateName = "#local.programName# - #local.qryRate.rateName#";

			local.totalRegFee = abs(val(arguments.event.getValue('totalRegFee',0)));
			local.totalRegFeeSalesTax = getTSSalesTaxAmount(orgCode=arguments.event.getValue('mc_siteinfo.sitecode'), amountBilled=local.totalRegFee, 
				billingState=arguments.event.getValue('billingState',''), billingZip=arguments.event.getValue('billingZip',''), acctCode=local.acctCode).salestax;
			local.totalRegFeeIncTax = NumberFormat(local.totalRegFee + local.totalRegFeeSalesTax,'0.00');
			
			local.ratePriceDisplay = "#dollarformat(local.totalRegFeeIncTax)##local.defaultCurrencyType#";
			if (local.totalRegFeeSalesTax gt 0)
				local.ratePriceDisplay = "#local.ratePriceDisplay# (#dollarformat(local.totalRegFee)# + #dollarformat(local.totalRegFeeSalesTax)# tax)";

			// promocode applied
			local.couponApplied = false;
			local.couponID = val(arguments.event.getValue('couponID',0));
			if (local.couponID gt 0) {
				local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.event.getValue('mc_siteinfo.siteID'), couponID=local.couponID);

				if (local.qryCoupon.recordCount) {
					local.discountAmt = val(arguments.event.getValue('couponDiscount',0));
					local.actualTotalRegFee = NumberFormat(local.totalRegFee - local.discountAmt,'0.00');
					local.actualTotalRegFeeSalesTax = getTSSalesTaxAmount(orgCode=arguments.event.getValue('mc_siteinfo.sitecode'), amountBilled=local.actualTotalRegFee, 
						billingState=arguments.event.getValue('billingState',''), billingZip=arguments.event.getValue('billingZip',''), acctCode=local.acctCode).salestax;
					local.actualTotalRegFeeIncTax = NumberFormat(local.actualTotalRegFee + local.actualTotalRegFeeSalesTax,'0.00');

					local.actualRatePriceDisplay = "#dollarformat(local.actualTotalRegFeeIncTax)##local.defaultCurrencyType#";
					if (local.actualTotalRegFeeSalesTax gt 0)
						local.actualRatePriceDisplay = "#local.actualRatePriceDisplay# (#dollarformat(local.actualTotalRegFee)# + #dollarformat(local.actualTotalRegFeeSalesTax)# tax)";

					local.discountApplied = NumberFormat(local.totalRegFeeIncTax - local.actualTotalRegFeeIncTax,'0.00');
					local.discountAppliedDisplay = "#dollarFormat(local.discountApplied)##local.defaultCurrencyType# discount applied";
					local.couponRedeemMessage = local.qryCoupon.redeemDetail;
					local.couponApplied = true;
				}
			}

			local.offerOnlinePaymentMethod = application.objUser.isSuperUser(cfcuser=session.cfcuser);
		</cfscript>

		<!--- SW handles Payment --->
		<cfif local.totalRegFee gt 0 and local.qryAssociation.handlesOwnPayment is 0>
			<!--- hard code the SeminarWeb MP --->
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qrySWProfile">
				SELECT top 1 mp.profileID, mp.profileCode, g.gatewayClass, mp.tabTitle
				FROM dbo.mp_profiles as mp
				INNER JOIN dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
				WHERE mp.siteID = dbo.fn_getSiteIDFromSiteCode('TS')
				AND mp.profileCode = 'AuthorizeCCSemWeb'
				AND mp.status = 'A'
				AND g.isActive = 1
			</cfquery>

			<cfset local.customerIDToUse = "olddid_#arguments.event.getValue('did',0)#">
		<cfelseif local.qryAssociation.handlesOwnPayment is 1>
			<cfset local.data = "You do not have rights to this section.">
			<cfreturn local.data>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="SWReg_stepSWPayment.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getRegRateByRateID" access="private" output="no" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		
		<cfset var qryRate = "">
		
		<cfif arguments.rateID gt 0>
			<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
				<cfstoredproc procedure="sw_getSeminarRateByRateIDForAdmin" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
					<cfprocresult name="qryRate" resultset="1">
				</cfstoredproc>
			<cfelseif arguments.programType eq 'SWB'>
				<cfstoredproc procedure="sw_getBundleRateByRateIDForAdmin" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
					<cfprocresult name="qryRate" resultset="1">
				</cfstoredproc>
			</cfif>
		<cfelse>
			<cfset qryRate = QueryNew("rateid,rateGroupingOrder,rateGrouping,rateName,rate,rateOrder,isHidden,hasAccess,rowNum")>
			<cfset addStaffCompRateToQuery(qryRate)>			
		</cfif>

		<cfreturn qryRate>
	</cffunction>

	<cffunction name="addSWRegistrant" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="qryProgram" type="query" required="true">
		<cfargument name="qryAssociation" type="query" required="true">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="billingState" type="string" required="yes">
		<cfargument name="stateIDForTax" type="numeric" required="yes">
		<cfargument name="zipforTax" type="string" required="yes">
		<cfargument name="arrCustomFields" type="array" required="yes">
		<cfargument name="strCreditData" type="struct" required="yes">
		<cfargument name="transactionDate" type="string" required="yes">
		<cfargument name="overrideSWRatePrice" type="string" required="false" default="">
		<cfargument name="fromSWLSeminarID" type="numeric" required="false" default="0">
		<cfargument name="couponID" type="numeric" required="false" default="0">
		<cfargument name="discountAmt" type="numeric" required="false" default="0">
		<cfargument name="emailTo" type="string" required="false" default="">
		<cfargument name="paymentMethod" type="string" required="false" default="">
		<cfargument name="paymentDescription" type="string" required="false" default="">
		<cfargument name="tspaymentMethod" type="string" required="false" default="">		
		<cfargument name="customText" type="string" required="no" default="">
		<cfargument name="isImportantCustomText" type="boolean" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.data.success = false>
		<cfset local.data.enrollmentID = 0>

		<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.orgID = local.siteInfo.orgID>
		<cfset local.siteID = local.siteInfo.siteID>

		<cfset local.activeMemberID = application.objMember.getActiveMemberID(arguments.memberID)>
		
		<cfset local.rateData = structNew()>
		<cfif len(arguments.overrideSWRatePrice)>
			<cfset local.rateData.price = val(arguments.overrideSWRatePrice)>
		<cfelse>
			<cfset local.qryRate = getRegRateByRateID(participantID=arguments.qryAssociation.participantID, rateid=arguments.rateID, programType=arguments.programType)>
			<cfset local.rateData.price = val(local.qryRate.rate)>
		</cfif>
		
		<cfset local.couponApplied = false>
		<cfif arguments.couponID gt 0>
			<cfset local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=local.siteID, couponID=arguments.couponID)>
			<cfif local.qryCoupon.recordCount>
				<cfset local.couponApplied = true>
			</cfif>
		</cfif>
		
		<!--- SW handles registration --->
		<cfif arguments.qryAssociation.handlesOwnPayment is 0>
			<cfset local.rateData.actualPrice = NumberFormat(local.rateData.price - arguments.discountAmt,'0.00')>

			<!--- sales tax --->
			<cfswitch expression="#arguments.programType#">
				<cfcase value="SWL">
					<cfset local.acctCode = "7001">
				</cfcase>
				<cfcase value="SWOD">
					<cfset local.acctCode = "7000">
				</cfcase>
				<cfcase value="SWB">
					<cfset local.qryBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleByBundleID(bundleID=arguments.programID, orgcode=arguments.sitecode)>
					<cfif local.qryBundle.isSWOD is 1>
						<cfset local.acctCode = "7000">
					<cfelse>
						<cfset local.acctCode = "7001">
					</cfif>
				</cfcase>
			</cfswitch>

			<cfset local.zipforTax = arguments.zipforTax>
			<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingState=arguments.billingState)>
			<cfif local.strBillingZip.isvalidzip>
				<cfset local.zipForTax = local.strBillingZip.billingzip>
			<cfelse>
				<cfthrow message="Invalid State/Zip.">
			</cfif>

			<cfset local.rateData.actualSalestax = getTSSalesTaxAmount(orgCode=arguments.siteCode, amountBilled=local.rateData.actualPrice, 
				billingState=arguments.billingState, billingZip=local.zipforTax, acctCode=local.acctCode).salestax>
			<cfset local.amountToCharge = local.rateData.actualPrice + local.rateData.actualSalestax>

			<cfset local.offerOnlinePaymentMethod = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.strResponse = { success=false, response='Invalid payment method' }>
			
			<cfif local.amountToCharge is 0>
				<cfset local.strResponse.success = true>
				<cfset local.strResponse.response = "">
			<cfelse>
				<cfset local.insertPaymentTrans = false>
				<cfif arguments.paymentMethod EQ 'offline' AND local.offerOnlinePaymentMethod>
					<cfset local.strResponse.success = true>
					<cfset local.strResponse.response = "">
					<cfset local.insertPaymentTrans = true>
				<cfelseif arguments.paymentMethod EQ 'cc'>
					<cfset local.objBuyNow = CreateObject("component","model.buyNow.BuyNow")>
					
					<cfset local.chargeDesc = "SeminarWeb.com Purchase #arguments.qryProgram.programName#">
					<cfset local.customerIDToUse = "olddid_#arguments.depoMemberDataID#">
					<cfset local.TransactionDepoMemberDataID = arguments.depoMemberDataID>
					<cfset local.paymentResponse = local.objBuyNow.chargeCC_TS(customerid=local.customerIDToUse, 
						amount=local.amountToCharge, chargeDesc=local.chargeDesc, merchantProfile='SW', TransactionDepoMemberDataID=local.TransactionDepoMemberDataID)>

					<!--- if payment not successful --->
					<cfif NOT local.paymentResponse.ccsuccess>
						<cfset local.strResponse.response = local.paymentResponse.ccresponse.len() ? local.paymentResponse.ccresponse : "Invalid payment.">
						<cfreturn local.strResponse>
					<cfelse>
						<cfset local.strResponse.success = true>
						<cfset local.strResponse.response = "">
						<cfset local.insertPaymentTrans = isDefined("local.paymentResponse.strInsertTrans")>
					</cfif>
				</cfif>
			</cfif>
			
			<cfif local.strResponse.success>
				<cfif local.amountToCharge GT 0 AND local.insertPaymentTrans>
					<cfquery name="local.qryInsertTransAndCustomerNotes" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @depomemberdataid int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">;

							BEGIN TRAN;
								<cfif arguments.paymentMethod EQ 'cc' AND isDefined("local.paymentResponse.strInsertTrans")>
									INSERT INTO dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid,
										sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
										statsSessionID, merchantOrgCode, refundableAmount)
									select <cfqueryparam value="#local.paymentResponse.strInsertTrans.description#" cfsqltype="CF_SQL_VARCHAR">, 
											<cfqueryparam value="#local.paymentResponse.strInsertTrans.AmountBilled#" cfsqltype="CF_SQL_DOUBLE">,
											0,
										getdate(), depomemberdataid, tlamemberstate, 
										<cfqueryparam value="#local.paymentResponse.strInsertTrans.approvalCode#" cfsqltype="CF_SQL_VARCHAR">,
										<cfqueryparam value="#local.paymentResponse.strInsertTrans.paymentmethod#" cfsqltype="CF_SQL_VARCHAR">,
										<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccTransactionID#" cfsqltype="CF_SQL_VARCHAR">,
										<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccResponseCode#" cfsqltype="CF_SQL_INTEGER">,
										<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccResponseReasonCode#" cfsqltype="CF_SQL_INTEGER">, 1,
										<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
										'SW',
										<cfif local.paymentResponse.strInsertTrans.AmountBilled neq 0>
											<cfqueryparam value="#local.paymentResponse.strInsertTrans.AmountBilled * -1#" cfsqltype="CF_SQL_DOUBLE">
										<cfelse>
											null
										</cfif>
									from dbo.depomemberdata
									where depomemberdataid = @depomemberdataid;

									INSERT INTO dbo.CustomerNotes (depomemberdataid, NoteTypeID, Note)
									VALUES(@depomemberdataid, 1,
										<cfqueryparam value="#abs(local.paymentResponse.strInsertTrans.AmountBilled)# SW Payment made using Authorize.net Customer Profile CustomerID #local.paymentResponse.strInsertTrans.customerid#" cfsqltype="CF_SQL_VARCHAR">
									);
								<cfelseif arguments.paymentMethod EQ 'offline'>
									INSERT INTO dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid,
										sourcestate, paymentmethod, isPayment, enteredByDepomemberdataid, statsSessionID)
									select <cfqueryparam value="#arguments.paymentDescription#" cfsqltype="CF_SQL_VARCHAR">, 
											<cfqueryparam value="#local.amountToCharge * -1#" cfsqltype="CF_SQL_DOUBLE">,
											0, GETDATE(), depomemberdataid, tlamemberstate, 
											<cfqueryparam value="#arguments.tspaymentMethod#" cfsqltype="CF_SQL_VARCHAR">, 1,
											<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.depomemberdataid#">,
											<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">
									from dbo.depomemberdata
									where depomemberdataid = @depomemberdataid;
								</cfif>
							COMMIT TRAN;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				</cfif>

				<cfswitch expression="#arguments.programType#">
					<cfcase value="SWL">
						<cfstoredproc procedure="swl_buySeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.zipforTax#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linksource#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linkterms#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(local.rateData.actualPrice,"0.00")#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(local.rateData.actualSalestax,"0.00")#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
							<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.strResponse.transactionID">
						</cfstoredproc>
						<cfif local.strResponse.transactionID gt 0>
							<cfset local.data.enrollmentID = CreateObject("component","model.seminarweb.SWLiveSeminars").registerUser(depomemberdataID=arguments.depoMemberDataID, 
								seminarID=arguments.programID, transactionID=local.strResponse.transactionID, signUpOrgCode=arguments.siteCode, 
								strCreditData=arguments.strCreditData, performedBy=session.cfcuser.memberdata.depomemberdataid, sendEmailConnectionInstructions=1, 
								emailOverride=arguments.emailTo, MCMemberID=local.activeMemberID)>
						</cfif>
					</cfcase>
					<cfcase value="SWOD">
						<cfstoredproc procedure="swod_buySeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.zipforTax#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linksource#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linkterms#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(local.rateData.actualPrice,"0.00")#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(local.rateData.actualSalestax,"0.00")#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
							<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.strResponse.transactionID">
						</cfstoredproc>
						<cfif local.strResponse.transactionID gt 0>
							<cfset local.data.enrollmentID = CreateObject("component","model.seminarweb.SWODSeminars").registerUser(depomemberdataID=arguments.depoMemberDataID, 
								seminarID=arguments.programID, transactionID=local.strResponse.transactionID, signUpOrgCode=arguments.siteCode, 
								strCreditData=arguments.strCreditData, performedBy=session.cfcuser.memberdata.depomemberdataid, emailOverride=arguments.emailTo, 
								MCMemberID=local.activeMemberID, customText=arguments.customtext, isImportantCustomText=arguments.isImportantCustomText)>
						</cfif>
					</cfcase>
					<cfcase value="SWB">
						<cfstoredproc procedure="swb_buyBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.zipforTax#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linksource#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linkterms#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(local.rateData.actualPrice,"0.00")#">
							<cfprocparam type="In" cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(local.rateData.actualSalestax,"0.00")#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
							<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.strResponse.transactionID">
						</cfstoredproc>
						<cfif local.strResponse.transactionID gt 0>
							<cfset local.data.orderID = CreateObject("component","model.seminarweb.SWBundles").registerUser(depomemberdataID=arguments.depoMemberDataID, 
								bundleID=arguments.programID, transactionID=local.strResponse.transactionID, signUpOrgCode=arguments.siteCode, 
								strCreditData=arguments.strCreditData, performedBy=session.cfcuser.memberdata.depomemberdataid, emailOverride=arguments.emailTo, 
								MCMemberID=local.activeMemberID)>
						</cfif>
					</cfcase>
				</cfswitch>

				<cfif listFindNoCase('SWL,SWOD',arguments.programType) AND arrayLen(arguments.arrCustomFields) AND local.data.keyExists('enrollmentID')>
					<cfquery name="local.qryEnrollmentRegCustom" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;

						DECLARE @enrollmentID int, @fieldID int, @detail varchar(max), @dataID int, @valueID int;
						SET @enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.data.enrollmentID#">;

						<cfloop array="#arguments.arrCustomFields#" index="local.cf">
							<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
								<cfset local.tempSQL = addSWReg_cf_option(itemType='#arguments.programType#RegCustom', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
								#preserveSingleQuotes(local.tempSQL)#
							<cfelseif len(local.cf.value)>
								<cfset local.tempSQL = addSWReg_cf_nonOption(itemType='#arguments.programType#RegCustom', fieldID=local.cf.fieldID, customText=local.cf.value)>
								#preserveSingleQuotes(local.tempSQL)#
							</cfif>
						</cfloop>
					</cfquery>
				</cfif>

				<cfif local.strResponse.transactionID gt 0 and local.couponApplied>
					<cfquery name="local.qryUpdateTransDesc" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						UPDATE dbo.depotransactions 
						SET Description = ISNULL(Description,'') + ' (#local.qryCoupon.couponCode# discounted #DollarFormat(arguments.discountAmt)#)'
						WHERE TransactionID = <cfqueryparam value="#local.strResponse.transactionID#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
				<cfelseif local.strResponse.transactionID gt 0 and arguments.fromSWLSeminarID gt 0>
					<cfquery name="local.qryUpdateTransDesc" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						SET NOCOUNT ON;

						DECLARE @description varchar(600);
						
						SELECT @description = description + ' - ' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="via SWL-#arguments.fromSWLSeminarID# - #arguments.qryProgram.programName#">
						FROM dbo.DepoDocumentTypes
						WHERE acctcode = '7000';

						UPDATE dbo.depotransactions 
						SET Description = @description
						WHERE TransactionID = <cfqueryparam value="#local.strResponse.transactionID#" cfsqltype="CF_SQL_INTEGER">;
					</cfquery>
				</cfif>

				<cfset local.data.success = true>
			</cfif>
		
		<!--- Association handles registration --->
		<cfelse>

			<cfset local.transactionDate = "#dateformat(arguments.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#">
			<cfswitch expression="#arguments.programType#">
				<cfcase value="SWL,SWOD">
					<cfset local.qryGetRateAndProgramGLAccountID = getRateAndProgramGLAccountID(participantID=arguments.qryAssociation.participantID, seminarID=arguments.programID, rateID=arguments.rateID)>
				</cfcase>
				<cfcase value="SWB">
					<cfset local.qryGetRateAndProgramGLAccountID = getRateAndBundleGLAccountID(participantID=arguments.qryAssociation.participantID, bundleID=arguments.programID, rateID=arguments.rateID)>
				</cfcase>
			</cfswitch>

			<cfset local.revenueGLAccountIDToUse = arguments.qryAssociation.revenueGLAccountID>
			<cfif val(local.qryGetRateAndProgramGLAccountID.GLAccountID) gt 0>
				<cfset local.revenueGLAccountIDToUse = local.qryGetRateAndProgramGLAccountID.GLAccountID>
			</cfif>

			<cfset local.zipForTax = arguments.zipForTax>
			<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=arguments.stateIDForTax)>
			<cfif local.strBillingZip.isvalidzip>
				<cfset local.zipForTax = local.strBillingZip.billingzip>
			<cfelse>
				<cfthrow message="Invalid State/Zip.">
			</cfif>

			<!--- sales tax --->
			<cfset local.rateData.salestax = createObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.revenueGLAccountIDToUse,
					saleAmount=local.rateData.price, transactionDate=local.transactionDate, stateIDForTax=arguments.stateIDforTax, zipForTax=local.zipforTax).totalTaxAmt>

			<cfquery name="local.qryAddEnrollment" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int, @siteID int, @siteCode varchar(10), @participantID int, @recordedByMemberID int, @statsSessionID int,
						@depomemberdataid int, @rateTransactionID int, @stateIDforTax int, @zipForTax varchar(25), @GLAccountID int, @enrollmentID int,
						@assignedToMemberID int, @invoiceProfileID int, @deferredGLAccountID int, @invoiceID int, @invoiceNumber varchar(18),
						@deferredDateStr varchar(10), @transactionDate datetime, @deferredDate datetime, @rateAmount decimal(18,2), @rateTaxAmount decimal(18,2),
						@invDue decimal(18,2), @xmlSchedule xml, @swMerchantProfiles varchar(1000), @programID int, @programName varchar(200),
						@itemType varchar(10), @itemID int, @subItemID int, @fieldID int, @detail varchar(max), @dataID int, @valueID int, @couponID int, 
						@discountAmount decimal(18,2), @adjTransactionID int, @maxOverallUsageCount int, @maxMemberUsageCount int, @redemptionCount int, 
						@redemptionCountPerMember int, @couponCode varchar(15), @SWRedemptionCount int, @orderID int, @internalNotes varchar(500), 
						@seminarCreditID int, @idnumber varchar(50), @nowDate datetime = GETDATE();
					
					set @transactionDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.transactionDate#">;
					set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">;
					set @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;
					set @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryAssociation.participantID#">;
					set @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
					set @assignedToMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.activeMemberID#">
					set @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.statsSessionID#">;
					set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
					set @depomemberdataid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">;
					set @GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.revenueGLAccountIDToUse#">;
					set @programName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.qryProgram.programName#">;
					
					<cfif arguments.stateIDforTax gt 0>
						set @stateIDforTax = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.stateIDforTax#">;
					</cfif>
					<cfif len(local.zipforTax)>
						set @zipForTax = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.zipforTax#">
					</cfif>

					set @rateAmount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.rateData.price#">;
					set @rateTaxAmount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.rateData.salestax#">;

					select @invoiceProfileID = invoiceProfileID
					from dbo.tr_GLAccounts
					where glAccountID = @GLAccountID;

					select @swMerchantProfiles = dbo.sortedIntList(profileID)
					from seminarWeb.dbo.tblParticipantMerchantProfiles
					where participantID	= @participantID;
					
					BEGIN TRAN;
						-- add enrollment
						<cfswitch expression="#arguments.programType#">
							<cfcase value="SWL">
								EXEC seminarWeb.dbo.swl_addEnrollment @depomemberdataid=@depomemberdataid, @seminarID=@programID,
									@orgcode=@siteCode, @isFeeExempt=0, @bundleOrderID=null, @transactionID=null, 
									@bypassFeesCalc=1, @MCMemberID=@assignedToMemberID, @enrollmentID=@enrollmentID OUTPUT;

								select @itemType = 'SWLRate', @itemID = @enrollmentID, @subItemID = @programID;
							</cfcase>
							<cfcase value="SWOD">
								EXEC seminarWeb.dbo.swod_addEnrollment @depomemberdataid=@depomemberdataid, @seminarID=@programID,
									@orgcode=@siteCode, @isFeeExempt=0, @bundleOrderID=null, @transactionID=null, 
									@bypassFeesCalc=1, @MCMemberID=@assignedToMemberID, @enrollmentID=@enrollmentID OUTPUT;

								select @itemType = 'SWODRate', @itemID = @enrollmentID, @subItemID = @programID;
							</cfcase>
							<cfcase value="SWB">
								EXEC seminarWeb.dbo.swb_addEnrollment @depomemberdataid=@depomemberdataid, @bundleID=@programID,
									@signuporgcode=@siteCode, @transactionID=null, @bypassFeesCalc=1, @MCMemberID=@assignedToMemberID, @orderID=@orderID OUTPUT;

								select @itemType = 'SWBRate', @itemID = @orderID, @subItemID = @programID;
							</cfcase>
						</cfswitch>

						<cfif listFindNoCase('SWL,SWOD',arguments.programType) AND arrayLen(arguments.arrCustomFields)>
							<cfloop array="#arguments.arrCustomFields#" index="local.cf">
								<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
									<cfset local.tempSQL = addSWReg_cf_option(itemType='#arguments.programType#RegCustom', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
									#preserveSingleQuotes(local.tempSQL)#
								<cfelseif len(local.cf.value)>
									<cfset local.tempSQL = addSWReg_cf_nonOption(itemType='#arguments.programType#RegCustom', fieldID=local.cf.fieldID, customText=local.cf.value)>
									#preserveSingleQuotes(local.tempSQL)#
								</cfif>
							</cfloop>
						</cfif>

						<!--- credits --->
						<cfif arguments.qryProgram.offerCredit is 1 and listFindNoCase('SWL,SWOD,SWB',arguments.programType)>
							DECLARE @enrollmentIDForBundleCredit int;
							<cfloop collection="#arguments.strCreditData.structCreditData#" item="local.key">
								SET @seminarCreditID = #int(val(local.key))#;
								SET @idnumber = '#replace(arguments.strCreditData.structCreditData[local.key],"'","''","ALL")#';

								<cfif arguments.programType eq "SWB">
									SET @enrollmentIDForBundleCredit = NULL;
									
									SELECT @enrollmentIDForBundleCredit = e.enrollmentID
									FROM seminarWeb.dbo.tblEnrollments as e
									INNER JOIN seminarWeb.dbo.tblSeminarsAndCredit as sac on sac.seminarID = e.seminarID
										AND sac.seminarCreditID = @seminarCreditID
									WHERE e.bundleOrderID = @orderID;

									IF @enrollmentIDForBundleCredit IS NOT NULL
										EXEC seminarWeb.dbo.sw_addEnrollmentCredits @enrollmentID=@enrollmentIDForBundleCredit, 
											@seminarCreditID=@seminarCreditID, @idnumber=@idnumber, @recordedByMemberID=@recordedByMemberID;
								<cfelse>
									EXEC seminarWeb.dbo.sw_addEnrollmentCredits @enrollmentID=@enrollmentID, @seminarCreditID=@seminarCreditID, 
										@idnumber=@idnumber, @recordedByMemberID=@recordedByMemberID;
								</cfif>

							</cfloop>
						</cfif>

						set @deferredDate = @transactionDate;
						set @deferredDateStr = convert(varchar(10),@deferredDate,101);

						-- create invoice assigned to payer based on invoice profile
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID,
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@transactionDate, @dateDue=@transactionDate,
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						IF @swMerchantProfiles is not null
							EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@swMerchantProfiles;

						-- handle deferred revenue
						select @xmlSchedule = null, @deferredGLAccountID = null;
						select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
						IF @deferredGLAccountID is not null
							set @xmlSchedule = '<rows><row amt="#local.rateData.price#" dt="' + @deferredDateStr + '" /></rows>';

						-- record rate transaction
						EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID,
							@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@recordedByMemberID,
							@statsSessionID=@statsSessionID, @status='Active', @detail=@programName, @parentTransactionID=null,
							@amount=@rateAmount, @transactionDate=@transactionDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, 
							@stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, @taxAmount=@rateTaxAmount, @bypassTax=0, @bypassInvoiceMessage=0,
							@bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@rateTransactionID OUTPUT;
						
						EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='SemWebCatalog', @transactionID=@rateTransactionID,
							@itemType=@itemType, @itemID=@itemID, @subItemID=@subItemID;

						-- promo code applied
						<cfif local.couponApplied>
							set @couponID = #int(val(arguments.couponID))#;
							set @discountAmount = #val(arguments.discountAmt)# * - 1;

							-- check if coupon max usage count met
							select @couponCode = couponCode, @maxOverallUsageCount = maxOverallUsageCount, @maxMemberUsageCount = maxMemberUsageCount,
								@SWRedemptionCount = seminarwebXML.value('(/sw/pid/p[text()=sql:variable("@programID")]/@rc)[1]', 'int')
							from dbo.tr_coupons
							where siteID = @siteID
							and couponID = @couponID;

							set @SWRedemptionCount = isnull(@SWRedemptionCount,1);

							; with redemptions as(
								select distinct td.itemType, td.itemID, td.redemptionCount, 
									case when m.memberID is not null then td.redemptionCount else 0 end as memberRedemptionCount
								from dbo.tr_coupons as c
								inner join dbo.tr_transactionDiscounts as td on td.orgID = @orgID 
									and td.couponID = c.couponID 
									and td.isActive = 1
									and c.couponID = @couponID
								inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
								left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID and m.activeMemberID = @assignedToMemberID
							)
							select @redemptionCount = SUM(redemptionCount), @redemptionCountPerMember = SUM(memberRedemptionCount)
							from redemptions;

							IF @maxOverallUsageCount > 0 AND ISNULL(@redemptionCount,0) + @SWRedemptionCount > @maxOverallUsageCount
								SET @internalNotes = 'Overall Redemptions Count for Coupon Code(' + @couponCode + ') exceeded; ';

							IF @maxMemberUsageCount > 0 AND ISNULL(@redemptionCountPerMember,0) + @SWRedemptionCount > @maxMemberUsageCount
								SET @internalNotes = ISNULL(@internalNotes,'') + 'Overall Redemptions Count Per Member for Coupon Code(' + @couponCode + ') exceeded; ';
							
							IF LEN(ISNULL(@internalNotes,'')) > 0 BEGIN
								<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
									UPDATE seminarweb.dbo.tblEnrollments
									SET internalNotes = ISNULL(internalNotes,'') + @internalNotes
									WHERE enrollmentID = @enrollmentID;
								<cfelseif arguments.programType eq "SWB">
									UPDATE seminarweb.dbo.tblBundleOrders
									SET orderNotes = ISNULL(orderNotes,'') + @internalNotes
									WHERE orderID = @orderID;
								</cfif>
							END

							EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteid, @recordedByMemberID=@recordedByMemberID,
								@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@transactionDate,
								@saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @couponID=@couponID,
								@itemType=@itemType, @itemID=@itemID, @redemptionCount=@SWRedemptionCount, 
								@transactionID=@adjTransactionID OUTPUT;
						</cfif>
					
						-- close invoice
						IF @invoiceID IS NOT NULL
							EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;
					
						-- calculate revenue and reg fees
						IF @itemType = 'SWBRate'
							EXEC seminarWeb.dbo.sw_calculateBundleOrderRevenueAndRegFees @bundleOrderID=@itemID, @calcOnly=0, @asOfDate=@nowDate;
						ELSE
							EXEC seminarWeb.dbo.sw_calculateEnrollmentRevenueAndRegFees @enrollmentID=@itemID, @calcOnly=0, @asOfDate=@nowDate;
					COMMIT TRAN;

					DECLARE @xmlResult xml;
					SELECT @xmlResult = (
						SELECT (
							select enrollmentID, invoiceID, orderID, success
							from (select isnull(@enrollmentID,0) as enrollmentID, @invoiceID as invoiceID, isnull(@orderID,0) as orderID, 1 as success) as r
							FOR XML AUTO, root('reg'), TYPE
							)
						FOR XML RAW('regresult'), TYPE
					);
					select @xmlResult as xmlResult;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.xmlResult = xmlParse(local.qryAddEnrollment.xmlResult)>	
			<cfset local.qryAddEnrollmentSuccess = val(XMLSearch(local.xmlResult,"string(/regresult/reg/r/@success)"))>
			<cfset local.enrollmentID = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@enrollmentID)")>
			<cfset local.qryAddEnrollmentInvoiceID = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@invoiceID)")>
			<cfset local.bundleOrderID = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@orderID)")>
			
			<cfif local.qryAddEnrollmentSuccess is 1>
				<!--- send confirmations and record credits --->
				<cfswitch expression="#arguments.programType#">
					<cfcase value="SWL">
						<cfset createObject("component","seminarWebSWL").registerSWLUser(enrollmentID=local.enrollmentID, seminarID=arguments.programID,
							signUpOrgCode=arguments.siteCode, performedBy=session.cfcuser.memberdata.depomemberdataid, 
							sendEmailConnectionInstructions=1, emailOverride=arguments.emailTo)>
					</cfcase>
					<cfcase value="SWOD">
						<cfset createObject("component","model.seminarweb.SWODEmails").sendConfirmation(seminarID=arguments.programID, enrollmentID=local.enrollmentID,
							performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="registerInstructions", 
							orgcode=arguments.siteCode, emailOverride=arguments.emailTo, customText=arguments.customtext, isImportantCustomText=arguments.isImportantCustomText)>
					</cfcase>
					<cfcase value="SWB">
						<cfset local.objAdminSWL = createObject("component","model.admin.seminarweb.seminarWebSWL")>
						
						<!--- each SWL program should register at stream provider. supress individual program connection instructions. --->
						<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							select e.enrollmentID, e.seminarID
							from dbo.tblEnrollments as e
							inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = e.seminarID
							where e.bundleOrderID  = <cfqueryparam value="#local.bundleOrderID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
						<cfloop query="local.qryEnrollments">
							<cfset local.objAdminSWL.registerSWLUser(enrollmentID=local.qryEnrollments.enrollmentID, seminarID=local.qryEnrollments.seminarID,
								signUpOrgCode=arguments.siteCode, performedBy=session.cfcuser.memberdata.depomemberdataid,
								sendEmailConnectionInstructions=0, emailOverride=arguments.emailTo)>
						</cfloop>

						<!--- email confirmation of bundle purchase --->
						<cfset createObject("component","model.seminarweb.SWBundlesEmails").sendConfirmation(bundleID=arguments.programID, depomemberdataID=arguments.depoMemberDataID,
							signUpOrgCode=arguments.siteCode, performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="registerInstructions", emailOverride=arguments.emailTo)>
					</cfcase>
				</cfswitch>

				<cfset local.data.success = true>
				<cfset local.data.enrollmentID = local.enrollmentID>
			</cfif>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addSWReg_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.addSWRegFieldSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				EXEC membercentral.dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@enrollmentID, @itemType='#arguments.itemType#',
					@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.addSWRegFieldSQL = "">
		</cfif>

		<cfreturn local.addSWRegFieldSQL>
	</cffunction>

	<cffunction name="addSWReg_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.addSWRegFieldSQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					
					EXEC membercentral.dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@enrollmentID, @itemType='#arguments.itemType#',
						@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addSWRegFieldSQL>
	</cffunction>

	<cffunction name="getTSSalesTaxAmount" access="public" output="false" returntype="struct">
		<cfargument name="orgCode" type="string" required="yes">
		<cfargument name="amountBilled" type="numeric" required="yes">
		<cfargument name="billingState" type="string" required="yes">
		<cfargument name="billingZip" type="string" required="yes">
		<cfargument name="acctCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery name="local.qrySalesTaxAmount" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteCode varchar(10) = <cfqueryparam value="#arguments.orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@billingState varchar(2) = <cfqueryparam value="#arguments.billingState#" cfsqltype="CF_SQL_VARCHAR">,
				@billingZIP varchar(10) = <cfqueryparam value="#arguments.billingZip#" cfsqltype="CF_SQL_VARCHAR">,
				@acctCode varchar(4) = <cfqueryparam value="#arguments.acctCode#" cfsqltype="CF_SQL_VARCHAR">,
				@amountBilled numeric(18,2) = <cfqueryparam value="#arguments.amountBilled#" cfsqltype="CF_SQL_DOUBLE" scale="2">;

			SELECT dbo.fn_tax_getTax(@siteCode,@billingState,@billingZIP,@acctCode,@amountBilled) as salesTax;
		</cfquery>

		<cfset local.data.salesTax = val(local.qrySalesTaxAmount.salesTax)>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAssociationSalesTaxAmount" access="private" output="false" returntype="struct">
		<cfargument name="saleGLAccountID" type="numeric" required="yes">
		<cfargument name="saleAmount" type="numeric" required="yes">
		<cfargument name="transactionDate" type="string" required="yes">
		<cfargument name="stateIDForTax" type="numeric" required="yes">
		<cfargument name="zipForTax" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfset local.transactionDate = "#dateformat(arguments.transactionDate,"m/d/yyyy")# #timeformat(now(),"h:mm tt")#">

		<cfset local.data.salesTax = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=arguments.saleGLAccountID, 
												saleAmount=arguments.saleAmount, transactionDate=local.transactionDate, stateIDForTax=arguments.stateIDForTax, 
												zipForTax=arguments.zipForTax).totalTaxAmt>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getStateZipForTax" access="private" output="no" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">

		<cfset var qryStateID = "">

		<cfquery name="qryStateID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">,
				@memberID int = <cfqueryparam value="#arguments.memberid#" cfsqltype="cf_sql_integer">;

			select ma.stateID, ma.postalCode
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
				AND matag.memberID = ma.memberID 
				AND matag.addressTypeID = ma.addressTypeID
			inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
				AND matagt.addressTagTypeID = matag.addressTagTypeID 
				AND matagt.addressTagType = 'Billing'
			where ma.orgID = @orgID
			and ma.memberid = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryStateID>
	</cffunction>

	<cffunction name="getRateandProgramGLAccountID" access="private" output="no" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryGetRateAndProgramGLAccountID = "">

		<cfquery name="qryGetRateAndProgramGLAccountID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			set nocount on;

			declare @rateGLAccountID int, @programGLAccountID int, @GLAccountID int;

			select @rateGLAccountID = revenueGLAccountID 
			from dbo.tblSeminarsAndRates
			where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			and participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			select @programGLAccountID = revenueGLAccountID 
			from dbo.tblSeminars 
			where seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
		
			set @GLAccountID = coalesce(@rateGLAccountID,@programGLAccountID);

			select @rateGLAccountID as rateGLAccountID, @programGLAccountID as programGLAccountID, @GLAccountID as GLAccountID;
		</cfquery>

		<cfreturn qryGetRateAndProgramGLAccountID>
	</cffunction>

	<cffunction name="getRateAndBundleGLAccountID" access="private" output="no" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryGetRateAndBundleGLAccountID = "">

		<cfquery name="qryGetRateAndBundleGLAccountID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			set nocount on;

			declare @rateGLAccountID int, @bundleGLAccountID int, @GLAccountID int;

			select @rateGLAccountID = revenueGLAccountID 
			from dbo.tblBundlesAndRates
			where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			and participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			select @bundleGLAccountID = revenueGLAccountID 
			from dbo.tblBundles 
			where bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">;
		
			set @GLAccountID = coalesce(@rateGLAccountID,@bundleGLAccountID);

			select @rateGLAccountID as rateGLAccountID, @bundleGLAccountID as bundleGLAccountID, @GLAccountID as GLAccountID;
		</cfquery>

		<cfreturn qryGetRateAndBundleGLAccountID>
	</cffunction>

	<cffunction name="validateCouponCode" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="couponCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="rateAmtOverride" type="string" required="true">
		
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { success=false, isvalidcoupon=false, couponresponse="Invalid Promo Code", discount=0 };

		arguments.couponCode = trim(arguments.couponCode);
		
		// if no length 
		if (len(arguments.couponCode) is 0) return local.returnStruct;

		cfxml(variable="local.cartItemsXML") {
			writeOutput('<cart><item mid="#arguments.memberID#" rateid="#arguments.rateID#" itemtype="#arguments.programType#" /></cart>');
		}

		local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','');

		var sqlParams = {
			siteID = { value=arguments.mcproxy_siteID, cfsqltype="CF_SQL_INTEGER" },
			applicationType = { value="SeminarWeb", cfsqltype="CF_SQL_VARCHAR" },
			cartItemsXML = { value=local.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
			couponCode = { value=arguments.couponCode, cfsqltype="CF_SQL_VARCHAR" }
		};

		var qryValidCoupon = queryExecute("
			SET NOCOUNT ON;
			
			DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
				@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml,
				@isAvailable bit;
			
			EXEC dbo.tr_isValidCouponCodeForAdmin @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
				@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
				@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT, @isAvailable=@isAvailable OUTPUT;
			
			SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML, @isAvailable AS isAvailable;
			", 
			sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
		);

		local.couponID = val(qryValidCoupon.couponID);
		local.returnStruct.couponResponse = qryValidCoupon.couponMessage;

		// valid coupon
		if (local.couponID AND arrayLen(XMLSearch(qryValidCoupon.qualifiedCartItemsXML,'/cart/item[@rateid="#arguments.rateID#"]'))) {
			local.returnStruct.isValidCoupon = true;
			local.strRegDiscount = getRegistrationDiscount(mcproxy_siteID=arguments.mcproxy_siteID, programType=arguments.programType,
				couponID=local.couponID, rateID=arguments.rateID, rateAmtOverride=arguments.rateAmtOverride);
			local.returnStruct.couponID = val(local.couponID);
			local.returnStruct.isAvailable = val(qryValidCoupon.isAvailable);
			local.returnStruct.discount = local.strRegDiscount.discount;

			local.returnStruct.success = true;
		}		
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="getRegistrationDiscount" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="rateAmtOverride" type="string" required="true">	
		
		<cfset var local = structNew()>
		<cfset local.strDiscount = { success=false, discount=0 }>

		<cfset local.participantID = CreateObject("component","seminarWebSWCommon").getParticipantIDFromSiteID(siteID=arguments.mcproxy_siteID)>
		<cfset local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.mcproxy_siteID, couponID=arguments.couponID)>

		<cfset local.strCoupon = {  couponID = local.qryCoupon.couponID,
									couponCode = local.qryCoupon.couponCode,
									pctOff = local.qryCoupon.pctOff,
									pctOffMaxOff = local.qryCoupon.pctOffMaxOff,
									amtOff = local.qryCoupon.amtOff,
									redeemDetail = local.qryCoupon.redeemDetail,
									invoiceDetail = local.qryCoupon.invoiceDetail }>
		
		<cfif len(arguments.rateAmtOverride)>
			<cfset local.rateAmount = val(rereplace(arguments.rateAmtOverride,"[^0-9.]","","ALL"))>
		<cfelse>
			<cfset local.qryRate = getRegRateByRateID(participantID=local.participantID, rateID=arguments.rateID, programType=arguments.programType)>
			<cfset local.rateAmount = val(local.qryRate.rate)>
		</cfif>	
		
		<cfset local.strRateDiscount = getAmountAfterCouponApplied(strCoupon=local.strCoupon, rateAmount=local.rateAmount)>		

		<cfset local.strDiscount["discount"] = local.strRateDiscount.discount>
		<cfset local.strDiscount["success"] = true>

		<cfreturn local.strDiscount>
	</cffunction>

	<cffunction name="getAmountAfterCouponApplied" access="private" output="false" returntype="struct">
		<cfargument name="strCoupon" type="struct" required="true">
		<cfargument name="rateAmount" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.strDiscount = { finalAmt=0, discount=0 }>

		<cfif arguments.rateAmount eq 0>
			<cfreturn local.strDiscount>
		</cfif>
		<cfif val(arguments.strCoupon.pctOff) gt 0>
			<cfset local.strDiscount.discount = NumberFormat((arguments.rateAmount * (arguments.strCoupon.pctOff / 100)),"9.99")>
			<cfif val(arguments.strCoupon.pctOffMaxOff) gt 0 and max(arguments.strCoupon.pctOffMaxOff,local.strDiscount.discount) eq local.strDiscount.discount>
				<cfset local.strDiscount.discount = arguments.strCoupon.pctOffMaxOff>
			</cfif>
		<cfelseif val(arguments.strCoupon.amtOff) gt 0>
			<cfset local.strDiscount.discount = arguments.strCoupon.amtOff>
		</cfif>

		<cfset local.strDiscount.discount = local.strDiscount.discount>
		<cfset local.strDiscount.finalAmt = NumberFormat(arguments.rateAmount - local.strDiscount.discount,"9.99")>
		
		<cfif local.strDiscount.finalAmt lte 0>
			<cfset local.strDiscount.finalAmt = 0>
			<cfset local.strDiscount.discount = arguments.rateAmount>
		</cfif>

		<cfreturn local.strDiscount>
	</cffunction>

	<cffunction name="getRegisteredSWLPrograms" access="public" output="false" returntype="struct">
		<cfargument name="mid" type="numeric" required="true">
		<cfargument name="seminarIDList" type="string" required="true">

		<cfset var qrySeminarsRegistered = "">

		<cfquery name="qrySeminarsRegistered" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT DISTINCT s.seminarID
			from dbo.tblSeminars as s
			inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
			inner join memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarIDList#">,',') as tmp on tmp.listItem = s.seminarID
			inner join dbo.tblEnrollments as e ON e.seminarID = s.seminarID
				and e.isActive = 1
			inner join membercentral.dbo.ams_members as m on m.memberID = e.MCMemberID
			inner join membercentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
				and m2.memberID = <cfqueryparam value="#arguments.mid#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>

		<cfreturn { "success": true, "registeredseminaridlist": valueList(qrySeminarsRegistered.seminarID) }>
	</cffunction>

</cfcomponent>