﻿<cfcomponent output="false">

	<cffunction name="insertCartItem" access="public" output="false" returntype="numeric" hint="insert Cart Item in DB">
		<cfargument name="Event" type="any">
		<cfargument name="storeID" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="statSessionID" type="numeric" required="true" />
		<cfargument name="itemID" type="numeric" required="true" />
		<cfargument name="formatID" type="numeric" required="true" />
		<cfargument name="quantity" type="numeric" required="true" />
		<cfargument name="rateid" type="numeric" required="true" />	
		<cfargument name="rateChanged" type="numeric" required="false" />	

		<cfset var qryInsertCartItem = "" />
		
		<cfquery name="qryInsertCartItem" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			INSERT INTO dbo.store_cartItems (storeID, orderNumber, memberID, statSessionID, ProductItemID, FormatID, quantity, 
				dateEntered, rateid<cfif isDefined("arguments.rateChanged")>, rateChanged</cfif>)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#" />,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#" />,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#" />,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.statSessionID#" />,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#" />,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#" />,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.Quantity#" />,
				getdate(),
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#" />
				<cfif isDefined("arguments.rateChanged")>, <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.rateChanged#" /></cfif>
			);

			SELECT SCOPE_IDENTITY() AS cartItemID;
		</cfquery>

		<cfreturn qryInsertCartItem.cartItemID>
	</cffunction>

	<cffunction name="insertCartItemSaleDetails" access="public" output="false" returntype="void" hint="insert Cart Item in DB">
		<cfargument name="cartItemID" type="numeric" required="true">
		<cfargument name="orderNumber" type="string" required="true">
		<cfargument name="saleID" type="numeric" required="true">	
		<cfargument name="saleAmountChanged" type="boolean" required="true">

		<cfset var qryInsertCartItemSaleDetails = "" />
		
		<cfquery name="qryInsertCartItemSaleDetails" datasource="#application.dsn.memberCentral.dsn#">
			INSERT INTO dbo.store_cartItemSaleDetails(cartItemID, orderNumber, saleID, saleAmountChanged)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.cartItemID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.saleID#">,
				<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.saleAmountChanged#">
			)
		</cfquery>
	</cffunction>
	
	<cffunction name="getCartDataByID" access="public" output="false" returntype="query" hint="get Cart Contents">
		<cfargument name="cartItemID" type="numeric" required="true" />

		<cfset var local = structNew() />

		<cfquery  name="local.qryCartByID" datasource="#application.dsn.memberCentral.dsn#">
			SELECT 
				c.StatSessionID, 
				c.ProductItemID, 
				c.rateID, 
				c.formatID, 
				c.Quantity,
				(select min(rateOverride) 
				from dbo.store_ProductRatesOverride as spo 
				where spo.rateid = c.rateid
				and ((spo.startDate <= getDate() and spo.endDate >= getDate()) or spo.startDate is null or spo.endDate is null)
				) as ratePaid,					
				f.name as formatName, 	
				(select min(perItem)
				from dbo.store_ProductShipping as spp  
				where spp.rateid = c.rateid 
				) as perItem,					
				(select min(perShipment)
				from dbo.store_ProductShipping as spp  
				where spp.rateid = c.rateid 
				) as perShipment,
				(SELECT prodcontent.contentTitle
					FROM dbo.store_CartItems AS ci
					INNER JOIN dbo.store_Products AS p ON p.storeID = c.storeID AND ci.productItemID = p.ItemID
					inner join dbo.cms_contentLanguages as prodcontent on prodcontent.contentID = p.productContentID and prodcontent.languageID = 1
					WHERE ci.cartItemID = c.cartItemID
				) as contentTitle							
			FROM dbo.store_cartItems as c
			INNER JOIN dbo.store_ProductFormats as f on f.formatID = c.formatID
			where cartitemid = <cfqueryparam value="#arguments.cartItemID#" cfsqltype="cf_sql_integer" />			
		</cfquery>	

		<cfreturn local.qryCartByID />
	</cffunction>		

	<cffunction name="getCartItem" access="public" output="FALSE" returntype="query">
		<cfargument name="storeID" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="true" />
		<cfargument name="ProductItemID" type="numeric" required="yes">
		<cfargument name="formatID" type="numeric" required="yes">
		
		<cfset var qryCart = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryCart">
			SELECT ci.cartItemID, ci.quantity
			FROM dbo.store_cartItems as ci
			INNER JOIN dbo.store_cartItemSaleDetails as cisd on cisd.cartItemID = ci.cartItemID
			WHERE ci.storeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#">
			AND ci.OrderNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">
			AND ci.ProductItemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ProductItemID#">
			AND ci.formatID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#">
			AND cisd.saleID is null;
		</cfquery>

		<cfreturn qryCart>
	</cffunction>
	
	<cffunction name="getCartData" access="public" output="false" returntype="query">
		<cfargument name="storeid" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="true" />
		<cfargument name="shippingid" type="numeric" required="true" />

		<cfset var local = structNew() />

		<cfstoredproc procedure="store_getCartData" datasource="#application.dsn.memberCentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#" null="false" />
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.orderNumber#" null="false" />
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.shippingid#" null="false" />
			<cfprocresult name="local.qryCart" resultset="1" />
		</cfstoredproc>

		<cfreturn local.qryCart />
	</cffunction>

	<cffunction name="getCartItemSaleDetails" access="public" output="false" returntype="query">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="orderNumber" type="string" required="true">

		<cfset var qryCartItemSaleDetails = "">

		<cfquery name="qryCartItemSaleDetails" datasource="#application.dsn.memberCentral.dsn#">
			SELECT ci.CartItemID, cisd.saleID
			FROM dbo.store_CartItems AS ci
			INNER JOIN dbo.store_cartItemSaleDetails as cisd on cisd.cartItemID = ci.CartItemID
			WHERE ci.storeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#">
			AND ci.orderNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orderNumber#">
		</cfquery>

		<cfreturn qryCartItemSaleDetails>
	</cffunction>
	
	<cffunction name="removeCartItem" access="public" output="false" returntype="void">
		<cfargument name="cartItemID" type="numeric" required="true">

		<cfset var qryDelete = "">

		<cfquery  name="qryDelete" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @cartItemID int;
				SET @cartItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.cartItemID#">;
				
				BEGIN TRAN;
					DELETE cisd
					FROM dbo.store_cartItemSaleDetails AS cisd 
					INNER JOIN dbo.store_cartItems AS ci ON ci.cartItemID = cisd.cartItemID
					WHERE cisd.cartItemID = @cartItemID;

					DELETE FROM dbo.store_cartItems
					WHERE cartItemID = @cartItemID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>		
	</cffunction>
	
	<cffunction name="updateQuantity" access="public" output="false" returntype="void" hint="Update quantity">
		<cfargument name="cartItemID" type="numeric" />
		<cfargument name="quantity" type="numeric" />

		<cfset var qryUpdate = "" />
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryUpdate">
			UPDATE dbo.store_cartItems
			SET quantity = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.quantity#" />
			WHERE cartItemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.cartItemID#" />
		</cfquery>		
	</cffunction>	
	
	<cffunction name="deleteCartData" access="public" output="false" returntype="void">
		<cfargument name="orderNumber" type="string" required="true">

		<cfset var qryDelete = "">

		<cfquery  name="qryDelete" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orderNumber varchar(50);
				SET @orderNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orderNumber#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.store_cartItemSaleDetails WHERE orderNumber = @orderNumber;

					DELETE FROM dbo.store_cartItems WHERE orderNumber = @orderNumber;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>		
	</cffunction>
		
	<cffunction name="removeAffirmation" access="public" output="false" returntype="void" hint="Update Affirmation Status">
		<cfargument name="orderID" type="numeric" />
		<cfargument name="formatID" type="numeric" />

		<cfset var qryUpdate = "" />
		
		<cfquery name="qryUpdate" datasource="#application.dsn.memberCentral.dsn#">
			UPDATE aff
			SET aff.status = 'D'
			FROM dbo.crd_affirmations as aff
			inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = aff.affirmationTypeID and cat.affirmationType = 'paper'
			WHERE aff.orderID = <cfqueryparam value="#arguments.orderID#" cfsqltype="cf_sql_integer" />
			and aff.productFormatID = <cfqueryparam value="#arguments.formatID#" cfsqltype="cf_sql_integer" />
		</cfquery>		
	</cffunction>	
	
	<cffunction name="getShippingInfo" access="public" output="false" returntype="query" hint="get Cart Contents">
		<cfargument name="orderID" type="numeric" required="true" />
		<cfargument name="rateIDList" type="string" required="true" />

		<cfset var local = structNew() />
		
		<cfif not listLen(arguments.rateIDList)>
			<cfset arguments.rateIDList = 0 />
		</cfif>

		<cfquery name="local.qryGetShippingInfo" datasource="#application.dsn.membercentral.dsn#">
		select 
			ps.shippingid,
			sm.shippingName,
			isNull(max(ps.PerItem)*max(od.quantity),convert(decimal(18,2),0.00)) as PerItem,
			isNull(max(ps.PerShipment),convert(decimal(18,2),0.00)) as PerShipment,
			isNull(max(ps.PerItem)*max(od.quantity),convert(decimal(18,2),0.00)) +
			isNull(max(ps.PerShipment),convert(decimal(18,2),0.00)) as totalShipmentAmt				
		from dbo.store_ShippingMethods sm
		inner join dbo.store_ProductShipping ps on ps.shippingid = sm.shippingid
			and ps.rateid in (<cfqueryparam value="#arguments.rateIDList#" cfsqltype="cf_sql_integer" list="true" />)
		inner join store_orderdetails od on ps.rateid = od.rateid
			and od.orderid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#" />					
		group by ps.shippingid, sm.shippingName	
		</cfquery>

		<cfreturn local.qryGetShippingInfo />
	</cffunction>	
	
	<cffunction name="getCartMerchantProfiles" access="private" output="false" returntype="query">
		<cfargument name="storeid" type="numeric" required="true" />

		<cfset var qryProfiles = "" />

		<cfquery name="qryProfiles" datasource="#application.dsn.memberCentral.dsn#">
			SELECT mp.profileID, mp.profileCode, g.gatewayID, g.gatewayClass, s.emailRecipient
			FROM dbo.store_merchantProfiles as smp
			INNER JOIN dbo.mp_profiles as mp on mp.profileID = smp.merchantProfileID
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
			INNER JOIN dbo.store as s on s.storeID = smp.storeID
			WHERE smp.storeID = <cfqueryparam value="#arguments.storeid#" cfsqltype="cf_sql_integer" />
			AND mp.status = 'A'
			AND g.isActive = 1
			and mp.allowPayments = 1
			ORDER BY mp.frontEndOrderBy
		</cfquery>
		
		<cfreturn qryProfiles />
	</cffunction>	
	
	<cffunction name="getOrderTotals" access="public" output="false" returntype="query">
		<cfargument name="orderNumber" type="string" />

		<cfset var qryOrder = "" />

		<cfquery name="qryOrder" datasource="#application.dsn.memberCentral.dsn#">
			SELECT so.totalProduct, totalShipping, totalTax, shippingKey, ssm.ShippingName as shippingMethod
			FROM dbo.store_orders so
			LEFT OUTER JOIN store_ShippingMethods ssm on ('PID' + convert(varchar,ssm.shippingID)) = so.shippingKey
			WHERE orderNumber = <cfqueryparam  value="#arguments.orderNumber#" cfsqltype="cf_sql_varchar" />
		</cfquery>

		<cfreturn qryOrder />
	</cffunction>	
	
	<cffunction name="totalCartItems" access="public" output="FALSE" returntype="struct">
		<cfargument name="storeID" type="numeric" required="true" />
		<cfargument name="qryCart" type="query" required="true" />

		<cfset var local = structNew() />
		<cfset local.totalRate = 0 />
		<cfset local.totalShipping = 0 />
		<cfset local.shippingOptions = structNew() />

		<cfset local.rateIds = valuelist(arguments.qryCart.rateid) />
		<cfif len(local.rateIds) eq 0>
			<cfset local.rateIds = 0 />
		</cfif>

		<cfquery name="local.qryShippingMethods" datasource="#application.dsn.memberCentral.dsn#">
			select *
			from dbo.store_ShippingMethods sm
			where sm.storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="cf_sql_integer" />  
			and sm.Visible = 1
		</cfquery>

		<cfloop query="local.qryShippingMethods">		
			<cfif not isDefined("local.shippingOptions.PID#local.qryShippingMethods.ShippingID#")>
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"] = structNew() />
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].name = local.qryShippingMethods.ShippingName>
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].PerShipment = 0 />
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].totalItems = 0 />
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].shippingGLAccountID = 0 />
			</cfif>
		</cfloop>

		<cfloop query="arguments.qryCart">
			
			<cfif not arguments.qryCart.saleAmountChanged or arguments.qryCart.rateChanged or arguments.qryCart.itemDiscountExcTax gt 0>
				<cfif arguments.qryCart.rateOverride eq "">
					<cfset local.totalRate = local.totalRate + (val(arguments.qryCart.rate) * arguments.qryCart.quantity) />
				<cfelse>
					<cfset local.totalRate = local.totalRate + (val(arguments.qryCart.rateOverride) * arguments.qryCart.quantity) />
				</cfif>	
			<cfelse>
				<cfset local.totalRate = local.totalRate + (val(arguments.qryCart.cache_amountAfterAdjustment) * arguments.qryCart.quantity) />
			</cfif>		
			
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryCurrShip">
				SET NOCOUNT ON;

				DECLARE @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">;

				select rateid, sm.ShippingID, sm.ShippingName, 
					isNull(sps.PerShipment,convert(decimal(18,2),0.00)) as PerShipment,
					isNull(sps.PerItem,convert(decimal(18,2),0.00)) as PerItem,
					gl.rateGLAccountID, gl.shippingGLAccountID
				from dbo.store_ShippingMethods sm
				left outer join dbo.store_ProductShipping sps on sps.shippingID = sm.shippingID 
					and sps.rateid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.qryCart.rateid#">
				CROSS APPLY dbo.fn_store_getRateGLAccountID(@storeID, rateid) as gl
				where sm.storeID = @storeID
				and sm.Visible = 1
				order by sm.ShippingID;
			</cfquery>		
			
			<cfloop query="local.qryCurrShip">
				<cfif local.qryCurrShip.PerShipment GT local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].PerShipment>
					<cfset local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].PerShipment = local.qryCurrShip.PerShipment />
					<cfset local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].shippingGLAccountID = local.qryCurrShip.shippingGLAccountID />
				</cfif>				
				<cfset local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].totalItems = local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].totalItems + (local.qryCurrShip.PerItem * arguments.qryCart.quantity) />
			</cfloop>
			
		</cfloop>
		
		<cfreturn local />
	</cffunction>	
	
	<cffunction name="calculateCartTaxes" access="public" output="false" returntype="struct">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="orderNumber" type="string" required="true">
		<cfargument name="shippingID" type="numeric" required="true">
		<cfargument name="stateIDForTax" type="numeric" required="true">
		<cfargument name="zipForTax" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.taxableTotal = 0>
		<cfset local.strTaxes = { totaltax=0 }>
		
		<cfquery name="local.qryCart" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @storeID int = <cfqueryparam value="#arguments.storeID#" cfsqltype="cf_sql_integer">;

			SELECT ci.cartItemID, ci.quantity, gl.rateGLAccountID, sr.rate, gl.shippingGLAccountID, 
				(isNULL(sr.rate,0) * ci.quantity) as ItemTotal,
				(isNULL(ps.perItem,0) * ci.quantity) as itemShipping, 
				(isNULL(ps.perItem,0)) as perItem,
				(isNULL(ps.perShipment,0)) as perShipment,
				(select top 1 spo.rateOverride * ci.quantity
				 from dbo.store_ProductRatesOverride as spo 
				 where spo.rateid = sr.rateid 
				 and (
				 	(spo.startDate <= getDate() and spo.endDate >= getDate()) 
				 	 or spo.startDate is null 
				 	 or spo.endDate is null
				 	 )
				 order by spo.rateOverride) as ItemTotalOverride,
				(select top 1 spo.rateOverride
				 from dbo.store_ProductRatesOverride as spo 
				 where spo.rateid = sr.rateid 
				 and (
				 	(spo.startDate <= getDate() and spo.endDate >= getDate())
				 	or spo.startDate is null 
				 	or spo.endDate is null 
				 	)
				 order by spo.rateOverride) as rateOverride
			FROM dbo.store_CartItems AS ci
			INNER JOIN dbo.store_rates AS sr ON ci.rateid = sr.rateid 
			CROSS APPLY dbo.fn_store_getRateGLAccountID(@storeID, sr.rateid) as gl
			LEFT OUTER JOIN dbo.store_productShipping ps ON ps.rateid = sr.rateid
				and ps.shippingid = <cfqueryparam value="#arguments.shippingid#" cfsqltype="cf_sql_integer">
			WHERE ci.storeID = @storeID
			AND ci.orderNumber = <cfqueryparam value="#arguments.orderNumber#" cfsqltype="cf_sql_varchar">;
		</cfquery>		
		
		<cfloop query="local.qryCart">
			<cfif local.qryCart.quantity gt 1>

				<!--- get the tax for each item --->
				<cfif NOT len(trim(local.qryCart.ItemTotalOverride))>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
													saleAmount=local.qryCart.rate, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
													zipForTax=arguments.zipForTax)>	
				<cfelse>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
													saleAmount=local.qryCart.rateOverride, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
													zipForTax=arguments.zipForTax)>
				</cfif>
				<cfif val(local.strTaxIndiv.totalTaxAmt)>
					<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt*local.qryCart.quantity,"0.00")>
				</cfif>	
				<cfset local.strTaxes[local.qryCart.cartItemID] = val(local.strTaxIndiv.totalTaxAmt)>

				<!--- get the tax on item shipping --->
				<cfif local.qryCart.perItem>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.shippingGLAccountID,
													saleAmount=local.qryCart.perItem, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
													zipForTax=arguments.zipForTax)>					
					<cfif val(local.strTaxIndiv.totalTaxAmt)>
						<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt*local.qryCart.quantity,"0.00")>
					</cfif>				
					<cfset local.strTaxes["#local.qryCart.cartItemID#_ship"] = val(local.strTaxIndiv.totalTaxAmt)>
				</cfif>						

			<cfelse>

				<!--- get the tax for each item --->
				<cfif NOT len(trim(local.qryCart.ItemTotalOverride))>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
														saleAmount=local.qryCart.ItemTotal, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
				<cfelse>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
														saleAmount=local.qryCart.ItemTotalOverride, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
				</cfif>
				<cfif val(local.strTaxIndiv.totalTaxAmt)>
					<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt,"0.00")>
				</cfif>	
				<cfset local.strTaxes[local.qryCart.cartItemID] = val(local.strTaxIndiv.totalTaxAmt)>
				
				<!--- get the tax on item shipping --->
				<cfif local.qryCart.ItemShipping>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.shippingGLAccountID,
														saleAmount=local.qryCart.ItemShipping, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
					<cfif val(local.strTaxIndiv.totalTaxAmt)>
						<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt,"0.00")>
					</cfif>				
					<cfset local.strTaxes["#local.qryCart.cartItemID#_ship"] = val(local.strTaxIndiv.totalTaxAmt)>
				</cfif>				

			</cfif>										
		</cfloop>

		<!--- get the tax on pershipment shipping --->
		<cfquery name="local.qryShipment" dbtype="query" maxrows="1">
			select shippingGLAccountID, PerShipment
			from [local].qryCart
			order by PerShipment DESC					
		</cfquery>																		
		<cfif local.qryShipment.recordCount and val(local.qryShipment.PerShipment)>
			<cfset local.strShipmentTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryShipment.shippingGLAccountID,
														saleAmount=local.qryShipment.PerShipment, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
			<cfif val(local.strShipmentTaxIndiv.totalTaxAmt)>
				<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strShipmentTaxIndiv.totalTaxAmt,"0.00")>
			</cfif>	
			<cfset local.strTaxes["pershipment"] = val(local.strShipmentTaxIndiv.totalTaxAmt)>
		</cfif>

		<cfset local.strTaxes["totaltax"] = local.taxableTotal>
		
		<cfreturn local.strTaxes>
	</cffunction>
	
	<cffunction name="getRateOptions" access="public" output="false" returntype="struct" hint="get Rate options">
		<cfargument name="storeID" type="numeric" required="true" />
		<cfargument name="formatID" type="numeric" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="StoreProductRate", functionName="Qualify")>

		<cfstoredproc procedure="store_getRateOptionsForAdminByFormatID" datasource="#application.dsn.memberCentral.dsn#">
			<cfprocparam type="In" value="#arguments.storeID#" cfsqltype="cf_sql_integer">
			<cfprocparam type="In" value="#arguments.formatID#" cfsqltype="cf_sql_integer">
			<cfprocparam type="In" value="#arguments.memberID#" cfsqltype="cf_sql_integer">
			<cfprocparam type="In" value="#local.QualifyRFID#" cfsqltype="cf_sql_integer">
			<cfprocresult name="local.returnStruct.qryRateActiveEligible" resultset="1">
			<cfprocresult name="local.returnStruct.qryRateInactiveEligible" resultset="2">
			<cfprocresult name="local.returnStruct.qryRateNonEligible" resultset="3">
		</cfstoredproc>

		<cfreturn local.returnStruct>
	</cffunction>	
	
	<cffunction name="updateRate" access="public" output="false" returntype="void" hint="Update rate">
		<cfargument name="cartItemID" type="numeric" />
		<cfargument name="rateID" type="numeric" />

		<cfset var qryUpdate = "" />
		
		<cfquery name="qryUpdate" datasource="#application.dsn.memberCentral.dsn#">
			UPDATE dbo.store_cartItems
			SET rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer" />,
				rateChanged = 1
			WHERE cartItemID = <cfqueryparam value="#arguments.cartItemID#" cfsqltype="cf_sql_integer" />
		</cfquery>		
	</cffunction>	
	
	<cffunction name="buynow_buy" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.objStore = CreateObject('component', 'model.admin.store.store')>
		<cfset local.strResponse = { success=false, response='' }>
		
		<!--- get Store Info --->			
		<cfset local.storeInfo = local.objStore.getStoreInfo(arguments.event)>

		<cfset local.storeID = arguments.event.getValue('storeid')>
		<cfset local.orderNumber = arguments.event.getValue('ordernumber')>
		<cfset local.shippingOption = arguments.event.getValue('shippingOption')>

		<!--- update member id for ordernumber --->
		<cfset local.usemid = arguments.event.getValue('memberID')>
		<cfset local.shippingKey = "PID" & local.shippingOption>

		<cfset local.qryOrder = local.objStore.getOrder(storeID=local.storeID, orderID=arguments.event.getValue('orderID'))>
		<cfset local.strOrderDetails = local.objStore.getOrderDetails(storeID=local.storeID, orderID=arguments.event.getValue('orderID'))>
			
		<cfset local.shippingInfoXML = XMLParse(local.qryOrder.xmlShippingInfo)>
		<cfset local.stateIDForTax = int(val(trim(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)))>
		<cfset local.zipForTax = trim(local.shippingInfoXML.xmlRoot.fldship_zip.xmlText)>

		<!--- get cart data --->
		<cfset local.qryCart = getCartData(storeid=local.storeID, ordernumber=local.orderNumber, shippingid=local.shippingOption)>
		<cfset local.qryCartItemSaleDetails = getCartItemSaleDetails(storeid=local.storeID, ordernumber=local.orderNumber)>
		<cfset local.arrTransactions = arrayNew(1)>
		
		<cfset local.qryPaymentGateways = getCartMerchantProfiles(storeid=local.storeID)>
		<cfset local.qryItemsTotal = totalCartItems(local.storeID,local.qryCart)>
		
		<cfset local.shippingOptionsTotal = 0.00>
		<cfif local.qryItemsTotal.totalRate>
			<cfset local.shippingOptionsTotal = local.qryItemsTotal.shippingOptions["PID" & arguments.event.getValue("shippingOption")].PerShipment + local.qryItemsTotal.shippingOptions["PID" & arguments.event.getValue("shippingOption")].totalItems>
		</cfif>			

		<!--- calculate taxes as if the order was all brand new. we'll only use the entries we need to --->
		<cfset local.strStoreTax = calculateCartTaxes(storeID=local.storeID, orderNumber=local.orderNumber, shippingID=arguments.event.getValue("shippingOption"), stateidForTax=local.stateIDForTax, zipForTax=local.zipForTax)>

		<!--- determine payment profileID and profileCode --->
		<cfset arguments.event.paramValue('profileid',local.qryPaymentGateways.profileID)>
		
		<cfquery name="local.qryMerchantProfile" dbtype="query">
			select profileID, profileCode, gatewayid, emailRecipient
			from [local].qryPaymentGateways
			where profileid = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="cf_sql_integer" />
		</cfquery>
		
		<cfquery name="local.qryGetThisOrderShippingTotals" dbtype="query">
			select sum(amountAfterAdjustment) as oldShippingTotals
			from [local].strOrderDetails.qryOrderShippingSaleDetails
		</cfquery>
		
		<cfset local.shippingMethodChanged = 0>
		<cfif len(local.qryOrder.shippingKey) and ((local.qryOrder.shippingKey NEQ "PID0" and local.qryOrder.shippingKey NEQ local.shippingKey)
			OR (local.qryGetThisOrderShippingTotals.oldShippingTotals NEQ local.shippingOptionsTotal))>
			<cfset local.shippingMethodChanged = 1>
		</cfif>

		<!--- Add discount information to cart query if coupon is applied --->
		<cfset local.couponID = int(val(arguments.event.getValue('couponID',0)))>
		<cfset local.qualifiedRateIDList = arguments.event.getValue('qualifiedRateIDList','')>
		<cfif local.couponID gt 0 and len(local.qualifiedRateIDList)>
			<cfset local.strOrderDiscount = local.objStore.getStoreItemsDiscount(siteID=arguments.event.getValue('mc_siteinfo.siteID'), couponID=local.couponID, 
						qualifiedRateIDList=local.qualifiedRateIDList, storeID=local.storeID, orderNumber=local.orderNumber, shippingID=local.shippingOption)>
			<cfset local.qryDiscountItems = local.strOrderDiscount.qryDiscountItems>
		<cfelse>
			<cfset local.qryDiscountItems = QueryNew("")>
		</cfif>
		<cfset local.objStore.addDiscountToOrderQuery(qryOrder=local.qryCart, qryDiscountItems=local.qryDiscountItems)>

		<cfset local.hasRemovedCoupon = false>
		<cfif local.strOrderDetails.qryOrderCouponDiscounts.couponID gt 0>
			<cfset local.qryStoreOrderCouponAppliedItems = local.objStore.getStoreOrderCouponAppliedItems(orgID=arguments.event.getValue('mc_siteinfo.orgID'), orderID=arguments.event.getValue('orderid'))>

			<cfif local.couponID eq 0>
				<cfset local.hasRemovedCoupon = true>
			</cfif>
		</cfif>
		
		<!--- Identify deleted items --->
		<cfset local.deletedItemList = "">
		<cfloop query="local.strOrderDetails.qryOrderProductSaleDetails">
			<cfif not listFind(valueList(local.qryCartItemSaleDetails.saleID),local.strOrderDetails.qryOrderProductSaleDetails.saleID)>
				<cfset local.deletedItemList = listAppend(local.deletedItemList,local.strOrderDetails.qryOrderProductSaleDetails.saleID)>
			</cfif>
		</cfloop>

		<cftry>
			<cfquery name="local.qryUpdateOrder" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @orderID int, @orgID int, @siteID int, @recordedByMemberID int, @assignedToMemberID int, @statsSessionID int, 
						@storeAppTypeID int, @nowDate datetime, @invoiceProfileID int, @invoiceID int, @invoiceNumber varchar(18), @transactionID int, 
						@newOrderDetailID int, @rsaTransactionID int, @rsaOldRatePaidNeg decimal(18,2), @rsaOldRatePaid decimal(18,2), @GlAccountID int,
						@rateTransactionID int, @saleID int, @shippingName varchar(200), @detail varchar(500), @invoiceIDList varchar(max), @stateIDForTax int, 
						@zipForTax varchar(25), @couponID int, @discountAmount decimal(18,2), @discountAdjTransactionID int, @maxOverallUsageCount int, 
						@maxMemberUsageCount int, @redemptionCount int, @redemptionCountPerMember int, @couponCode varchar(15), @trashID int, 
						@newRateTransactionID int, @tr_AdjustTrans int, @couponTransactionID int, @SPRedemptionCount int, @productItemID int,
						@tids xml, @vidPool xml, @storeID int;
					declare @tblInvoices TABLE (invoiceID int, invoiceProfileID int, amount decimal(18,2));
					set @orderID = <cfqueryparam value="#arguments.event.getValue('orderid')#" cfsqltype="cf_sql_integer">;
					set @storeID = <cfqueryparam value="#local.storeID#" cfsqltype="cf_sql_integer">;
					set @orgID = #arguments.event.getValue('mc_siteinfo.orgID')#;
					set @siteID = #arguments.event.getValue('mc_siteinfo.siteID')#;
					set @recordedByMemberID = #session.cfcuser.memberdata.memberid#;
					set @assignedToMemberID = #local.useMID#;
					set @statsSessionID = #session.cfcuser.statsSessionID#;
					select @storeAppTypeID = dbo.fn_getApplicationTypeIDFromName('Store');
					set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
					set @stateIDForTax=<cfif val(local.stateIDForTax)>#local.stateIDForTax#<cfelse>null</cfif>;
					set @zipForTax=<cfif len(local.zipForTax)>'#local.zipForTax#'<cfelse>null</cfif>;
					set @couponID = #int(val(local.couponID))#;
					select @nowDate = getdate();

					BEGIN TRAN;
						<!--- Update shipping --->					
						<cfif not len(local.qryOrder.shippingKey) OR local.qryOrder.shippingKey is "PID0" OR local.shippingMethodChanged>
							UPDATE dbo.store_orders
							SET shippingKey = <cfqueryparam value="PID#local.shippingOption#" cfsqltype="cf_sql_varchar">
							WHERE orderID = @orderID;
						</cfif>

						<!--- put all open invoices used for this order into table since they were already created and can be used for adjustments --->
						insert into @tblInvoices (invoiceID, invoiceProfileID, amount)
						select distinct i.invoiceID, i.invoiceProfileID, null as amount
						from dbo.fn_store_orderTransactions(@orgID,@orderID) as ot
						inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = ot.transactionID
						inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
						where i.statusID = 1;

						<!--- voiding coupon discount adjustment on coupon removal --->
						<cfif local.hasRemovedCoupon>
							select @couponTransactionID = min(d.transactionID)
							from dbo.tr_transactionDiscounts as d
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = d.transactionID and t.statusID = 1
							inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_AdjustTrans and tr.transactionID = d.transactionID
							where d.orgID = @orgID
							and d.isActive = 1
							and d.itemType = 'StoreOrder'
							and d.itemID = @orderID;

							WHILE @couponTransactionID IS NOT NULL BEGIN
								EXEC dbo.tr_voidTransaction @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
									@transactionID=@couponTransactionID, @checkInBounds=1, @vidPool=@vidPool OUTPUT, @tids=@tids OUTPUT;		

								-- mark the discount row as not active so it doesnt count against redemption
								UPDATE dbo.tr_transactionDiscounts
								set isActive = 0
								where transactionID = @couponTransactionID
								and isActive = 1;

								select @couponTransactionID = min(d.transactionID)
								from dbo.tr_transactionDiscounts d
								inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = d.transactionID and t.statusID = 1
								inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_AdjustTrans and tr.transactionID = d.transactionID
								where d.orgID = @orgID
								and d.isActive = 1
								and d.itemType = 'StoreOrder'
								and d.itemID = @orderID
								and d.transactionID > @couponTransactionID;
							END
						</cfif>

						<!--- Item sales --->
						<cfloop query="local.qryCart">
							<cfif len(local.qryCart.rateOverride)>
								<cfset local.thisRateAmount = local.qryCart.rateOverride>
							<cfelse>
								<cfset local.thisRateAmount = local.qryCart.rate>
							</cfif>

							<cfset local.couponAppliedItem = false>
							<cfif local.qryCart.itemDiscountExcTax gt 0>
								<cfset local.couponAppliedItem = true>
								<cfset local.thisItemDiscount = local.qryCart.itemDiscountExcTax>
								<cfset local.thisQtyDiscountAmt = 0>
							</cfif>

							<!--- if this is a new item --->
							<cfif not val(local.qryCart.hasSaleAssociated)>
								
								<cfloop from="1" to="#local.qryCart.quantity#" index="local.i">
									select @invoiceProfileID=null, @invoiceID=null, @detail=null, @discountAmount = null;
									select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.rateGLAccountID#);
									select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
									IF @invoiceID is null begin
										EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
											@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
											@invoiceNumber=@invoiceNumber OUTPUT;
										INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
										VALUES (@invoiceID, @invoiceProfileID, null);
									end

									set @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle#';

									EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
										@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
										@detail=@detail, @parentTransactionID=null, @amount=#local.thisRateAmount#, @transactionDate=@nowDate, 
										@creditGLAccountID=#local.qryCart.rateGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax,  
										@taxAmount=<cfif structKeyExists(local.strStoreTax,local.qryCart.cartItemID)>#local.strStoreTax[local.qryCart.cartItemID]#<cfelse>null</cfif>, 
										@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@rateTransactionID OUTPUT;

									INSERT INTO dbo.store_orderDetails (orderID, StatSessionID, ProductItemID, rateid, formatID, Quantity, ratePaid, 
										formatName, perItem, perShipment, contentTitle)
									select @orderID, c.StatSessionID, c.ProductItemID, c.rateID, c.formatID, 1, 
										nullIf((select min(rateOverride) from dbo.store_ProductRatesOverride as spo where spo.rateid = c.rateid and (spo.startDate <= getDate() and spo.endDate >= getDate()) or spo.startDate is null or spo.endDate is null),0) as ratePaid,
										f.name, isnull((select min(perItem) from dbo.store_ProductShipping as spp where spp.rateid = c.rateid),0) as perItem,
										isnull((select min(perShipment) from dbo.store_ProductShipping as spp where spp.rateid = c.rateid),0) as perShipment,
										cl.contentTitle
									FROM dbo.store_cartItems as c
									INNER JOIN dbo.store_ProductFormats as f on f.formatID = c.formatID
									INNER JOIN dbo.store_Products AS p ON p.storeID = c.storeID and c.productItemID = p.ItemID
									INNER JOIN dbo.cms_contentlanguages as cl on cl.contentID = p.productContentID and cl.languageID = 1
									where cartitemid = #local.qryCart.cartItemID#;
										select @newOrderDetailID = scope_identity();

									EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@rateTransactionID, 
										@itemType='Product', @itemID=@newOrderDetailID, @subItemID=null;

									<cfif local.couponAppliedItem and local.thisItemDiscount gt 0>
										<cfset local.thisQtyDiscountAmt = min(val(local.thisRateAmount),local.thisItemDiscount)>

										set @discountAmount = #local.thisQtyDiscountAmt# * - 1;
										set @productItemID = #int(val(local.qryCart.ItemID))#;

										-- promo code : check if coupon max usage count met
										select @couponCode = null, @maxOverallUsageCount = null, @maxMemberUsageCount = null, @redemptionCount = null, 
											@redemptionCountPerMember = null, @SPRedemptionCount = null;
										
										select @couponCode = couponCode, @maxOverallUsageCount = maxOverallUsageCount, @maxMemberUsageCount = maxMemberUsageCount,
											@SPRedemptionCount = storeXML.value('(/store/p/i[text()=sql:variable("@productItemID")]/@rc)[1]', 'int')
										from dbo.tr_coupons
										where siteID = @siteID
										and couponID = @couponID;

										set @SPRedemptionCount = isnull(@SPRedemptionCount,1);

										;with redemptionsCheck as (
											select distinct td.itemType, td.itemID, td.redemptionCount, 
												case when m.memberID is not null then td.redemptionCount else 0 end as memberRedemptionCount
											from dbo.tr_transactionDiscounts as td
											inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
											left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID and m.activeMemberID = @assignedToMemberID
											where td.couponID = @couponID
											and not (td.itemID = @orderID and itemType = 'StoreOrder')
										)
										select @redemptionCount = SUM(redemptionCount), @redemptionCountPerMember = SUM(memberRedemptionCount) 
										from redemptionsCheck;

										IF @maxOverallUsageCount > 0 AND ISNULL(@redemptionCount,0) + @SPRedemptionCount > @maxOverallUsageCount
											UPDATE dbo.store_orders
											SET orderNotes = isnull(orderNotes,'') + ' - Overall Redemptions Count for Coupon Code(' + @couponCode + ') exceeded; '
											WHERE orderID = @orderID;

										IF @maxMemberUsageCount > 0 AND ISNULL(@redemptionCountPerMember,0) + @SPRedemptionCount > @maxMemberUsageCount
											UPDATE dbo.store_orders
											SET orderNotes = isnull(orderNotes,'') + ' - Overall Redemptions Count Per Member for Coupon Code(' + @couponCode + ') exceeded; '
											WHERE orderID = @orderID;

										EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
											@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@nowDate, 
											@saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @couponID=@couponID, 
											@itemType='StoreOrder', @itemID=@orderID, @redemptionCount=@SPRedemptionCount, 
											@transactionID=@discountAdjTransactionID OUTPUT;

										<cfset local.thisItemDiscount = local.thisItemDiscount - local.thisQtyDiscountAmt>
									</cfif>

									<!--- Per item shipping sales --->
									<cfif local.qryCart.perItem>
										select @invoiceProfileID=null, @invoiceID=null, @detail=null;
										select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.shippingGLAccountID#);
										
										select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
										IF @invoiceID is null BEGIN
											EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
												@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
												@invoiceNumber=@invoiceNumber OUTPUT;
											INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
											VALUES (@invoiceID, @invoiceProfileID, null);
										END

										SET @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle# - #local.qryCart.shippingName# shipping';

										EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
											@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
											@detail=@detail, @parentTransactionID=null, @amount=#local.qryCart.perItem#, @transactionDate=@nowDate, 
											@creditGLAccountID=#local.qryCart.shippingGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax,  
											@taxAmount=<cfif structKeyExists(local.strStoreTax,"#local.qryCart.cartItemID#_ship")>#local.strStoreTax["#local.qryCart.cartItemID#_ship"]#<cfelse>null</cfif>, 
											@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;

										EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@transactionID, 
											@itemType='ProductPerItem', @itemID=@newOrderDetailID, @subItemID=null;
									</cfif>														
								</cfloop>
							<cfelse>

								<cfquery name="local.qryThisCartItemSaleDetails" dbtype="query">
									select saleID
									from [local].qryCartItemSaleDetails
									where cartItemID = #local.qryCart.cartItemID#
								</cfquery>

								<!--- using distinct here to avoid duplication due to split/reclass of sales --->
								<cfquery name="local.qryThisProductItemSaleDetails" dbtype="query">
									select distinct orderDetailID, rateID
									from [local].strOrderDetails.qryOrderProductSaleDetails
									where saleID in (0#valueList(local.qryThisCartItemSaleDetails.saleID)#)
								</cfquery>
								
								<cfloop query="local.qryThisProductItemSaleDetails">
									<!--- check if discount already applied --->
									<cfif local.strOrderDetails.qryOrderCouponDiscounts.couponID gt 0 and local.couponAppliedItem and local.thisItemDiscount gt 0>
										<cfquery name="local.qryThisItemDiscount" dbtype="query">
											select sum(amount) as itemDiscount
											from [local].qryStoreOrderCouponAppliedItems
											where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryThisProductItemSaleDetails.orderDetailID#">
										</cfquery>

										<cfif val(local.qryThisItemDiscount.itemDiscount) gt 0>
											<cfset local.couponAppliedItem = false>
											<cfset local.thisItemDiscount = 0>
										</cfif>
									</cfif>

									<!--- Identify items in which rateid changed or coupon removed --->
									<cfif local.qryThisProductItemSaleDetails.rateID neq local.qryCart.rateID or local.hasRemovedCoupon>
										set @saleID = null;
										
										select @saleID = min(ts.saleID)
										from dbo.tr_applications as tra
										inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
										where tra.orgID = @orgID
										and tra.itemID = #local.qryThisProductItemSaleDetails.orderDetailID#
										and tra.itemType = 'Product'
										and tra.status = 'A';
										
										WHILE @saleID IS NOT NULL BEGIN
											select @rsaTransactionID = null, @rsaOldRatePaidNeg = null, @rsaOldRatePaid = null;
											
											select @rsaTransactionID = fsot.transactionID, @rsaOldRatePaidNeg = ts.cache_amountAfterAdjustment * -1, 
												@rsaOldRatePaid = ts.cache_amountAfterAdjustment
											from dbo.fn_store_orderTransactions(@orgID,@orderID) as fsot
											inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fsot.transactionid 
											inner join dbo.tr_invoiceTransactions as tit on tit.orgID = @orgID and tit.transactionID = fsot.transactionid 
											where ts.saleID = @saleID;

											<!--- Adjust old rate sale to 0 --->
											IF isnull(@rsaOldRatePaid,0) > 0 BEGIN
												set @GlAccountID = null;
												select @GlAccountID = gl.GLAccountID
												from dbo.tr_glAccounts as gl
												inner join dbo.tr_transactions as t 
													on t.ownedByOrgID = @orgID
													and t.creditGLAccountID = gl.GLAccountID
													and t.transactionID = @rsaTransactionID;

												select @invoiceProfileID=null, @invoiceID=null;
												select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(@GlAccountID);
												select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
												IF @invoiceID is null begin
													EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
														@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
														@invoiceNumber=@invoiceNumber OUTPUT;
													INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
													VALUES (@invoiceID, @invoiceProfileID, null);
												end

												EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
													@statsSessionID=@statsSessionID, @amount=@rsaOldRatePaidNeg, @taxAmount=null, @transactionDate=@nowDate, 
													@autoAdjustTransactionDate=1, @saleTransactionID=@rsaTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
													@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
											END

											-- mark any discount rows as not active against this adjusted sale
											UPDATE td
											set td.isActive = 0
											from dbo.tr_transactionDiscounts as td
											inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.transactionID = td.transactionID
												and tr.typeID = @tr_AdjustTrans
												and tr.appliedToTransactionID = @rsaTransactionID
											where td.orgID = @orgID
											and td.isActive = 1;

											UPDATE dbo.tr_applications
											SET [status] = 'D'
											WHERE transactionID = @rsaTransactionID;
										
											select @saleID = min(ts.saleID)
											from dbo.tr_applications as tra
											inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
											where tra.orgID = @orgID
											and tra.itemID = #local.qryThisProductItemSaleDetails.orderDetailID#
											and tra.itemType = 'Product'
											and tra.status = 'A'
											and ts.saleID > @saleID;
										END

										<!--- new rate transaction --->
										select @rateTransactionID = null, @newRateTransactionID = null, @invoiceProfileID=null, @invoiceID=null, @detail=null;
										
										select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.rateGLAccountID#);
										select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

										IF @invoiceID is null BEGIN
											EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
												@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
												@invoiceNumber=@invoiceNumber OUTPUT;
											
											INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
											VALUES (@invoiceID, @invoiceProfileID, null);
										END

										set @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle#';

										EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
											@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
											@detail=@detail, @parentTransactionID=null, @amount=#local.thisRateAmount#, @transactionDate=@nowDate, 
											@creditGLAccountID=#local.qryCart.rateGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
											@taxAmount=<cfif structKeyExists(local.strStoreTax,local.qryCart.cartItemID)>#local.strStoreTax[local.qryCart.cartItemID]#<cfelse>null</cfif>, 
											@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@newRateTransactionID OUTPUT;

										EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@newRateTransactionID, @itemType='Product', @itemID=#local.qryThisProductItemSaleDetails.orderDetailID#, @subItemID=null;

										set @rateTransactionID = @newRateTransactionID;

										-- qryTransactionSaleData
										set @saleID = null;
										select @saleID = min(ts.saleID)
										from dbo.tr_applications as tra
										inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
										where tra.orgID = @orgID
										and tra.itemID = #local.qryThisProductItemSaleDetails.orderDetailID#
										and tra.itemType = 'ProductPerItem'
										and tra.status = 'A';

										<!--- Perform per item shipping sale adjustment --->	
										IF @saleID is not null BEGIN

											WHILE @saleID is not null BEGIN
												-- qryAdjust
												select @rsaTransactionID = null, @rsaOldRatePaidNeg = null, @rsaOldRatePaid = null;
												select @rsaTransactionID = fsot.transactionID, @rsaOldRatePaidNeg = ts.cache_amountAfterAdjustment * -1, 
													@rsaOldRatePaid = ts.cache_amountAfterAdjustment
												from dbo.fn_store_orderTransactions(@orgID,@orderID) as fsot
												inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fsot.transactionid 
												inner join dbo.tr_invoiceTransactions as tit on tit.orgID = @orgID and tit.transactionID = fsot.transactionid 
												where ts.saleID = @saleID;

												<!--- Adjust transaction --->
												IF isnull(@rsaOldRatePaid,0) > 0 BEGIN
													set @GlAccountID = null;
													select @GlAccountID = gl.GLAccountID
													from dbo.tr_glAccounts as gl
													inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.GLAccountID
													where t.transactionID = @rsaTransactionID;

													select @invoiceProfileID=null, @invoiceID=null;
													select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(@GlAccountID);
													select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
													IF @invoiceID is null BEGIN
														EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
															@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
															@invoiceNumber=@invoiceNumber OUTPUT;
														INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
														VALUES (@invoiceID, @invoiceProfileID, null);
													END

													EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
														@statsSessionID=@statsSessionID, @amount=@rsaOldRatePaidNeg, @taxAmount=null, @transactionDate=@nowDate, 
														@autoAdjustTransactionDate=1, @saleTransactionID=@rsaTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
														@byPassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;
												END

												UPDATE dbo.tr_applications
												SET [status] = 'D'
												WHERE transactionID = @rsaTransactionID;

												select @saleID = min(ts.saleID)
												from dbo.tr_applications as tra
												inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
												where tra.orgID = @orgID
												and tra.itemID = #local.qryThisProductItemSaleDetails.orderDetailID#
												and tra.itemType = 'ProductPerItem'
												and tra.status = 'A'
												and ts.saleID > @saleID;
											END

											<!--- new shipping transaction --->
											select @invoiceProfileID=null, @invoiceID=null, @detail=null;
											select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.shippingGLAccountID#);
											
											select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
											IF @invoiceID is null BEGIN
												EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
													@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
													@invoiceNumber=@invoiceNumber OUTPUT;
												INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
												VALUES (@invoiceID, @invoiceProfileID, null);
											END

											SET @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle# - #local.qryCart.shippingName# shipping';

											EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
												@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
												@detail=@detail, @parentTransactionID=null, @amount=#local.qryCart.ItemShipping#, @transactionDate=@nowDate, 
												@creditGLAccountID=#local.qryCart.shippingGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
												@taxAmount=<cfif structKeyExists(local.strStoreTax,"#local.qryCart.cartItemID#_ship")>#local.strStoreTax["#local.qryCart.cartItemID#_ship"]#<cfelse>null</cfif>, 
												@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;

											EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@transactionID, @itemType='ProductPerItem', @itemID=#local.qryThisProductItemSaleDetails.orderDetailID#, @subItemID=null;
										END

										ELSE BEGIN

											select @invoiceProfileID=null, @invoiceID=null, @detail=null;

											<!--- Per item shipping sales --->
											<cfif local.qryCart.perItem>
												select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.shippingGLAccountID#);
												select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
												IF @invoiceID is null BEGIN
													EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
														@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
														@invoiceNumber=@invoiceNumber OUTPUT;
													INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
													VALUES (@invoiceID, @invoiceProfileID, null);
												END

												SET @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle# - #local.qryCart.shippingName# shipping';

												EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
													@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
													@detail=@detail, @parentTransactionID=null, @amount=#local.qryCart.perItem#, @transactionDate=@nowDate, 
													@creditGLAccountID=#local.qryCart.shippingGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
													@taxAmount=<cfif structKeyExists(local.strStoreTax,"#local.qryCart.cartItemID#_ship")>#local.strStoreTax["#local.qryCart.cartItemID#_ship"]#<cfelse>null</cfif>, 
													@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;

												EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@transactionID, @itemType='ProductPerItem', @itemID=#local.qryThisProductItemSaleDetails.orderDetailID#, @subItemID=null;
											</cfif>	

										END
									<cfelse>
										select @rateTransactionID = null, @newRateTransactionID = null;

										select @rateTransactionID = min(fsot.transactionID)
										from dbo.fn_store_orderTransactions(@orgID,@orderID) as fsot
										inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fsot.transactionID 
										inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.transactionID = ts.transactionID
										where tra.itemID = #local.qryThisProductItemSaleDetails.orderDetailID#
										and tra.itemType = 'Product'
										and tra.status = 'A';
									</cfif>

									<cfif local.couponAppliedItem and local.thisItemDiscount gt 0>
										IF @newRateTransactionID is null BEGIN
											select @invoiceProfileID = null, @invoiceID = null, @invoiceNumber = null;

											select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.rateGLAccountID#);
											
											select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
										
											-- if necessary, create invoice assigned to registrant based on invoice profile
											IF @invoiceID is null BEGIN
												EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
													@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, 
													@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
							
												INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
												VALUES (@invoiceID, @invoiceProfileID, null);
											END
										END

										<cfset local.thisQtyDiscountAmt = min(val(local.thisRateAmount),local.thisItemDiscount)>

										set @discountAmount = #local.thisQtyDiscountAmt# * - 1;
										set @productItemID = #int(val(local.qryCart.ItemID))#;

										-- promo code : check if coupon max usage count met
										select @couponCode = null, @maxOverallUsageCount = null, @maxMemberUsageCount = null, @redemptionCount = null, 
											@redemptionCountPerMember = null, @SPRedemptionCount = null;
										
										select @couponCode = couponCode, @maxOverallUsageCount = maxOverallUsageCount, @maxMemberUsageCount = maxMemberUsageCount,
											@SPRedemptionCount = storeXML.value('(/store/p/i[text()=sql:variable("@productItemID")]/@rc)[1]', 'int')
										from dbo.tr_coupons
										where siteID = @siteID
										and couponID = @couponID;

										set @SPRedemptionCount = isnull(@SPRedemptionCount,1);

										;with redemptionsCheck as (
											select distinct td.itemType, td.itemID, td.redemptionCount, 
												case when m.memberID is not null then td.redemptionCount else 0 end as memberRedemptionCount
											from dbo.tr_transactionDiscounts as td
											inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
											left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID and m.activeMemberID = @assignedToMemberID
											where td.couponID = @couponID
											and not (td.itemID = @orderID and itemType = 'StoreOrder')
										)
										select @redemptionCount = SUM(redemptionCount), @redemptionCountPerMember = SUM(memberRedemptionCount) 
										from redemptionsCheck;

										IF @maxOverallUsageCount > 0 AND ISNULL(@redemptionCount,0) + @SPRedemptionCount > @maxOverallUsageCount
											UPDATE dbo.store_orders
											SET orderNotes = isnull(orderNotes,'') + ' - Overall Redemptions Count for Coupon Code(' + @couponCode + ') exceeded; '
											WHERE orderID = @orderID;

										IF @maxMemberUsageCount > 0 AND ISNULL(@redemptionCountPerMember,0) + @SPRedemptionCount > @maxMemberUsageCount
											UPDATE dbo.store_orders
											SET orderNotes = isnull(orderNotes,'') + ' - Overall Redemptions Count Per Member for Coupon Code(' + @couponCode + ') exceeded; '
											WHERE orderID = @orderID;

										EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
											@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@nowDate, 
											@saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @couponID=@couponID, 
											@itemType='StoreOrder', @itemID=@orderID, @redemptionCount=@SPRedemptionCount, 
											@transactionID=@discountAdjTransactionID OUTPUT;

										<cfset local.thisItemDiscount = local.thisItemDiscount - local.thisQtyDiscountAmt>
									</cfif>

									<!--- shipping Method changed--->
									<cfif local.shippingMethodChanged>
										set @saleID = null;
										select @saleID = min(ts.saleID)
										from dbo.tr_applications as tra
										inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
										where tra.orgID = @orgID
										and tra.itemID = #local.qryThisProductItemSaleDetails.orderDetailID#
										and tra.itemType = 'ProductPerItem'
										and tra.status = 'A';

										<!--- Perform per item shipping sale adjustment --->
										IF @saleID is not null BEGIN

											WHILE @saleID is not null BEGIN 
												-- qryAdjust
												select @rsaTransactionID = null, @rsaOldRatePaidNeg = null, @rsaOldRatePaid = null;
												select @rsaTransactionID = fsot.transactionID, @rsaOldRatePaidNeg = ts.cache_amountAfterAdjustment * -1, 
													@rsaOldRatePaid = ts.cache_amountAfterAdjustment
												from dbo.fn_store_orderTransactions(@orgID,@orderID) as fsot
												inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fsot.transactionid 
												inner join dbo.tr_invoiceTransactions as tit on tit.orgID = @orgID and tit.transactionID = fsot.transactionid 
												where ts.saleID = @saleID;

												<!--- Adjust transaction --->
												IF isnull(@rsaOldRatePaid,0) > 0 BEGIN
													set @GlAccountID = null;
													select @GlAccountID = gl.GLAccountID
													from dbo.tr_glAccounts as gl
													inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.GLAccountID
													where t.transactionID = @rsaTransactionID;

													select @invoiceProfileID=null, @invoiceID=null;
													select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(@GlAccountID);
													select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
													IF @invoiceID is null BEGIN
														EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
															@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
															@invoiceNumber=@invoiceNumber OUTPUT;
														INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
														VALUES (@invoiceID, @invoiceProfileID, null);
													END

													EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
														@statsSessionID=@statsSessionID, @amount=@rsaOldRatePaidNeg, @taxAmount=null, @transactionDate=@nowDate, 
														@autoAdjustTransactionDate=1, @saleTransactionID=@rsaTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
														@byPassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;
												END

												UPDATE dbo.tr_applications
												SET [status] = 'D'
												WHERE transactionID = @rsaTransactionID;

												select @saleID = min(ts.saleID)
												from dbo.tr_applications as tra
												inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
												where tra.orgID = @orgID
												and tra.itemID = #local.qryThisProductItemSaleDetails.orderDetailID#
												and tra.itemType = 'ProductPerItem'
												and tra.status = 'A'
												and ts.saleID > @saleID;
											END

											<!--- new shipping transaction --->
											select @invoiceProfileID=null, @invoiceID=null, @detail=null;
											select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.shippingGLAccountID#);

											select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
											IF @invoiceID is null BEGIN
												EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
													@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
													@invoiceNumber=@invoiceNumber OUTPUT;
												INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
												VALUES (@invoiceID, @invoiceProfileID, null);
											END

											SET @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle# - #local.qryCart.shippingName# shipping';

											EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
												@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
												@detail=@detail, @parentTransactionID=null, @amount=#local.qryCart.ItemShipping#, @transactionDate=@nowDate, 
												@creditGLAccountID=#local.qryCart.shippingGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
												@taxAmount=<cfif structKeyExists(local.strStoreTax,"#local.qryCart.cartItemID#_ship")>#local.strStoreTax["#local.qryCart.cartItemID#_ship"]#<cfelse>null</cfif>, 
												@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;

											EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@transactionID, @itemType='ProductPerItem', @itemID=#local.qryThisProductItemSaleDetails.orderDetailID#, @subItemID=null;
										END 

										ELSE BEGIN

											select @invoiceProfileID=null, @invoiceID=null, @detail=null;

											<!--- Per item shipping sales --->
											<cfif local.qryCart.perItem>
												select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryCart.shippingGLAccountID#);
												select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
												IF @invoiceID is null BEGIN
													EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
														@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
														@invoiceNumber=@invoiceNumber OUTPUT;
													INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
													VALUES (@invoiceID, @invoiceProfileID, null);
												END

												SET @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle# - #local.qryCart.shippingName# shipping';

												EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
													@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
													@detail=@detail, @parentTransactionID=null, @amount=#local.qryCart.perItem#, @transactionDate=@nowDate, 
													@creditGLAccountID=#local.qryCart.shippingGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
													@taxAmount=<cfif structKeyExists(local.strStoreTax,"#local.qryCart.cartItemID#_ship")>#local.strStoreTax["#local.qryCart.cartItemID#_ship"]#<cfelse>null</cfif>, 
													@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;

												EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@transactionID, @itemType='ProductPerItem', @itemID=#local.qryThisProductItemSaleDetails.orderDetailID#, @subItemID=null;
											</cfif>	
									
										END
										
									</cfif>
								</cfloop>
							</cfif>

						</cfloop>

						<!--- Perform sale adjustment for deleted items --->
						<cfset local.perShipmentDeleted = 0>					
						<cfif listLen(local.deletedItemList)>
							<cfloop list="#local.deletedItemList#" index="local.thisSaleID">
								<cfquery name="local.qryGetThisOrderDetails" dbtype="query">
									select rateID, orderDetailID
									from [local].strOrderDetails.qryOrderProductSaleDetails
									where saleID = #val(local.thisSaleID)#
								</cfquery>
									
								<cfloop query="local.strOrderDetails.qryOrderShippingSaleDetails">
									<cfif local.qryGetThisOrderDetails.rateid eq local.strOrderDetails.qryOrderShippingSaleDetails.rateid>
										<cfif local.strOrderDetails.qryOrderShippingSaleDetails.itemType eq "ProductPerShipment">
											<cfset local.perShipmentDeleted = 1>
										</cfif>

										-- qryAdjust
										select @rsaTransactionID = null, @rsaOldRatePaidNeg = null, @rsaOldRatePaid = null;
										select @rsaTransactionID = fsot.transactionID, @rsaOldRatePaidNeg = ts.cache_amountAfterAdjustment * -1, 
											@rsaOldRatePaid = ts.cache_amountAfterAdjustment
										from dbo.fn_store_orderTransactions(@orgID,@orderID) as fsot
										inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fsot.transactionid 
										inner join dbo.tr_invoiceTransactions as tit on tit.orgID = @orgID and tit.transactionID = fsot.transactionid 
										where ts.saleID = #local.strOrderDetails.qryOrderShippingSaleDetails.saleID#;

										<!--- Adjust transaction --->
										IF isnull(@rsaOldRatePaid,0) > 0 begin
											set @GlAccountID = null;
											select @GlAccountID = gl.GLAccountID
											from dbo.tr_glAccounts as gl
											inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.GLAccountID
											where t.transactionID = @rsaTransactionID;

											select @invoiceProfileID=null, @invoiceID=null;
											select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(@GlAccountID);
											select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
											IF @invoiceID is null begin
												EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
													@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
													@invoiceNumber=@invoiceNumber OUTPUT;
												INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
												VALUES (@invoiceID, @invoiceProfileID, null);
											end

											exec dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
												@statsSessionID=@statsSessionID, @amount=@rsaOldRatePaidNeg, @taxAmount=null, @transactionDate=@nowDate, 
												@autoAdjustTransactionDate=1, @saleTransactionID=@rsaTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
												@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
										end

										UPDATE dbo.tr_applications
										SET [status] = 'D'
										WHERE transactionID = @rsaTransactionID;
									</cfif>							
								</cfloop>
							</cfloop>

							<cfquery name="local.qryDeleteSaleDetails" dbtype="query">
								select orderDetailID, saleID
								from [local].strOrderDetails.qryOrderProductSaleDetails
								where saleID in (0#local.deletedItemList#)
							</cfquery>				

							<cfoutput query="local.qryDeleteSaleDetails" group="orderDetailID">
								<cfoutput>
									-- qryAdjust
									select @rsaTransactionID = null, @rsaOldRatePaidNeg = null, @rsaOldRatePaid = null;
									select @rsaTransactionID = fsot.transactionID, @rsaOldRatePaidNeg = ts.cache_amountAfterAdjustment * -1, 
										@rsaOldRatePaid = ts.cache_amountAfterAdjustment
									from dbo.fn_store_orderTransactions(@orgID,@orderID) as fsot
									inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fsot.transactionid 
									inner join dbo.tr_invoiceTransactions as tit on tit.orgID = @orgID and tit.transactionID = fsot.transactionid 
									where ts.saleID = #local.qryDeleteSaleDetails.saleID#;

									<!--- Adjust transaction --->
									IF isnull(@rsaOldRatePaid,0) > 0 begin
										set @GlAccountID = null;
										select @GlAccountID = gl.GLAccountID
										from dbo.tr_glAccounts as gl
										inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.GLAccountID
										where t.transactionID = @rsaTransactionID;

										select @invoiceProfileID=null, @invoiceID=null;
										select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(@GlAccountID);
										select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
										IF @invoiceID is null begin
											EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
												@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
												@invoiceNumber=@invoiceNumber OUTPUT;
											INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
											VALUES (@invoiceID, @invoiceProfileID, null);
										end

										exec dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
											@statsSessionID=@statsSessionID, @amount=@rsaOldRatePaidNeg, @taxAmount=null, @transactionDate=@nowDate, 
											@autoAdjustTransactionDate=1, @saleTransactionID=@rsaTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
											@byPassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;
									end

									-- mark any discount rows as not active against this adjusted sale
									UPDATE td
									set td.isActive = 0
									from dbo.tr_transactionDiscounts as td
									inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.transactionID = td.transactionID
										and tr.typeID = @tr_AdjustTrans
										and tr.appliedToTransactionID = @rsaTransactionID
									where td.orgID = @orgID
									and td.isActive = 1;

									UPDATE dbo.tr_applications
									SET [status] = 'D'
									WHERE transactionID = @rsaTransactionID;
								</cfoutput>

								delete from dbo.store_orderDetailStreams
								where orderDetailID = #local.qryDeleteSaleDetails.orderDetailID#;

								delete from dbo.store_orderDetails
								where orderDetailID = #local.qryDeleteSaleDetails.orderDetailID#;
							</cfoutput>
						</cfif>

						<!--- If shipping method changed in the order --->
						<cfif local.shippingMethodChanged and local.strOrderDetails.qryOrderProductSaleDetails.recordCount and not local.perShipmentDeleted>
							<cfloop query="local.strOrderDetails.qryOrderShippingSaleDetails">
								<cfif local.strOrderDetails.qryOrderShippingSaleDetails.itemType eq "ProductPerShipment">
									<cfset local.perShipmentDeleted = 1>

									-- qryAdjust
									select @rsaTransactionID = null, @rsaOldRatePaidNeg = null, @rsaOldRatePaid = null;
									select @rsaTransactionID = fsot.transactionID, @rsaOldRatePaidNeg = ts.cache_amountAfterAdjustment * -1, 
										@rsaOldRatePaid = ts.cache_amountAfterAdjustment
									from dbo.fn_store_orderTransactions(@orgID,@orderID) as fsot
									inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fsot.transactionid 
									inner join dbo.tr_invoiceTransactions as tit on tit.orgID = @orgID and tit.transactionID = fsot.transactionid 
									where ts.saleID = #local.strOrderDetails.qryOrderShippingSaleDetails.saleID#;

									<!--- Adjust transaction --->
									IF isnull(@rsaOldRatePaid,0) > 0 begin
										set @GlAccountID = null;
										select @GlAccountID = gl.GLAccountID
										from dbo.tr_glAccounts as gl
										inner join dbo.tr_transactions as t on t.creditGLAccountID = gl.GLAccountID
										where t.transactionID = @rsaTransactionID;

										select @invoiceProfileID=null, @invoiceID=null;
										select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(@GlAccountID);
										select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
										IF @invoiceID is null begin
											EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
												@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
												@invoiceNumber=@invoiceNumber OUTPUT;
											INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
											VALUES (@invoiceID, @invoiceProfileID, null);
										end

										exec dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
											@statsSessionID=@statsSessionID, @amount=@rsaOldRatePaidNeg, @taxAmount=null, @transactionDate=@nowDate, 
											@autoAdjustTransactionDate=1, @saleTransactionID=@rsaTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
											@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
									end

									UPDATE dbo.tr_applications
									SET [status] = 'D'
									WHERE transactionID = @rsaTransactionID;
								</cfif>							
							</cfloop>						
						</cfif>	

						<cfif local.perShipmentDeleted OR not local.strOrderDetails.qryOrderProductSaleDetails.recordCount>
							
							<!--- Shipping sales --->
							<cfquery name="local.qryPerShipment" dbtype="query">
								select shippingGLAccountID, perShipment
								from [local].qryCart
								order by perShipment DESC				
							</cfquery>		

							<!--- Per shipment sale --->
							<cfif val(local.qryPerShipment.perShipment) gt 0>

								<!--- new shipping transaction --->
								select @invoiceProfileID=null, @invoiceID=null;
								select @invoiceProfileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.qryPerShipment.shippingGLAccountID#);
								select TOP 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
								IF @invoiceID is null begin
									EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
										@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, @invoiceID=@invoiceID OUTPUT, 
										@invoiceNumber=@invoiceNumber OUTPUT;
									INSERT INTO @tblInvoices (invoiceID, invoiceProfileID, amount)
									VALUES (@invoiceID, @invoiceProfileID, null);
								end

								-- qryGetShippingData
								set @shippingName = null;
								select @shippingName = ssm.shippingName
								from dbo.store_orders as o
								inner join dbo.store_ShippingMethods as ssm on ssm.shippingID = replace(o.shippingKey,'PID','')
								where o.orderID = @orderID;

								set @detail = 'Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - ' + isnull(@shippingName,'') + ' shipping';

								EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
									@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', 
									@detail=@detail, @parentTransactionID=null, @amount=#local.qryPerShipment.perShipment#, @transactionDate=@nowDate, 
									@creditGLAccountID=#local.qryPerShipment.shippingGLAccountID#, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
									@taxAmount=<cfif structKeyExists(local.strStoreTax,"pershipment")>#local.strStoreTax["pershipment"]#<cfelse>null</cfif>, 
									@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;

								select @newOrderDetailID = max(orderDetailID) from dbo.store_orderDetails where orderID = @orderID;

								insert into dbo.tr_applications (applicationTypeID, transactionID, itemType, itemID, status, orgID)
								values (@storeAppTypeID, @transactionID, 'ProductPerShipment', @newOrderDetailID, 'A', @orgID);
							</cfif>	
						
						</cfif>

						EXEC dbo.store_finalizeOrder @storeID=@storeID, @orderID=@orderID, @orderNumber='#local.orderNumber#', 
							@memberID=@assignedToMemberID, @profileID=#local.qryMerchantProfile.profileID#, 
							@xmlShippingInfo='#local.qryOrder.xmlShippingInfo#';

						select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) from @tblInvoices group by invoiceID;
						if len(@invoiceIDList) > 0
							EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceIDList;

					COMMIT TRAN;

					select @orderID as orderID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
				
			<cfset local.strResponse.success = true>
			<cfset local.strResponse.profileCode = local.qryMerchantProfile.profileCode>

			<!--- run storeOrderFinalized hook with orderID --->
			<cfset local.strHookData = { orderID=local.qryUpdateOrder.orderID }>
			<cfset application.objCMS.runHook(event='storeOrderFinalized', siteResourceID=local.storeInfo.siteResourceID, data=local.strHookData)>

			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.strResponse.response = cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn local.strResponse>
	</cffunction>	
		
</cfcomponent>