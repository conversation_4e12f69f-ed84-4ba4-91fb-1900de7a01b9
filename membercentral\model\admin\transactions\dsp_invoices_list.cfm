<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfset local.lowAmtDue = numberformat(arguments.event.getValue('dueAmtStart'),"9.99")>
<cfset local.highAmtDue = numberformat(arguments.event.getValue('dueAmtEnd'),"9.99")>
<cfif local.lowAmtDue is local.highAmtDue and local.highAmtDue is 0>
	<cfset local.lowAmtDue = "">
	<cfset local.highAmtDue = "">
</cfif>

<cfset local.lowAmtInv = numberformat(arguments.event.getValue('invAmtStart'),"9.99")>
<cfset local.highAmtInv = numberformat(arguments.event.getValue('invAmtEnd'),"9.99")>
<cfif local.lowAmtInv is local.highAmtInv and local.highAmtInv is 0>
	<cfset local.lowAmtInv = "">
	<cfset local.highAmtInv = "">
</cfif>

<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/manageInvoices.js#local.assetCachingKey#"></script>
	<script language="javascript">
		let invoiceListTable, checkAllInvs = 1, arrUnchkedInvs = [], arrChkedInvs = [], totalInvCount=0;
		var #ToScript(local.invoiceListLink,'invoiceListLink')#
		var #ToScript(local.memberTransationsLink,'link_mtl')#
		var #ToScript(this.link.viewInvoiceInfo,'link_vii')#
		var #ToScript(this.link.editInvoice,'link_ei')#
		var #ToScript(this.link.addPayment,'link_ap')#
		var #ToScript(this.link.allocatePayment,'link_allocp')#
		var #ToScript(this.link.memSelectGotoLink,'link_msgtl')#
		var #ToScript(this.link.grpSelectGotoLink,'link_gsgtl')#
		var #ToScript(this.link.generateInvoiceBundle,'link_gib')#
		var #ToScript(this.link.massEmailInvoices,'link_mei')#
		var #ToScript(this.link.changeInvoiceDates,'link_cid')#
		var #ToScript(local.setInvoicePayProfiles,'link_sipp')#
		<cfif local.myRightsTransactionsAdmin.transAllocatePayment is not 1>
			var transAllocatePayment = 0;
		<cfelse>
			var transAllocatePayment = 1;
		</cfif>

		$(function(){
			prepFilterForm();
			initInvoicesTable();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#Application.objCommon.minText(local.gridJS)#">

<cfoutput>
<!--- button bar --->
<div class="toolButtonBar" id="invoiceButtonBar">
	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1>
		<div><a href="##" onclick="createInvoice();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to create a new invoice."><i class="fa-regular fa-circle-plus"></i> Create Invoice</a></div>
	</cfif>
	<div><a href="javascript:filterInvoices();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter invoices."><i class="fa-regular fa-filter"></i> Filter Invoices</a></div>
	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceDownload') is 1>
		<div><a href="##" onclick="generateInvoiceBundle();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download filtered closed/delinquent/paid invoices."><i class="fa-regular fa-file-pdf"></i> Download Invoices</a></div>
		<div><a href="##" onclick="emailInvoices();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email filtered closed/delinquent/paid invoices."><i class="fa-regular fa-envelope"></i> Email Invoices</a></div>
	</cfif>
	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1>
		<div><a href="##" onclick="massCloseInvoices();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to close filtered open/pending invoices."><i class="fa-regular fa-file-lock"></i> Close Invoices</a></div>
	</cfif>
	<div><a href="##" onclick="manuallyProcessPayments();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to process filtered closed or delinquent invoices with a pay method on file."><i class="fa-regular fa-receipt"></i> Process Invoices</a></div>
	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1>
		<div><a href="##" onclick="updateInvoiceMessages();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to update end-of-invoice messages on the filtered invoices."><i class="fa-regular fa-comment-pen"></i> Update Messages</a></div>
		<div><a href="##" onclick="changeInvoiceDates();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to update billed dates or due dates on the filtered invoices."><i class="fa-regular fa-calendar-lines-pen"></i> Change Dates</a></div>
		<div><a href="##" onclick="setPayProfiles();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to update the accepted payment profiles on the filtered invoices."><i class="fa-regular fa-money-bill"></i> Set Pay Profiles</a></div>
	</cfif>
</div>
<div id="invoiceButtonBarWait"></div>

<div id="divFilterForm" style="display:none;">
	<div class="row mb-3">
		<div class="col-xl-12">
			<form name="frmFilter" id="frmFilter">
				<div class="card card-box mb-1">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-md">
							Filter Invoices
						</div>
					</div>
					<div class="card-body pb-3">					
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<select id="statusID" name="statusID" class="form-control form-control-sm" data-toggle="custom-select2" multiple="yes">
											<cfloop query="local.qryStatus">
												<option value="#local.qryStatus.statusID#"<cfif listFind(arguments.event.getTrimValue('statusID'),local.qryStatus.statusID)> selected</cfif>>#local.qryStatus.status#</option>
											</cfloop>
										</select>
										<label for="statusID">Status</label>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="billeddateStart" id="billeddateStart" value="#arguments.event.getValue('billeddateStart')#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="billeddateStart"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('billeddateStart');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="billeddateStart">Billed from</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="billeddateEnd" id="billeddateEnd" value="#arguments.event.getValue('billeddateEnd')#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="billeddateEnd"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('billeddateEnd');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="billeddateEnd">Billed to</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12 mb-2">
								<div class="input-group flex-nowrap">
									<div class="input-group-prepend">
										<span class="input-group-text">#arguments.event.getValue("mc_siteinfo.orgCode")#</span>
									</div>
									<div class="form-label-group flex-grow-1 mb-0">
										<input type="text" name="invoiceNumber" id="invoiceNumber" value="" class="form-control">
										<label for="invoiceNumber">Invoice number</label>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="duedateStart" id="duedateStart" value="#arguments.event.getValue('duedateStart')#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="duedateStart"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('duedateStart');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="duedateStart">Due from</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="duedateEnd" id="duedateEnd" value="#arguments.event.getValue('duedateEnd')#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="duedateEnd"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('duedateEnd');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="duedateEnd">Due to</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<select id="cardOnFile" name="cardOnFile" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
											<option value="0">No Pay Method</option>
											<cfloop query="local.qryPayProfiles">
												<option value="#local.qryPayProfiles.profileID#"<cfif listFind(arguments.event.getTrimValue('cardOnFile'),local.qryPayProfiles.profileID)> selected</cfif>>#local.qryPayProfiles.profileName#</option>
											</cfloop>
										</select>
										<label for="cardOnFile">Pay Method</label>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<select id="invProfile" name="invProfile" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
													<cfloop query="local.qryInvoiceProfiles">
														<option value="#local.qryInvoiceProfiles.profileID#"<cfif listFind(arguments.event.getTrimValue('profileID'),local.qryInvoiceProfiles.profileID)> selected</cfif>>#local.qryInvoiceProfiles.profileName#</option>
													</cfloop>
												</select>
												<label for="invProfile">Invoice Profile</label>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-label-group mb-0">
											<input type="text" name="trDetail" id="trDetail" value="#arguments.event.getValue('trDetail')#" class="form-control">
											<label for="trDetail">Transaction Detail Contains...</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="dueAmtStart" id="dueAmtStart" value="#local.lowAmtDue#" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
												<label for="dueAmtStart">Amt Due From</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="dueAmtEnd" id="dueAmtEnd" value="#local.highAmtDue#" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
												<label for="dueAmtEnd">Amt Due To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="invAmtStart" id="invAmtStart" value="#local.lowAmtInv#" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
												<label for="invAmtStart">Invoice Amt From</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="invAmtEnd" id="invAmtEnd" value="#local.highAmtInv#" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
												<label for="invAmtEnd">Invoice Amt To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group row mt-2">
							<div class="col-xl-6 col-lg-12">
								<div class="row">
									<div class="col-sm-12">
										Associated With:
										<input type="radio" name="assocType" id="assocTypeMember" class="assocType" value="member"> <label for="assocTypeMember">A Specific Member</label> &nbsp; 
										<input type="radio" name="assocType" id="assocTypeGroup" class="assocType" value="group"> <label for="assocTypeGroup">A Specific Group</label> &nbsp; 
										<a href="##" id="aClearAssocType" class="ml-2">clear</a>
										<input type="hidden" name="associatedMemberID" id="associatedMemberID" value="#arguments.event.getValue('associatedMemberID')#">
										<input type="hidden" name="associatedGroupID" id="associatedGroupID" value="#arguments.event.getValue('associateGroupID',0)#">
									</div>
								</div>
								<div class="row">
									<div class="col-sm-12 trAssoc" style="display:none;">
										<div id="associatedVal"></div>
										<b>Expand search to consider linked records</b><br/>
										<input type="radio" name="linkedRecords" id="linkedRecordsAll" value="all" checked="true"> <label for="linkedRecordsAll">Include children of the selected records in search</label><br/>
										<input type="radio" name="linkedRecords" id="linkedRecordSelected" value="selected"> <label for="linkedRecordSelected">Only include the specific selected records</label><br/><br/>
										<b>Expand results to consider linked records</b><br/>
										<input type="checkbox" name="assocRules" id="assocRulesAssigned" class="linkedRecordOptions" value="assigned" checked="true"> <label for="assocRulesAssigned">Invoices assigned to selected records</label><br/>
										<input type="checkbox" name="assocRules" id="assocRulesTrans" class="linkedRecordOptions" value="trans" checked="true"> <label for="assocRulesTrans">Invoices on which the selected records have transactions</label><br/>
										<input type="checkbox" name="assocRules" id="assocRulesPayment" class="linkedRecordOptions" value="payment" checked="true"> <label for="assocRulesPayment">Invoices on which the selected records have applied payments</label>
									</div>
								</div>
							</div>
						</div>					
					</div>
					<div class="card-footer p-2 text-right">
						<button type="button" name="btnClearFilterGrid" onclick="clearFilterInvoices()" class="btn btn-sm btn-secondary">Clear Filters</button>
						<button type="button" name="btnFilterGrid" class="btn btn-sm btn-primary" onclick="filterInvoiceGrid(true)"><i class="fa-light fa-filter"></i> Filter Invoices</button>
					</div>				
				</div>
			</form>
		</div>
	</div>
</div>
<div class="my-1">
	<table id="invoiceList" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th><input type="checkbox" name="masterCheckBox" id="masterCheckBox" onclick="doCheckAllInv(this.checked);" value="1" checked="checked"></th>
				<th>Due</th>
				<th>Invoice</th>
				<th>Assigned To</th>
				<th>Amount</th>
				<th>Due</th>
				<th>Actions</th>
			</tr>
		</thead>
	</table>
</div>
</cfoutput>