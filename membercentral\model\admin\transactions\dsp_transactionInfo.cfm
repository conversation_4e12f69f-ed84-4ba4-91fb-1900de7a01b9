<cfsavecontent variable="local.transactionInfoJS">
	<cfoutput>
	<script language="javascript">
	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transRefundPayment') is 1>
		function refundPayment(mid,ptid) {
			top.MCModalUtils.showLoading('Issue Refund');
			self.location.href = '#this.link.refundPayment#&mid=' + mid + '&ptid=' + ptid;
		}
	</cfif>

	function editTransaction(mid,tid) {
		top.MCModalUtils.showLoading('Accept Pending Payment');
		self.location.href = '#this.link.editTransaction#&mid=' + mid + '&tid=' + tid;
	}

	<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.transWriteOffPayment') is 1 and isDefined("local.qryTypeInfo.btn_canWriteOff") and local.qryTransaction.type neq "sale" and local.qryTypeInfo.btn_canWriteOff is 1)
		or (arguments.event.getValue('mc_admintoolInfo.myRights.transWriteOffSale') is 1 and isDefined("local.qryTypeInfo.btn_canWriteOff") and local.qryTransaction.type eq "sale" and local.qryTypeInfo.btn_canWriteOff is 1)>
		function writeOff(mid,tid) {
			top.MCModalUtils.showLoading('Write-Off Transaction');
			self.location.href = '#this.link.writeOffTransaction#&mid=' + mid + '&tid=' + tid;
		}
	</cfif>

	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transNSF') is 1>
		function chargebackTransaction(mid,tid) {
			top.MCModalUtils.showLoading();
			self.location.href = '#this.link.chargebackTransaction#&mid=' + mid + '&tid=' + tid;
		}
	</cfif>

	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAdjustSale') is 1>
		function adjustTransaction(tid) {
			top.MCModalUtils.showLoading('Adjust Sale');
			self.location.href = '#this.link.adjustTransaction#&tid=' + tid;
		}
	</cfif>

	<cfif local.qryTransaction.type eq "sale" and local.reclassAmount gt 0>
		function reclassTransaction(tid) {
			top.MCModalUtils.showLoading('Reclass/Split Revenue');
			self.location.href = '#this.link.reclassTransaction#&tid=' + tid;
		}
	</cfif>

	function changeSched(tid) {
		top.MCModalUtils.showLoading('Change Recognition Schedule');
		self.location.href = '#this.link.changeRecognitionSchedule#&tid=' + tid;
	}

	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transVoid') is 1>
		function voidTransaction(mid,tid) {
			top.MCModalUtils.showLoading('Void Transaction Confirmation Needed');
			self.location.href = '#this.link.voidTransaction#&mid=' + mid + '&tid=' + tid;
		}
	</cfif>

	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is 1>
		function deallocate(pmid,stid,ptid) {
			top.MCModalUtils.showLoading('Confirm Deallocation of Payment');
			self.location.href = '#this.link.deallocatePayment#&pmid=' + pmid + '&stid=' + stid + '&ptid=' + ptid;
		}
	</cfif>

	<cfif local.qryTransaction.type eq "payment" and local.qryCurrentAllocations.recordcount gt 0>
		<cfif local.myRightsInvoiceAdmin.invoiceDownload is 1>
			function downloadInvoices(tid) {
				$('##linkDownloadInvoice').addClass('d-none');
				$('##divDownloadInvoice').append('<span class="invDownloadLoading"><i class="fa-solid fa-circle-notch fa-spin"></i> Please Wait</span>');
				setTimeout(() => {
					$('##divDownloadInvoice .invDownloadLoading').remove();
					$('##linkDownloadInvoice').removeClass('d-none');
				}, 5000);
				self.location.href = '#this.link.downloadInvoiceBundle#&tid='+tid;
			}
		</cfif>
		<cfif local.myRightsInvoiceAdmin.invoiceEmail is 1>
			function emailInvoices(tid) {
				top.MCModalUtils.showLoading('Email Invoice');
				top.MCModalUtils.buildFooter({ classlist: 'd-none' });
				self.location.href = '#this.link.emailInvoiceBundle#&tid=' + tid;
			}
		</cfif>
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transRefundPayment') is 1 AND local.allowRefundPFD>
			function refundPFD(mid,ptid,profileid) {
				top.MCModalUtils.showLoading('Issue #encodeForJavaScript(local.additionalPaymentFeeLabel)# Refund');
				self.location.href = '#local.refundPFDLink#&mid=' + mid + '&ptid=' + ptid + '&profileid=' + profileid;
			}
		</cfif>
	</cfif>

	<cfif local.qryTransaction.type eq "refund">
		<cfif local.myRightsInvoiceAdmin.invoiceDownload is 1>
			function downloadRefundStatement(tid) {
				top.MCModalUtils.showLoading('Refund Description');
				self.location.href = '#this.link.downloadRefundStatementPrompt#&tid=' + tid;
			}
		</cfif>
		<cfif local.myRightsInvoiceAdmin.invoiceEmail is 1>
			function emailRefundStatement(tid) {
				top.MCModalUtils.showLoading('Email Refund Statement');
				top.MCModalUtils.buildFooter({ classlist: 'd-none' });
				self.location.href = '#this.link.emailRefundStatement#&tid=' + tid;
			}
		</cfif>
	</cfif>

	function loadRelatedTrans() {
		$('div##divRelatedTrans').load('#this.link.viewTransactionInfoRelated#&tid=#local.qryTransaction.transactionid#', 
			function(response, status, xhr) { 	
				$('div##divRelatedTransLoading').hide();
				if (status === 'error') {
					$(this).html('<b>There was an issue loading the related transactions. Contact us for assistance.</b>');
				}
			});
	}

	$(function() {
		window.setTimeout("loadRelatedTrans();", 500);
		top.MCModalUtils.setTitle('#encodeForJavaScript(local.qryTransaction.detail)#');
		top.MCModalUtils.buildFooter({});
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.transactionInfoJS)#">

<cfoutput>
<div class="d-flex p-3">
	<div class="col border-right pl-0">
		<h6 class="text-primary">#DollarFormat(local.qryTransaction.amount)# #local.qryTransaction.type# on #dateformat(local.qryTransaction.transactionDate,'m/d/yyyy')# #timeformat(local.qryTransaction.transactionDate,'h:mm tt')#</h6>
		<div class="mt-2">
			<div class="small text-dim">Assigned To:</div>
			<h6 class="mb-0">#encodeForHTML(local.qryTransaction.assignedToMember)#</h6>
			<cfif len(local.qryTransaction.assignedToMemberCompany)><div class="small">#encodeForHTML(local.qryTransaction.assignedToMemberCompany)#</div></cfif>
		</div>
	</div>
	<div class="col d-flex">
		<div id="tranOtherLinks" class="font-size-sm mx-auto">
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is 1 and isDefined("local.qryTypeInfo.btn_canApplyPayment")>
				<cfif local.qryTypeInfo.btn_canApplyPayment is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="mca_addPayment('#local.addPaymentEncString#');return false;"><i class="fa-solid fa-money-bill-1 text-green mr-1"></i> Pay for Sale</a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-money-bill-1 mr-1"></i> Pay for Sale</div>
				</cfif>
			</cfif>
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transRefundPayment') is 1 AND local.qryTransaction.type eq "payment" AND local.qryCurrentAllocations.recordcount gt 0 AND local.allowRefundPFD>
				<div class="mb-2 text-nowrap"><a href="##" onclick="refundPFD(#local.qryTransaction.assignedTomemberID#,#local.qryTransaction.transactionid#,#local.qryTypeInfo.profileID#);return false;"><i class="fa-solid fa-hand-holding-dollar text-darkred mr-1"></i> Refund #local.additionalPaymentFeeLabel#</a></div>
			</cfif>
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transRefundPayment') is 1 and isDefined("local.qryTypeInfo.btn_canRefund")>
				<cfif local.qryTypeInfo.btn_canRefund is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="refundPayment(#local.qryTransaction.assignedTomemberID#,#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-money-bill-1 text-darkred mr-1"></i> Refund Payment</a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-money-bill-1 mr-1"></i> Refund Payment</div>
				</cfif>
			</cfif>
			<cfif isDefined("local.qryTypeInfo.btn_canEdit") and local.qryTransaction.status eq "pending">
				<cfif local.qryTypeInfo.btn_canEdit is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="editTransaction(#local.qryTransaction.assignedTomemberID#,#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-money-bill-1 text-green mr-1"></i> Accept Pending Payment</a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-money-bill-1 mr-1"></i> Accept Pending Payment</div>
				</cfif>
			</cfif>
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transNSF') is 1 and isDefined("local.qryTypeInfo.btn_canNSF")>
				<cfif local.qryTypeInfo.btn_canNSF is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="chargebackTransaction(#local.qryTransaction.assignedTomemberID#,#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-triangle-exclamation text-goldenrod mr-1"></i> Mark payment as NSF</a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-triangle-exclamation mr-1"></i> Mark payment as NSF</div>
				</cfif>
			</cfif>
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAdjustSale') is 1 and isDefined("local.qryTypeInfo.btn_canAdjust")>
				<cfif local.qryTypeInfo.btn_canAdjust is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="adjustTransaction(#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-money-bill-1-wave text-green mr-1"></i> Adjust Revenue</a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-money-bill-1-wave mr-1"></i> Adjust Revenue</div>
				</cfif>
			</cfif>
			<cfif isDefined("local.qryTypeInfo.hasSchedule") and local.qryTypeInfo.hasSchedule is 1>
				<div class="mb-2 text-nowrap"><a href="##" onclick="changeSched(#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-pen-to-square mr-1"></i> Change Recognition Schedule</a></div>
			</cfif>
			<cfif local.qryTransaction.type eq "sale">
				<cfif local.reclassAmount gt 0>
					<div class="mb-2 text-nowrap"><a href="##" onclick="reclassTransaction(#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-square-pen text-green mr-1"></i> Reclass/Split Revenue</a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-square-pen mr-1"></i> Reclass/Split Revenue</div>
				</cfif>
			</cfif>
			<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.transWriteOffPayment') is 1 and isDefined("local.qryTypeInfo.btn_canWriteOff") and local.qryTransaction.type neq "sale")
				or (arguments.event.getValue('mc_admintoolInfo.myRights.transWriteOffSale') is 1 and isDefined("local.qryTypeInfo.btn_canWriteOff") and local.qryTransaction.type eq "sale")>
				<cfif local.qryTypeInfo.btn_canWriteOff is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="writeOff(#local.qryTransaction.assignedTomemberID#,#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-square-minus text-darkred mr-1"></i> Write Off <cfif local.qryTransaction.type eq "sale">Uncollected Revenue<cfelse>Overpaid Cash</cfif></a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-square-minus mr-1"></i> Write Off <cfif local.qryTransaction.type eq "sale">Uncollected Revenue<cfelse>Overpaid Cash</cfif></div>
				</cfif>
			</cfif>
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transVoid') is 1 and isDefined("local.qryTypeInfo.btn_canVoid")>
				<cfif local.qryTypeInfo.btn_canVoid is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="voidTransaction(#local.qryTransaction.assignedTomemberID#,#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-circle-minus text-danger mr-1"></i> Void #local.qryTransaction.type#</a></div>
				<cfelse>
					<div class="mb-2 text-nowrap text-muted"><i class="fa-solid fa-circle-minus mr-1"></i> Void #local.qryTransaction.type#</div>
				</cfif>
			</cfif>
			<cfif local.qryTransaction.type eq "payment" and local.qryCurrentAllocations.recordcount gt 0>
				<cfif local.myRightsInvoiceAdmin.invoiceDownload is 1>
					<div class="mb-2 text-nowrap" id="divDownloadInvoice"><a id="linkDownloadInvoice" href="##" onclick="downloadInvoices(#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-download mr-1"></i> Download Invoice(s)</a></div>	
				</cfif>
				<cfif local.myRightsInvoiceAdmin.invoiceEmail is 1>
					<div class="mb-2 text-nowrap"><a href="##" onclick="emailInvoices(#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-envelope mr-1"></i> Email Invoice(s)</a></div>
				</cfif>
			</cfif>

			<cfif local.qryTransaction.type eq "refund">
				<div class="mb-2 text-nowrap"><a href="##" onclick="downloadRefundStatement(#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-download mr-1"></i> Download Refund Statement</a></div>
				<div class="mb-2 text-nowrap"><a href="##" onclick="emailRefundStatement(#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-envelope mr-1"></i> Email Refund Statement</a></div>
			</cfif>

			<cfif local.qryTransaction.status eq "voided">
				<div id="tmetar">This transaction has been voided.</div>
			</cfif>
		</div>
	</div>
</div>

<cfswitch expression="#local.qryTransaction.type#">
	<cfcase value="sale,sales tax">
		<cfif local.qryTransaction.type eq "sales tax">
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-regular fa-money-bill-1 mr-1"></i> Tax for #local.qrySale.type#
					</div>
					<div class="card-body py-0">
						<div class="mb-1">This sales tax is linked to the following #local.qrySale.type# transaction:</div>
						<table class="table table-sm table-borderless">
							<tbody class="font-size-sm">
								<tr>
									<td width="70" class="align-top">#dollarformat(local.qrySale.amount)#</td>
									<td class="align-top">
										<div><a href="#this.link.viewTransactionInfo#&tid=#local.qrySale.transactionID#" onclick="top.MCModalUtils.showLoading();">#local.qrySale.detail#</a></div>
										<div>#local.qrySale.creditGL#</div>
										<div>#encodeForHTML(local.qrySale.assignedToMember)#</div>
										<cfif len(local.qrySale.assignedToMemberCompany)><div>#encodeForHTML(local.qrySale.assignedToMemberCompany)#</div></cfif>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</cfif>

		<cfif local.qryTransaction.status neq "voided" and local.qryTypeInfo.hasAdjustments is 1>
			<div class="alert alert-warning">
				<b>Note:</b> This #local.qryTransaction.type# has adjustments. Information below includes these adjustments.
			</div>
		</cfif>

		<cfif local.qryTransaction.status neq "voided">
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-regular fa-money-bill-1 mr-1"></i> Allocations
					</div>
					<div class="card-body py-0">
						<cfif local.qryTypeInfo.unallocatedAmount gt 0>
							<div class="mb-1"><b>#DollarFormat(local.qryTypeInfo.unallocatedAmount)#</b> is due for this #local.qryTransaction.type#.</div>
						</cfif>
						<cfif local.qryTypeInfo.allocatedAmount gt 0>
							<div class="mb-1"><b>#DollarFormat(local.qryTypeInfo.allocatedAmount)#</b> of this #local.qryTransaction.type# is paid for<cfif local.qryTypeInfo.allocatedAmount gt 0> by:<cfelse>.</cfif></div>
							<div class="mb-1 ml-3">
								<table class="table table-sm table-borderless<cfif local.qryCurrentAllocations.recordcount gt 1> table-hover</cfif>">
								<tbody class="font-size-sm">
								<cfloop query="local.qryCurrentAllocations">
									<tr>
										<td width="70" class="align-top">#dollarformat(local.qryCurrentAllocations.allocAmount)# from</td>
										<td class="align-top pl-2">
											<div><a href="#this.link.viewTransactionInfo#&tid=#local.qryCurrentAllocations.transactionID#" onclick="top.MCModalUtils.showLoading();">#local.qryCurrentAllocations.detail#</a></div>
											<div>#dollarformat(local.qryCurrentAllocations.Amount)# <cfif local.qryCurrentAllocations.typeID is 6>Write-Off<cfelse>Payment</cfif></div>
											<div>#local.qryCurrentAllocations.debitGL#</div>
											<cfif local.qryCurrentAllocations.typeID is not 6>
												<div>#encodeForHTML(local.qryCurrentAllocations.assignedToMember)#</div>
												<cfif len(local.qryCurrentAllocations.assignedToMemberCompany)><div>#encodeForHTML(local.qryCurrentAllocations.assignedToMemberCompany)#</div></cfif>
											</cfif>
										</td>
									</tr>
								</cfloop>
								</tbody>
								</table>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</cfif>

		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-receipt mr-1"></i> Invoice
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This #local.qryTransaction.type# and its related transactions appear on the following invoices:</div>
					<cfloop query="local.qryInvoices">
						<div class="mb-1"><a href="#this.link.viewInvoiceInfo#&vid=#local.qryInvoices.invoiceid#" onclick="top.MCModalUtils.showLoading();"><i class="fa-solid fa-receipt"></i> #local.qryInvoices.invoiceNumber#</a> (#local.qryInvoices.profileName#) - Due #dateformat(local.qryInvoices.dateDue,'m/d/yyyy')#</div>
					</cfloop>
				</div>
			</div>
		</div>

		<cfif local.qryDeferredSchedule.recordCount>
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-solid fa-calendar-days mr-1"></i> Deferred Recognition Schedule
					</div>
					<div class="card-body py-0">
						<cfloop query="local.qryDeferredSchedule">
							<div class="mb-1">#dollarFormat(local.qryDeferredSchedule.recogAmt)# on #DateFormat(local.qryDeferredSchedule.recognitionDate,'m/d/yyyy')#</div>
						</cfloop>
					</div>
				</div>
			</div>
		</cfif>
	</cfcase>
	<cfcase value="payment">
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-money-bill-1 mr-1"></i> Allocations
				</div>
				<div class="card-body py-0">
					<div class="mb-1"><b>#DollarFormat(local.qryTypeInfo.allocatedAmount)#</b> of this payment is allocated<cfif local.qryTypeInfo.allocatedAmount gt 0> to:<cfelse>.</cfif></div>
					<cfif local.qryCurrentAllocations.recordcount>
						<div class="mb-2 ml-3">
							<table class="table table-sm table-borderless<cfif local.qryCurrentAllocations.recordcount gt 1> table-hover</cfif>">
							<tbody class="font-size-sm">
							<cfloop query="local.qryCurrentAllocations">
								<tr>
									<cfif local.qryCurrentAllocations.recordcount gt 1 or local.qryCurrentAllocations.allocAmount is not local.qryTypeInfo.allocatedAmount>
										<td class="align-top text-nowrap" width="40">#dollarformat(local.qryCurrentAllocations.allocAmount)# to</td>
									</cfif>
									<td class="align-top pl-2">
										<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is 1 and val(local.qryCurrentAllocations.paymentFeeTypeID) EQ 0>
											<div class="float-right">
												<a href="##" onclick="deallocate(#local.qryTransaction.assignedTomemberID#,#local.qryCurrentAllocations.transactionid#,#local.qryTransaction.transactionid#);return false;"><i class="fa-solid fa-hexagon-minus text-danger"></i> Deallocate from Sale</a>
											</div>
										</cfif>
										<div><a href="#this.link.viewTransactionInfo#&tid=#local.qryCurrentAllocations.transactionID#" onclick="top.MCModalUtils.showLoading();">#local.qryCurrentAllocations.detail#</a></div>
										<div>#dollarformat(local.qryCurrentAllocations.Amount)# (sale, tax, and adjustments included)</div>
										<div>#local.qryCurrentAllocations.creditGL#</div>
										<div>#encodeForHTML(local.qryCurrentAllocations.assignedToMember)#</div>
										<cfif len(local.qryCurrentAllocations.assignedToMemberCompany)><div>#encodeForHTML(local.qryCurrentAllocations.assignedToMemberCompany)#</div></cfif>
									</td>
								</tr>
							</cfloop>
							</tbody>
							</table>
						</div>
					</cfif>
					<div class="mb-1"><b>#DollarFormat(local.qryTypeInfo.unallocatedAmount)#</b> of this payment is unallocated and can be refunded.</div>
				</div>
			</div>
		</div>
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-solid fa-calendar-days mr-1"></i> Batch
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This payment appears on the following <b>#local.qryTypeInfo.status#</b> batch dated <b>#dateformat(local.qryTypeInfo.depositDate,'m/d/yyyy')#</b>:</div>
					<div class="mb-1"><a href="#buildLinkToTool(toolType='BatchAdmin',mca_ta='viewBatch')#&bid=#local.qryTypeInfo.batchID#" target="_new">#local.qryTypeInfo.batchName#</a></div>
				</div>
			</div>
		</div>
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-money-bill-1 mr-1"></i> Payment Profile
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This payment was made using the <b>#local.qryTypeInfo.profileName#</b> payment profile.</div>
					<cfif len(local.qryTypeInfo.gatewayTransactionID)>
						<div class="mb-1">The Gateway Transaction ID returned was <b>#local.qryTypeInfo.gatewayTransactionID#</b>.</div>
					</cfif>
					<cfif len(local.qryTypeInfo.gatewayApprovalCode)>
						<div class="mb-1">The Gateway Approval Code returned was <b>#local.qryTypeInfo.gatewayApprovalCode#</b>.</div>
					</cfif>
					<cfif NOT StructIsEmpty(local.strPaymentGatewayInfo)>
						<div class="mb-1 ml-3">
							<table class="table table-sm table-borderless">
								<tbody class="font-size-sm">
								<cfloop collection="#local.strPaymentGatewayInfo#" item="local.key">
									<cfif NOT ListFindNoCase("Transaction ID,Approval Code,Gateway Transaction ID,Gateway Approval Code",local.key)>
										<tr><td class="align-top">#local.key#:</td><td class="align-top"><b>#local.strPaymentGatewayInfo[local.key]#</b></td></tr>
									</cfif>
								</cfloop>
								</tbody>
							</table>
						</div>
					</cfif>
				</div>
			</div>
		</div>
	</cfcase>
	<cfcase value="adjustment,negative adjustment">
		<cfif local.qryCouponInfo.recordcount>
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-regular fa-badge-dollar mr-1"></i> Coupon Adjustment
					</div>
					<div class="card-body py-0">
						<div class="mb-1">This adjustment is a coupon application to the revenue transaction noted in the "Adjusted Sale" section.</div>
						<div class="mb-1">Coupon Code: <b>#local.qryCouponInfo.couponCode#</b></div>
					</div>
				</div>
			</div>
		</cfif>
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-receipt mr-1"></i> Adjusted #local.qryAdjusting.type#
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This adjustment was made to the following #local.qryAdjusting.type# transaction:</div>
					<div class="mb-1 ml-3">
						<table class="table table-sm table-borderless">
							<tbody class="font-size-sm">
								<tr>
									<td width="70" class="align-top">#dollarformat(local.qryAdjusting.amount)#</td>
									<td class="align-top">
										<div><a href="#this.link.viewTransactionInfo#&tid=#local.qryAdjusting.transactionID#" onclick="top.MCModalUtils.showLoading();">#local.qryAdjusting.detail#</a></div>
										<div>#local.qryAdjusting.creditGL#</div>
										<div>#encodeForHTML(local.qryAdjusting.assignedToMember)#</div>
										<cfif len(local.qryAdjusting.assignedToMemberCompany)><div>#encodeForHTML(local.qryAdjusting.assignedToMemberCompany)#</div></cfif>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-receipt mr-1"></i> Invoice
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This adjustment appears on the following <b>#local.qryTypeInfo.status#</b> invoice due on <b>#dateformat(local.qryTypeInfo.dateDue,'m/d/yyyy')#</b>:</div>
					<div class="mb-1"><a href="#this.link.viewInvoiceInfo#&vid=#local.qryTypeInfo.invoiceid#" onclick="top.MCModalUtils.showLoading();"><i class="fa-solid fa-receipt"></i> #local.qryTypeInfo.invoiceNumber#</a> (#local.qryTypeInfo.profileName#)</div>
				</div>
			</div>
		</div>
		<cfif local.qryDeferredSchedule.recordCount>
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-solid fa-calendar-days mr-1"></i> Deferred Recognition Schedule
					</div>
					<div class="card-body py-0">
						<cfloop query="local.qryDeferredSchedule">
							<div class="mb-1">#dollarFormat(local.qryDeferredSchedule.recogAmt)# on #DateFormat(local.qryDeferredSchedule.recognitionDate,'m/d/yyyy')#</div>
						</cfloop>
					</div>
				</div>
			</div>
		</cfif>
	</cfcase>
	<cfcase value="refund">
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-solid fa-calendar-days mr-1"></i> Batch
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This refund appears on the following <b>#local.qryTypeInfo.status#</b> batch dated <b>#dateformat(local.qryTypeInfo.depositDate,'m/d/yyyy')#</b>:</div>
					<div class="mb-1"><a href="#buildLinkToTool(toolType='BatchAdmin',mca_ta='viewBatch')#&bid=#local.qryTypeInfo.batchID#" target="_new">#local.qryTypeInfo.batchName#</a></div>
				</div>
			</div>
		</div>
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-money-bill-1 mr-1"></i> Payment Profile
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This refund was made using the <b>#local.qryTypeInfo.profileName#</b> payment profile.</div>
					<cfif len(local.qryTypeInfo.gatewayTransactionID)>
						<div class="mb-1">The Gateway Transaction ID returned was <b>#local.qryTypeInfo.gatewayTransactionID#</b>.</div>
					</cfif>
					<cfif len(local.qryTypeInfo.gatewayApprovalCode)>
						<div class="mb-1">The Gateway Approval Code returned was <b>#local.qryTypeInfo.gatewayApprovalCode#</b>.</div>
					</cfif>
					<cfif NOT StructIsEmpty(local.strPaymentGatewayInfo)>
						<div class="mb-1 ml-3">
							<table class="table table-sm table-borderless">
								<tbody class="font-size-sm">
								<cfloop collection="#local.strPaymentGatewayInfo#" item="local.key">
									<cfif NOT ListFindNoCase("Transaction ID,Approval Code,Gateway Transaction ID,Gateway Approval Code",local.key)>
										<tr><td width="100" class="align-top">#local.key#:</td><td class="align-top"><b>#local.strPaymentGatewayInfo[local.key]#</b></td></tr>
									</cfif>
								</cfloop>
								</tbody>
							</table>
						</div>
					</cfif>
				</div>
			</div>
		</div>
	</cfcase>
	<cfcase value="allocation,deallocation">
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-money-bill-1 mr-1"></i> #local.qryTransaction.type# of Payment to #local.qryCurrentAllocations.revenueType#
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This <b>#DollarFormat(local.qryTransaction.amount)#</b> #local.qryTransaction.type# is between the following:</div>
					<div class="mb-1 ml-3">
						<table class="table table-sm table-borderless">
							<tbody class="font-size-sm">
							<tr>
								<td width="70" class="align-top">#dollarformat(local.qryCurrentAllocations.cashAmount)#</td>
								<td class="align-top">
									<div><a href="#this.link.viewTransactionInfo#&tid=#local.qryCurrentAllocations.cashTransactionID#" onclick="top.MCModalUtils.showLoading();">#local.qryCurrentAllocations.cashDetail#</a></div>
									<div>#local.qryCurrentAllocations.cashDebitGL#</div>
									<div>#encodeForHTML(local.qryCurrentAllocations.cashAssignedToMember)#</div>
									<cfif len(local.qryCurrentAllocations.cashAssignedToMemberCompany)><div>#encodeForHTML(local.qryCurrentAllocations.cashAssignedToMemberCompany)#</div></cfif>
								</td>
							</tr>
							<tr>
								<td width="70" class="align-top">#dollarformat(local.qryCurrentAllocations.revenueAmount)#</td>
								<td class="align-top">
									<div><a href="#this.link.viewTransactionInfo#&tid=#local.qryCurrentAllocations.revenueTransactionID#" onclick="top.MCModalUtils.showLoading();">#local.qryCurrentAllocations.revenueDetail#</a></div>
									<div>#local.qryCurrentAllocations.revenueCreditGL#</div>
									<div>#encodeForHTML(local.qryCurrentAllocations.revenueAssignedToMember)#</div>
									<cfif len(local.qryCurrentAllocations.revenueAssignedToMemberCompany)><div>#encodeForHTML(local.qryCurrentAllocations.revenueAssignedToMemberCompany)#</div></cfif>
								</td>
							</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-solid fa-calendar-days mr-1"></i> Batch
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This #local.qryTransaction.type# appears on the following <b>#local.qryTypeInfo.status#</b> batch dated <b>#dateformat(local.qryTypeInfo.depositDate,'m/d/yyyy')#</b>:</div>
					<div class="mb-1"><a href="#buildLinkToTool(toolType='BatchAdmin',mca_ta='viewBatch')#&bid=#local.qryTypeInfo.batchID#" target="_new">#local.qryTypeInfo.batchName#</a></div>
				</div>
			</div>
		</div>
	</cfcase>
	<cfcase value="write off,negative write off">
		<cfif local.qryTransaction.type eq "negative write off">
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-solid fa-calendar-days mr-1"></i> Batch
					</div>
					<div class="card-body py-0">
						<div class="mb-1">This Negative Write Off appears on the following <b>#local.qryTypeInfo.status#</b> batch dated <b>#dateformat(local.qryTypeInfo.depositDate,'m/d/yyyy')#</b>:</div>
						<div class="mb-1"><a href="#buildLinkToTool(toolType='BatchAdmin',mca_ta='viewBatch')#&bid=#local.qryTypeInfo.batchID#" target="_new">#local.qryTypeInfo.batchName#</a></div>
					</div>
				</div>
			</div>
		</cfif>
	</cfcase>
	<cfcase value="voidoffset">
		<cfif isDefined("local.qryTypeInfo.batchID")>
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-solid fa-calendar-days mr-1"></i> Batch
					</div>
					<div class="card-body py-0">
						<div class="mb-1">This VoidOffset appears on the following <b>#local.qryTypeInfo.status#</b> batch dated <b>#dateformat(local.qryTypeInfo.depositDate,'m/d/yyyy')#</b>:</div>
						<div class="mb-1"><a href="#buildLinkToTool(toolType='BatchAdmin',mca_ta='viewBatch')#&bid=#local.qryTypeInfo.batchID#" target="_new">#local.qryTypeInfo.batchName#</a></div>
					</div>
				</div>
			</div>
		<cfelse>
			<div class="section px-3 py-2">
				<div class="card card-box shadow-none">
					<div class="card-title pl-2 pt-2">
						<i class="fa-regular fa-receipt mr-1"></i> Invoice
					</div>
					<div class="card-body py-0">
						<div class="mb-1">This VoidOffset appears on the following <b>#local.qryTypeInfo.status#</b> invoice due on <b>#dateformat(local.qryTypeInfo.dateDue,'m/d/yyyy')#</b>:</div>
						<div class="mb-1"><a href="#this.link.viewInvoiceInfo#&vid=#local.qryTypeInfo.invoiceid#" onclick="top.MCModalUtils.showLoading();"><i class="fa-solid fa-receipt"></i> #local.qryTypeInfo.invoiceNumber#</a> (#local.qryTypeInfo.profileName#)</div>
					</div>
				</div>
			</div>
		</cfif>
	</cfcase>
	<cfcase value="nsf">
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-solid fa-calendar-days mr-1"></i> Batch
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This NSF appears on the following <b>#local.qryTypeInfo.status#</b> batch dated <b>#dateformat(local.qryTypeInfo.depositDate,'m/d/yyyy')#</b>:</div>
					<div class="mb-1"><a href="#buildLinkToTool(toolType='BatchAdmin',mca_ta='viewBatch')#&bid=#local.qryTypeInfo.batchID#" target="_new">#local.qryTypeInfo.batchName#</a></div>
				</div>
			</div>
		</div>
	</cfcase>
	<cfcase value="deferred transfer">
		<div class="section px-3 py-2">
			<div class="card card-box shadow-none">
				<div class="card-title pl-2 pt-2">
					<i class="fa-regular fa-receipt mr-1"></i> Deferred Transfer for #local.qrySale.type#
				</div>
				<div class="card-body py-0">
					<div class="mb-1">This deferred transfer is linked to the following #local.qrySale.type# transaction:</div>
					<div class="mb-1 ml-3">
						<table class="table table-sm table-borderless">
						<tbody class="font-size-sm">
						<tr>
							<td class="align-top" width="70">#dollarformat(local.qrySale.amount)#</td>
							<td class="align-top">
								<div><a href="#this.link.viewTransactionInfo#&tid=#local.qrySale.transactionID#" onclick="top.MCModalUtils.showLoading();">#local.qrySale.detail#</a></div>
								<div>#local.qrySale.creditGL#</div>
								<div>#encodeForHTML(local.qrySale.assignedToMember)#</div>
								<cfif len(local.qrySale.assignedToMemberCompany)><div>#encodeForHTML(local.qrySale.assignedToMemberCompany)#</div></cfif>
							</td>
						</tr>
						</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</cfcase>
</cfswitch>

<cfif local.qryGLActivity.recordCount>
	<div class="section px-3 py-2">
		<div class="card card-box shadow-none">
			<div class="card-title pl-2 pt-2">
				<i class="fa-regular fa-tag mr-1"></i> GL Activity
			</div>
			<div class="card-body py-0">
				<table class="table table-sm table-borderless">
				<thead>
					<tr>
						<th>Account</th>
						<th class="text-right">Debits</th>
						<th class="text-right">Credits</th>
					</tr>
				</thead>
				<tbody class="font-size-sm">
					<tr><td colspan="3"></td></tr>
					<cfloop query="local.qryGLActivity">
						<tr>
							<td class="align-top">#local.qryGLActivity.glexpanded#</td>
							<td class="align-top text-right"><cfif len(local.qryGLActivity.debits)>#dollarformat(local.qryGLActivity.debits)#<cfelse>&nbsp;</cfif></td>
							<td class="align-top text-right"><cfif len(local.qryGLActivity.credits)>#dollarformat(local.qryGLActivity.credits)#<cfelse>&nbsp;</cfif></td>
						</tr>
					</cfloop>
				</tbody>
				</table>
			</div>
		</div>
	</div>
</cfif>

<div class="section px-3 py-2">
	<div class="card card-box shadow-none">
		<div class="card-title pl-2 pt-2">
			<i class="fa-regular fa-clock-rotate-left mr-1"></i> History and Related Transactions
		</div>
		<div class="card-body py-0">
			<div class="mb-1">
				Recorded <b>#dateformat(local.qryTransaction.dateRecorded,'m/d/yyyy')# #timeformat(local.qryTransaction.dateRecorded,'h:mm tt')# CT</b> 
				by #encodeForHTML(local.qryTransaction.recordedByMember)#
			</div>
			<div class="mb-1 mt-3 text-center" id="divRelatedTransLoading">
				<div class="spinner-border" role="status"></div>
				<div class="font-weight-bold mt-2">Loading related transactions...</div>
			</div>
			<div id="divRelatedTrans"></div>
		</div>
	</div>
</div>
	
<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
	<div class="section px-3 d-print-none">
		<div class="card card-box shadow-none bg-light">
			<div class="card-body">
				<div class="small text-dim text-center">Shown to MC Staff only</div>
				<div class="d-flex">
					<div class="col border-right">
						<div class="mb-2">
							<div class="small text-dim">TransactionID</div>
							<h6 class="mt-1">#local.qryTransaction.transactionID#</h6>
						</div>
						<cfswitch expression="#local.qryTransaction.type#">
							<cfcase value="sale,sales tax">
								<div class="mb-2">
									<div class="small text-dim">InvoiceID</div>
									<h6 class="mt-1">#valueList(local.qryInvoices.invoiceID)#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">SaleID</div>
									<h6 class="mt-1">#local.qryTypeInfo.saleID#</h6>
								</div>
							</cfcase>
							<cfcase value="payment">
								<div class="mb-2">
									<div class="small text-dim">BatchID</div>
									<h6 class="mt-1">#local.qryTypeInfo.batchID#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">PaymentID</div>
									<h6 class="mt-1">#local.qryTypeInfo.paymentID#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">HistoryID</div>
									<h6 class="mt-1">#local.qryTypeInfo.historyID#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">DatePaid</div>
									<h6 class="mt-1">#dateformat(local.qryTypeInfo.datePaid,'m/d/yyyy')# #timeformat(local.qryTypeInfo.datePaid,'h:mm tt')#</h6>
								</div>
							</cfcase>
							<cfcase value="adjustment,negative adjustment">
								<div class="mb-2">
									<div class="small text-dim">InvoiceID</div>
									<h6 class="mt-1">#local.qryTypeInfo.invoiceID#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">InvoiceProfileID</div>
									<h6 class="mt-1">#local.qryTypeInfo.invoiceProfileID#</h6>
								</div>
							</cfcase>
							<cfcase value="refund">
								<div class="mb-2">
									<div class="small text-dim">BatchID</div>
									<h6 class="mt-1">#local.qryTypeInfo.batchID#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">PaymentID</div>
									<h6 class="mt-1">#local.qryTypeInfo.paymentID#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">HistoryID</div>
									<h6 class="mt-1">#local.qryTypeInfo.historyID#</h6>
								</div>
								<div class="mb-2">
									<div class="small text-dim">DatePaid</div>
									<h6 class="mt-1">#dateformat(local.qryTypeInfo.datePaid,'m/d/yyyy')# #timeformat(local.qryTypeInfo.datePaid,'h:mm tt')#</h6>
								</div>
							</cfcase>
							<cfcase value="allocation,deallocation">
								<div class="mb-2">
									<div class="small text-dim">BatchID</div>
									<h6 class="mt-1">#local.qryTypeInfo.batchID#</h6>
								</div>
							</cfcase>
							<cfcase value="negative write off">
								<div class="mb-2">
									<div class="small text-dim">BatchID</div>
									<h6 class="mt-1">#local.qryTypeInfo.batchID#</h6>
								</div>
							</cfcase>
							<cfcase value="voidoffset">
								<cfif isDefined("local.qryTypeInfo.batchID")>
									<div class="mb-2">
										<div class="small text-dim">BatchID</div>
										<h6 class="mt-1">#local.qryTypeInfo.batchID#</h6>
									</div>
								<cfelse>
									<div class="mb-2">
										<div class="small text-dim">InvoiceID</div>
										<h6 class="mt-1">#local.qryTypeInfo.invoiceID#</h6>
									</div>
									<div class="mb-2">
										<div class="small text-dim">InvoiceProfileID</div>
										<h6 class="mt-1">#local.qryTypeInfo.invoiceProfileID#</h6>
									</div>
								</cfif>
							</cfcase>
							<cfcase value="nsf">
								<div class="mb-2">
									<div class="small text-dim">BatchID</div>
									<h6 class="mt-1">#local.qryTypeInfo.batchID#</h6>
								</div>
							</cfcase>
						</cfswitch>
					</div>
					<div class="col d-flex">
						<div class="mx-auto">
							<div class="mb-2">
								<div class="small text-dim">OwnedByOrgID</div>
								<h6 class="mt-1">#local.qryTransaction.ownedByOrgID#</h6>
							</div>
							<div class="mb-2">
								<div class="small text-dim">RecordedOnSiteID</div>
								<h6 class="mt-1">#local.qryTransaction.recordedOnSiteID#</h6>
							</div>
						</div>
					</div>
				</div>
				<cfif ListFindNoCase("payment,refund",local.qryTransaction.type)>
					<div class="mb-3 px-3">
						<div class="small text-dim">PaymentInfo</div>
						<textarea class="form-control" rows="10" readonly>#htmleditformat(local.qryTypeInfo.paymentInfo)#</textarea>
					</div>
					<div class="mb-2 px-3">
						<div class="small text-dim">GatewayResponse</div>
						<textarea class="form-control" rows="10" readonly>#htmleditformat(local.qryTypeInfo.gatewayResponse)#</textarea>
					</div>
				</cfif>
			</div>
		</div>
	</div>
</cfif>
</cfoutput>