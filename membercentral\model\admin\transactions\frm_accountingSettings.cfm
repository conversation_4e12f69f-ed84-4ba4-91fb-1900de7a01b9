<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
		function validateSettingsForm(e) {
			$('##settingserr').html('').addClass('d-none');
			$('.saveBtn').attr('disabled', true);

			var arrReq = new Array();
			if ($('##notifybadcof').val() == '1' && $.trim($('##notifyBadCOFMessage').val()).length == 0)
				arrReq[arrReq.length] = 'Notification message is required.';
			if ($('##fiscalYearStartMonth').val() == '')
				arrReq[arrReq.length] = 'First Month of Fiscal Year is required.';

			if (arrReq.length > 0) {
				$('##settingserr').html(arrReq.join('<br/>')).removeClass('d-none');
				$('.saveBtn').attr('disabled', false);
				return false;
			}
			return true;
		}

		$(document).ready(function () {
			$('.notifybadcof').change(function () {
				if ($(this).val()) {
					if ($(this).val() == '1') $('##divNotifyBadCOF1').show();
					else $('##divNotifyBadCOF1').hide();
				}
			});

			mca_setupTagsInput('accountingEmail', 'err_acct_emails', "#application.regEx.email#", 'email address');
			$('##divSuperKey').show();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.js#">

<cfoutput>
<form name="frmSettings" id="frmSettings" action="#this.link.saveSettings#" method="POST" onsubmit="return validateSettingsForm();">
	<div class="row no-gutters mb-2">
		<div class="col-auto">
			<h4>Accounting Settings</h4>
		</div>
		<div class="col text-right">
			<cfif arguments.event.getValue('c', 0) is 1><span class="font-weight-bold text-danger">Information saved.</span></cfif>
			<button type="submit" name="btnSave" class="btn btn-sm btn-primary saveBtn">Save Settings</button>
		</div>
	</div>

	<div id="settingserr" class="alert alert-danger mb-2 d-none"></div>

	<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert">
		<span class="font-size-lg d-block d-40 mr-2 text-center">
			<i class="fa-regular fa-circle-question"></i>
		</span>
		<span>
			<strong class="d-block">Warning!</strong>
			<div class="mt-1">
				Changing settings on this page may change options, actions, and screens found throughout the site.<br />
				Use extreme caution when changing information on this screen.
			</div>
		</span>
	</div>
	<div class="card mb-3">
		<div class="card-header bg-light">
			Member Notifications
		</div>
		<div class="card-body">
			<div class="form-group">
				<div class="form-label-group">
					<select name="notifybadcof" id="notifybadcof" class="custom-select notifybadcof">
						<option value="1" <cfif local.qrySettings.notifyBadCOF is 1>selected</cfif>>Yes, notify members upon login.</option>
						<option value="0" <cfif local.qrySettings.notifyBadCOF is not 1>selected</cfif>>No, do not notify members upon login.</option>
					</select>
					<label for="notifybadcof">Bad Credit Cards Associated with Overdue Invoices</label>
				</div>
			</div>
			<div class="form-group">
				<div class="form-label-group">
					<div id="divNotifyBadCOF1" class="form-check-text my-1" <cfif local.qrySettings.notifyBadCOF is not 1>style="display:none;"</cfif>>
						<textarea rows="3" name="notifyBadCOFMessage" id="notifyBadCOFMessage" class="form-control form-control-sm">#local.qrySettings.notifyBadCOFMessage#</textarea>
						<div class="font-size-sm font-italic">We'll automatically provide a link for members to update payment information.</div>
						<label for="notifyBadCOFMessage" class="form-check-text my-1">Notification message:</label>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="card mb-3">
		<div class="card-header bg-light">
			Reports and Dashboard
		</div>
		<div class="card-body">
			<div class="form-group">
				<div class="form-label-group">
					<select name="fiscalYearStartMonth" id="fiscalYearStartMonth" class="custom-select">
						<cfloop index="local.index" from="1" to="12">
							<option value="#local.index#" <cfif local.index eq local.qrySettings.fiscalYearStartMonth>selected</cfif>>#monthAsString(local.index)#</option>
						</cfloop>
					</select>
					<label for="fiscalYearStartMonth">First Month of Fiscal Year *</label>
					<div class="ml-2">Changing this setting will refresh all dashboard charts. It may take several minutes for that refresh to complete.</div>
				</div>
			</div>
			<div id="err_acct_emails" class="alert alert-danger mb-2 d-none"></div>
			<div class="form-group">
				<div class="form-label-group">
					<input type="text" name="accountingEmail" id="accountingEmail" value="#local.qrySettings.accountingEmail#" class="form-control" >
					<label for="accountingEmail">E-mail Recipient for Report</label>
					<div class="font-italic ml-2">
						We'll use this e-mail for:<br/>
						- the daily Invoice Payment Report<br/>
						- the weekly Accounting Issues Report
					</div>
				</div>
			</div>

			<div class="form-row">
				<div class="col">
					<label for="fs_IPReport" class="mb-0">Use this Member Field Set, when showing failed charges on the Invoice Payment Report</label>
					#local.strIPReportFSSelector.html#
					<input type="hidden" name="useid_IPReport" id="useid_IPReport" value="#local.ipreportUseID#">
					<input type="hidden" name="origfs_IPReport" id="origfs_IPReport" value="#local.ipreportFieldsetID#">
				</div>
			</div>
			<div class="form-text small text-dim">(First Name, Last Name, MemberNumber, and Company always appear.)</div>
		</div>
	</div>

	<div class="card mb-3">
		<div class="card-header bg-light">
			Payment Handling
		</div>
		<div class="card-body">
			<div class="form-group">
				<div class="form-label-group">
					<select name="defaultPending" id="defaultPending" class="custom-select">
						<option value="1" <cfif local.qrySettings.defaultPending is 1>selected</cfif>>Yes, automatically verify payment and bypass the pending state.</option>
						<option value="0" <cfif local.qrySettings.defaultPending is not 1>selected</cfif>>No, do not automatically verify payment.</option>
					</select>
					<label for="defaultPending">Default for Offline Payments</label>
				</div>
			</div>
			<div class="form-group">
				<div class="form-label-group">
					<select name="useBatches" id="useBatches" class="custom-select">
						<option value="1" <cfif local.qrySettings.useBatches is 1>selected</cfif>>Yes, batches will be self-managed.</option>
						<option value="0" <cfif local.qrySettings.useBatches is not 1>selected</cfif>>No, all batches will be handled automatically.</option>
					</select>
					<label for="useBatches">Use Self-Managed Batches</label>
				</div>
			</div>
			<div class="form-group">
				<div class="form-label-group">
					<select name="useAccrualAcct" id="useAccrualAcct" class="custom-select"<cfif NOT local.isSuperUser OR local.qrySettings.useAccrualAcct is 1> disabled</cfif>>
						<option value="1" <cfif local.qrySettings.useAccrualAcct is 1>selected</cfif>>Yes, accrual accounting is enabled.</option>
						<option value="0" <cfif local.qrySettings.useAccrualAcct is not 1>selected</cfif>>No, accrual accounting is not enabled.</option>
					</select>
					<label for="useAccrualAcct">Accrual Accounting
						<cfif NOT local.isSuperUser>
							<i class="fa-solid fa-circle-question fa-md ml-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Please contact Support for assistance in activating Accrual Accounting."></i>
						<cfelse>
							<span class="superuser"></span>
						</cfif>
					</label>
				</div>
			</div>
		</div>
	</div>

	<div class="card mb-3">
		<div class="card-header bg-light">
			Invoices App Content
		</div>
		<div class="card-body">
			<input type="hidden" name="invAppContentID" value="#local.qryInvAppContent.contentID#">
			<input type="hidden" name="managePayMethodsContentID" value="#local.qryManagePayMethodsContent.contentID#">
			<div>
				#application.objWebEditor.showContentBoxWithLinks(fieldname='invAppContent', fieldlabel='Invoices <i class="fa-solid fa-circle-question fa-md ml-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="This content will display on pg=invoices."></i>', contentID=local.qryInvAppContent.contentID, content=local.qryInvAppContent.rawContent, allowMergeCodes=1, supportsBootstrap=true, allowVersioning=true)#
			</div>
			<div class="mt-3">
				#application.objWebEditor.showContentBoxWithLinks(fieldname='managePayMethodsContent', fieldlabel='Manage Pay Methods <i class="fa-solid fa-circle-question fa-md ml-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="This content will display on pg=invoices&va=listpayprofiles."></i>', contentID=local.qryManagePayMethodsContent.contentID, content=local.qryManagePayMethodsContent.rawContent, allowMergeCodes=1, supportsBootstrap=true, allowVersioning=true)#
			</div>
		</div>
	</div>

	<div class="text-right">
		<button type="submit" name="btnSave" class="btn btn-sm btn-primary saveBtn">Save Settings</button>
	</div>
</form>
</cfoutput>