<cfcomponent extends="model.AppLoader" output="no">

	<cfset variables.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- send header to browser to not index these pages --->
		<cfheader name="X-Robots-Tag" value="noindex">

		<!--- get app instance settings --->
		<cfset variables.instanceSettings = getInstanceSettings(this.appInstanceID)>
		<cfset arguments.event.setValue('triggerKeepAlive',true)>

		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true" 
				or 
				(isdefined("session.enableMobile") and session.enableMobile)
				)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>

		<cfif arguments.event.valueExists('wizard')>
			<cfset local.methodToRun = this['doWizard']>
			<cfreturn local.methodToRun(arguments.event)>
		<cfelseif arguments.event.valueExists('wizardTS')>
			<cfset local.methodToRun = this['doWizardTS']>
			<cfreturn local.methodToRun(arguments.event)>
		<cfelseif arguments.event.valueExists('viewPolicy')>
			<cfset local.methodToRun = this['showPolicy']>
			<cfreturn local.methodToRun(arguments.event)>
		<cfelse>
			<cfscript>	
			// ensure item exists and put into rc for easier grabbing
			arguments.event.paramValue("item","");
			local.rc = arguments.event.getCollection();
			
			// set current URL
			arguments.event.setValue("currentURL","/?pg=buyNow&item=#local.rc.item#");
			
			// parse item
			local.strItem = parseItem(arguments.event);
			
			local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.rc.mc_siteInfo.orgID);

			/* determine state of activity (in correct order below)
			1 = buy now item not found
			2 = not identified
			3 = identified
			4 = purchase confirmed - receipt
			*/
			local.activityState = 1;
			if (NOT local.strItem.itemOK)
				local.activityState = 1;
			else if (local.useMemberID is 0 and ListFindNoCase("JOINTS,TSBILL,EXPERT,CDORDER",local.strItem.itemtype) is 0)
				local.activityState = 2;
			else if (arguments.event.getValue("confirmPurchase",0) is 1)
				local.activityState = 4;
			else if (local.useMemberID gt 0 or ListFindNoCase("JOINTS,TSBILL,EXPERT,CDORDER",local.strItem.itemtype))
				local.activityState = 3;

			// perform activity functions 
			switch (local.activityState) {
				case 1: 
					doLogBuyNow('controller','case statement','activityState 1 - start'); 
					local.contentToShow = local.strItem.notFoundTemplate; 
					doLogBuyNow('controller','case statement','activityState 1 - end'); 
					break;
				case 2: 
					doLogBuyNow('controller','case statement','activityState 2 - start'); 
					local.objLocator = CreateObject("component","model.system.user.accountLocater");
	
					switch(arguments.event.getValue('cartAction','')) {
						case "locator":
							local.resultsData = local.objLocator.locateMember(event=arguments.event,numResults=20);
							local.contentToShow = "locater_results";
							break;
						case "usemid":
							arguments.event.paramValue('mid',int(val(arguments.event.getValue('mid',0))));
							if (arguments.event.getValue('mid') gt 0 AND application.objMember.getMemberInfo(arguments.event.getValue('mid')).recordCount) {
								application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=arguments.event.getValue('mid'));
								application.objCommon.redirect(arguments.event.getValue('currentURL'));
							} else
								local.contentToShow = local.strItem.notIdentifiedTemplate; 
							break;
						case "newacct":
							local.newMemID = local.objLocator.createAccount(event=arguments.event);
							if (local.newMemID gt 0) 
								application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=local.newMemID);
							local.contentToShow = "locater_postNA";
							break;
						default:
							local.contentToShow = local.strItem.notIdentifiedTemplate; 
							break;
					}
					doLogBuyNow('controller','case statement','activityState 2 - end'); 
					break;
				case 3:
					doLogBuyNow('controller','case statement','activityState 3 - start'); 
					switch(arguments.event.getValue('cartAction','')) {
						case "useAID":
							local.qryAddress = application.objMember.getMemberAddress(orgID=local.rc.mc_siteInfo.orgID, memberid=session.cfcuser.memberdata.memberid, addressid=arguments.event.getValue('aid',0));
							arguments.event.setValue('fldship_attn',local.qryAddress.attn);
							arguments.event.setValue('fldship_address1',local.qryAddress.address1);
							arguments.event.setValue('fldship_city',local.qryAddress.city);
							arguments.event.setValue('fldship_state',local.qryAddress.stateid);
							arguments.event.setValue('fldship_zip',local.qryAddress.postalCode);
	
							// handle data and reget the parsed item with updated totals
							local.strItem.thisCFC.buyNow_shipping(arguments.event,local.strItem);
							local.strItem = parseItem(arguments.event);
							
							// dont break here - let it go to the next case condition						
						case "newAID": 
							// handle data and reget the parsed item with updated totals
							local.strItem.thisCFC.buyNow_shipping(arguments.event,local.strItem);
							local.strItem = parseItem(arguments.event);
						
							// dont break here - let it go to the next case condition						
						default:
							if (local.strItem.showShippingArea and NOT arguments.event.valueExists('aid')) {
								local.qryStates = application.objCommon.getStates();
								local.contentToShow = "showItem,confirmShipping"; 
							} else {
								local.contentToShow = "showItem,confirmPurchase"; 
							}
							break;					
					}
					doLogBuyNow('controller','case statement','activityState 3 - end'); 
					break;
				case 4:
					doLogBuyNow('controller','case statement','activityState 4 - start');
					try {
						local.strBuyStatus = local.strItem.thisCFC.buyNow_buy(arguments.event,local.strItem);
						if (local.strBuyStatus.success) {
							local.strReceipt = local.strItem.thisCFC.buyNow_receipt(arguments.event,local.strItem);
							local.contentToShow = "receipt";

							// ticket 8422363 - clear remembered payer when not logged in
							application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0);
						} else { 
							doLogBuyNow('controller','case statement','activityState 4 - end');
							application.objCommon.redirect(arguments.event.getValue('currentURL') & "&perr=#urlEncodedFormat(local.strBuyStatus.response)#");
						}
					}
					catch (any e) {
						application.objError.sendError(cfcatch=e);
						doLogBuyNow('controller','case statement','activityState 4 - end');
						application.objCommon.redirect(arguments.event.getValue('currentURL') & "&perr=#urlEncodedFormat(e.message)#");
					}
					doLogBuyNow('controller','case statement','activityState 4 - end');
					break;
				default: local.contentToShow = "ItemNotFound"; break;
			}
			
			// define page title
			if (left(local.contentToShow,12) eq "ItemNotFound") {
				local.pageTitle = "Item Not Found";
			} else if (listFindNoCase("receipt",local.contentToShow)) {
				local.pageTitle = local.strItem.receiptTitle;
			} else {
				local.pageTitle = local.strItem.buyNowPageTitle; 
			}

			local.defaultCurrencyType = "";
			if (arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1) {
				local.defaultCurrencyType = " #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#";
			}

			local.viewDirectory = arguments.event.getValue('viewDirectory', 'default');
			</cfscript>		
			
			<!--- show content --->
			<cfsavecontent variable="local.data">
				<cfinclude template="/views/buyNow/#local.viewDirectory#/dsp_buyNow.cfm">
			</cfsavecontent>
			
			<!--- record app hit --->
			<cfset application.objPlatformStats.recordAppHit(appname="buynow",appsection="")>
					
			<!--- return the app struct --->
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>
	</cffunction>

	<cffunction name="doLogBuyNow" access="private" output="no" returntype="void">
		<cfargument name="CalledInsideEvent" type="string" required="yes">
		<cfargument name="CallDescription" type="string" required="yes">
		<cfargument name="CallValue" type="string" required="yes">
		
		<cfset var local = StructNew()>
	
		<cfquery name="local.qryInsertRecord" datasource="#application.dsn.memberCentral.dsn#">
			INSERT INTO dbo.BuyNow_Log(BuyNowSessionID, memberID, CalledInsideEvent, CallDescription, CallValue)
			VALUES(	<cfqueryparam value="#session.SessionID#" cfsqltype="CF_SQL_VARCHAR">,
					nullif(<cfqueryparam value="#Session.cfcUser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">,0),
					<cfqueryparam value="#arguments.CalledInsideEvent#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#arguments.CallDescription#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#arguments.CallValue#" cfsqltype="CF_SQL_VARCHAR">)
		</cfquery>  	
	</cffunction>

	<cffunction name="parseItem" access="private" output="no" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		
		// set defaults
		local.strBuyNow = { itemkey=arguments.event.getValue('item',''), itemType='' };
		local.strBuyNowReturn = { 	phoneSupport='', itemOK=false, ItemType='', ItemFolder='', notFoundTemplate='ItemNotFound', 
									notIdentifiedTemplate='NotLoggedIn', existingAccountRequired=false, allowInvoice=false };

		// log it
		doLogBuyNow('BuyNow.cfc','parseItem','start');

		// if no length or not at least 2 elements, item is bad
		if (len(local.strBuyNow.itemKey) is 0 OR listlen(local.strBuyNow.itemKey,'-') lt 2) return local.strBuyNowReturn;
		local.strBuyNow.itemType = GetToken(local.strBuyNow.itemKey,1,'-');
		
		// set returnstruct data
		switch (local.strBuyNow.itemType) {

			// trialsmith document cart
			case "DOCCART":
				local.returnStruct = CreateObject("component","model.viewCart.viewCart").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;

			// trialsmith join app
			case "JOINTS":
			case "RENEWTS":
				local.returnStruct = CreateObject("component","model.system.platform.tsJoinTrialSmith").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;

			// invoices
			case "INV":
				local.returnStruct = CreateObject("component","model.invoices.invoices").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;

			// store cart
			case "STORE":
				local.returnStruct = CreateObject("component","model.store.shoppingCart").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;
			
			case "JOBBANK":
				local.returnStruct = CreateObject("component","model.JobBank.JobBank").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);				
				break;
				
			// referral payment
			case "REFERRAL":
				local.returnStruct = CreateObject("component","model.referrals.referrals").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;				

			// contribution
			case "CONTRIB":
				local.returnStruct = CreateObject("component","model.contributions.contributions").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;

			// trialsmith billing statement
			case "TSBILL":
				local.returnStruct = CreateObject("component","sitecomponents.MC.TS.custom.TSStatement").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;

			// expertProfiler report order
			case "EXPERT":
				local.returnStruct = CreateObject("component","sitecomponents.MC.TS.custom.ExpertProfilerReportOrder").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;	
				


			// cd-order form
			case "CDORDER":
				local.returnStruct = CreateObject("component","sitecomponents.MC.TS.custom.cd-order-form").buyNow_parseItem(arguments.event,local.strBuyNow,local.strBuyNowReturn);
				break;	

			default:
				local.returnStruct = local.strBuyNowReturn;
				break;
		}
			
		// association guest account override		
		if (application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).allowGuestAccounts is not 1) 
			local.returnStruct.existingAccountRequired = true;

		doLogBuyNow('BuyNow.cfc','parseItem','end');
		</cfscript>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doWizard" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">
		<cfset local.appStruct = "">
	
		<cfset local.wizardString = arguments.event.getTrimValue('wizard','')>
		<cfif len(local.wizardString) is 0>
			<cfset arguments.event.setValue("mc_trigger404page",1)>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cftry>
			<cfset local.decString = Decrypt(ToString(ToBinary(URLDecode(Replace(local.wizardString,"xPcmKx","%","ALL")))),'M3mberC3ntr@l^_2012@')>
			<cfset local.xmlWizard = XMLParse(local.decString)>
			<cfset local.profileCode = XMLSearch(local.xmlWizard,"string(/data/profilecode/text())")>
			<cfset local.action = XMLSearch(local.xmlWizard,"string(/data/action/text())")>
			
			<!--- Find out which gateway to use based on profile --->
			<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				DECLARE @profileCode varchar(20) = <cfqueryparam value="#local.profileCode#" cfsqltype="cf_sql_varchar">;

				SELECT pr.siteID, s.orgID, pr.profileID, pr.profileCode, ga.gatewayType, pr.gatewayUsername, pr.gatewayPassword, 
					pr.gatewayMerchantID, pr.enableProcessingFeeDonation, pr.processFeeDonationFeePercent, pfm.message AS processFeeDonationFEMsg, 
					pr.gatewayAccountID, pr.googlePayMerchantID, pr.enableSurcharge, pr.surchargePercent, pr.surchargeRevenueGLAccountID,
					ct.currencyType, pr.enableMCPay
				FROM dbo.mp_profiles AS pr
				INNER JOIN dbo.mp_gateways AS ga ON ga.gatewayid = pr.gatewayID
				INNER JOIN dbo.sites AS s ON s.siteID = pr.siteID
				INNER JOIN dbo.currencyTypes AS ct ON ct.currencyTypeID = s.defaultCurrencyTypeID
				LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.siteID = @siteID
					AND pfm.messageID = pr.solicitationMessageID
				WHERE pr.profileCode = @profileCode
				AND pr.siteID = @siteID
				AND pr.status = 'A'
				AND ga.isActive = 1;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfswitch expression="#local.qryGatewayID.gatewayType#">
				<cfcase value="AuthorizeCCCIM">
					<cfswitch expression="#local.action#">
						<cfcase value="addPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"addPaymentProfile","insertPaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerid = XMLSearch(local.xmlWizard,"string(/data/cid/text())")>
							<cfset local.customername = XMLSearch(local.xmlWizard,"string(/data/cn/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.hidecancel = XMLSearch(local.xmlWizard,"string(/data/hcnl/text())")>
							<cfset local.strPaymentFeatures = len(XMLSearch(local.xmlWizard,"string(/data/pfs/text())")) ? deserializeJSON(XMLSearch(local.xmlWizard,"string(/data/pfs/text())")) : application.objPayments.setDefaultPayFeaturesStruct()>
							<cfset local.chargeInfo = len(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) ? deserializeJSON(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) : { "amt":0, "processingfees":0 }>
							<cfset local.editMode = XMLSearch(local.xmlWizard,"string(/data/em/text())")>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerid=local.customerid, customername=local.customername, customerProfileId=local.customerprofileid, 
									qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL, hidecancel=local.hidecancel, paymentFeatures=local.strPaymentFeatures, 
									chargeInfo=local.chargeInfo, editMode=local.editMode }>
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="addPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="insertPaymentProfile">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerid = XMLSearch(local.xmlWizard,"string(/data/cid/text())")>
							<cfset local.customername = XMLSearch(local.xmlWizard,"string(/data/cn/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerid=local.customerID, customername=local.customername, customerprofileid=local.customerprofileid } >
							<cfloop list="1,2,4,6,7,11,12,13,14,15" index="local.thisFID">
								<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
							</cfloop>
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="insertPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="addPaymentProfileReturn">
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/p/text())")>
							<cfset local.showCOF = XMLSearch(local.xmlWizard,"string(/data/c/text())")>
							<cfset local.overrideCustomerID = XMLSearch(local.xmlWizard,"string(/data/ocid/text())")>
							<cfset local.hideSelect = XMLSearch(local.xmlWizard,"string(/data/h/text())")>
							<cfset local.offerDelete = XMLSearch(local.xmlWizard,"string(/data/od/text())")>
							<cfset local.usePopup = XMLSearch(local.xmlWizard,"string(/data/o/text())")>
							<cfset local.usePopupDIVName = XMLSearch(local.xmlWizard,"string(/data/d/text())")>
							<cfset local.customerID = XMLSearch(local.xmlWizard,"string(/data/cid/text())")>
							<cfset local.customerName = XMLSearch(local.xmlWizard,"string(/data/cn/text())")>
							<cfset local.editMode = XMLSearch(local.xmlWizard,"string(/data/em/text())")>
							<cfset local.strPaymentFeatures = len(XMLSearch(local.xmlWizard,"string(/data/pfs/text())")) ? deserializeJSON(XMLSearch(local.xmlWizard,"string(/data/pfs/text())")) : application.objPayments.setDefaultPayFeaturesStruct()>
							<cfset local.chargeInfo = len(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) ? deserializeJSON(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) : { "amt":0, "processingfees":0 }>
							<cfset local.tokenArgs = { siteid=arguments.event.getValue('mc_siteinfo.siteid'), customerProfileId=local.customerprofileid,
													   customerid=local.customerID, customername=local.customername, pmid=local.pmid, showCOF=local.showCOF, 
													   hideSelect=local.hideSelect, offerDelete=local.offerDelete, usePopup=local.usePopup, 
													   usePopupDIVName=local.usePopupDIVName, qryGateWayID=local.qryGateWayID, 
													   overrideCustomerID=local.overrideCustomerID, editMode=local.editMode, paymentFeatures=local.strPaymentFeatures,
													   chargeInfo=local.chargeInfo } >
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="addPaymentProfileReturn" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="editPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"editPaymentProfile","updatePaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.strPaymentFeatures = len(XMLSearch(local.xmlWizard,"string(/data/pfs/text())")) ? deserializeJSON(XMLSearch(local.xmlWizard,"string(/data/pfs/text())")) : application.objPayments.setDefaultPayFeaturesStruct()>
							<cfset local.chargeInfo = len(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) ? deserializeJSON(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) : { "amt":0, "processingfees":0 }>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerProfileId=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId, 
									qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL, paymentFeatures=local.strPaymentFeatures, chargeInfo=local.chargeInfo } >
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="editPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="updatePaymentProfile">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerprofileid=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId } >
							<cfloop list="1,2,4,6,7,11,12,13,14,15" index="local.thisFID">
								<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
							</cfloop>
							<cfif arguments.event.valueExists('fld_nickname')>
								<cfset local.tokenArgs["nickname"] = arguments.event.getTrimValue('fld_nickname')>
							</cfif>
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="updatePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="reassignPaymentProfile">
							<cfset local.formpost = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"reassignPaymentProfile","reassignPaymentProfileSave"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerProfileId=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId, formpost=local.formpost, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="reassignPaymentProfileSave">
							<cfset local.newPayProfileID = arguments.event.getTrimValue('newPayProfileID',0)>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerProfileId=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId, formpost='', newPayProfileID=local.newPayProfileID, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.AuthorizeCCCIM" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
					</cfswitch>
				</cfcase>
				<cfcase value="SageCCCIM">
					<cfswitch expression="#local.action#">
						<cfcase value="addPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"addPaymentProfile","insertPaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.hidecancel = XMLSearch(local.xmlWizard,"string(/data/hcnl/text())")>
							<cfset local.editMode = XMLSearch(local.xmlWizard,"string(/data/em/text())")>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL, hidecancel=local.hidecancel, editMode=local.editMode } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="addPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="insertPaymentProfile">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid } >
							<cfloop list="4,6,8,11,12,13,14" index="local.thisFID">
								<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
							</cfloop>
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="insertPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="addPaymentProfileReturn">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/p/text())")>
							<cfset local.showCOF = XMLSearch(local.xmlWizard,"string(/data/c/text())")>
							<cfset local.overrideCustomerID = XMLSearch(local.xmlWizard,"string(/data/ocid/text())")>
							<cfset local.hideSelect = XMLSearch(local.xmlWizard,"string(/data/h/text())")>
							<cfset local.offerDelete = XMLSearch(local.xmlWizard,"string(/data/od/text())")>
							<cfset local.usePopup = XMLSearch(local.xmlWizard,"string(/data/o/text())")>
							<cfset local.usePopupDIVName = XMLSearch(local.xmlWizard,"string(/data/d/text())")>
							<cfset local.editMode = XMLSearch(local.xmlWizard,"string(/data/em/text())")>
							<cfset local.tokenArgs = { siteid=arguments.event.getValue('mc_siteinfo.siteid'), pmid=local.pmid, showCOF=local.showCOF, 
													   hideSelect=local.hideSelect, offerDelete=local.offerDelete, usePopup=local.usePopup, 
													   usePopupDIVName=local.usePopupDIVName, qryGateWayID=local.qryGateWayID, overrideCustomerID=local.overrideCustomerID, editMode=local.editMode } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="addPaymentProfileReturn" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="editPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"editPaymentProfile","updatePaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId, qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="editPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="updatePaymentProfile">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId } >
							<cfloop list="4,6,8,11,12,13,14" index="local.thisFID">
								<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
							</cfloop>
							<cfif arguments.event.valueExists('fld_nickname')>
								<cfset local.tokenArgs["nickname"] = arguments.event.getTrimValue('fld_nickname')>
							</cfif>
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="updatePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="reassignPaymentProfile">
							<cfset local.formpost = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"reassignPaymentProfile","reassignPaymentProfileSave"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerProfileId=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId, formpost=local.formpost, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="reassignPaymentProfileSave">
							<cfset local.newPayProfileID = arguments.event.getTrimValue('newPayProfileID',0)>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerProfileId=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId, formpost='', newPayProfileID=local.newPayProfileID, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>

						<cfcase value="removePaymentProfile">
							<cfset local.formpost = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"removePaymentProfile","removePaymentProfileSave"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId, formpost=local.formpost } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="removePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="removePaymentProfileSave">
							<cfset local.doRemCard = arguments.event.getTrimValue('doRemCard',0)>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId, formpost='', doRemCard=local.doRemCard } >
							<cfinvoke component="model.system.platform.gateways.SageCCCIM" method="removePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
					</cfswitch>
				</cfcase>
				<cfcase value="BankDraft,MCPayEcheck">
					<!--- common vars --->
					<cfif listFindNoCase("addPaymentProfileReturn,editPaymentProfile,updatePaymentProfile,reassignPaymentProfile,reassignPaymentProfileSave",local.action)>
						<cfset local.routingNumber = XMLSearch(local.xmlWizard,"string(/data/rtn/text())")>
						<cfset local.accountNumber = XMLSearch(local.xmlWizard,"string(/data/acn/text())")>
						<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
					</cfif>

					<cfswitch expression="#local.action#">
						<cfcase value="addPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"addPaymentProfile","insertPaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.hidecancel = XMLSearch(local.xmlWizard,"string(/data/hcnl/text())")>
							<cfset local.adminForm = XMLSearch(local.xmlWizard,"string(/data/adm/text())")>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL, hidecancel=local.hidecancel, adminForm=local.adminForm } >
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="addPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="insertPaymentProfile">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid } >
							<cfloop list="1,2,9,10,11,12,13,14,27" index="local.thisFID">
								<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
							</cfloop>
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="insertPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="addPaymentProfileReturn">
							<cfset local.showCOF = XMLSearch(local.xmlWizard,"string(/data/c/text())")>
							<cfset local.overrideCustomerID = XMLSearch(local.xmlWizard,"string(/data/ocid/text())")>
							<cfset local.hideSelect = XMLSearch(local.xmlWizard,"string(/data/h/text())")>
							<cfset local.adminForm = XMLSearch(local.xmlWizard,"string(/data/adm/text())")>
							<cfset local.offerDelete = XMLSearch(local.xmlWizard,"string(/data/od/text())")>
							<cfset local.usePopup = XMLSearch(local.xmlWizard,"string(/data/o/text())")>
							<cfset local.usePopupDIVName = XMLSearch(local.xmlWizard,"string(/data/d/text())")>
							<cfset local.editMode = XMLSearch(local.xmlWizard,"string(/data/em/text())")>
							<cfset local.tokenArgs = { siteid=arguments.event.getValue('mc_siteinfo.siteid'), routingNumber=local.routingNumber,
													   accountNumber=local.accountNumber, pmid=local.pmid, showCOF=local.showCOF, 
													   hideSelect=local.hideSelect, offerDelete=local.offerDelete, usePopup=local.usePopup, 
													   usePopupDIVName=local.usePopupDIVName, qryGateWayID=local.qryGateWayID, adminForm=local.adminForm, overrideCustomerID=local.overrideCustomerID, editMode=local.editMode } >
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="addPaymentProfileReturn" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="editPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"editPaymentProfile","updatePaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.payProfileid = XMLSearch(local.xmlWizard,"string(/data/ppid/text())")>
							<cfset local.adminForm = XMLSearch(local.xmlWizard,"string(/data/adm/text())")>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, payProfileID=local.payProfileid, routingNumber=local.routingNumber, accountNumber=local.accountNumber, qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL, adminForm=local.adminForm } >
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="editPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="updatePaymentProfile">
							<cfset local.payProfileid = XMLSearch(local.xmlWizard,"string(/data/ppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, payProfileID=local.payProfileid, routingNumber=local.routingNumber, accountNumber=local.accountNumber } >
							<cfloop list="1,2,9,10,11,12,13,14,27" index="local.thisFID">
								<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
							</cfloop>
							<cfif arguments.event.valueExists('fld_nickname')>
								<cfset local.tokenArgs["nickname"] = arguments.event.getTrimValue('fld_nickname')>
							</cfif>
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="updatePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="reassignPaymentProfile">
							<cfset local.formpost = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"reassignPaymentProfile","reassignPaymentProfileSave"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, routingNumber=local.routingNumber, accountNumber=local.accountNumber, formpost=local.formpost, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="reassignPaymentProfileSave">
							<cfset local.newPayProfileID = arguments.event.getTrimValue('newPayProfileID',0)>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, routingNumber=local.routingNumber, accountNumber=local.accountNumber, formpost='', newPayProfileID=local.newPayProfileID, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.BankDraft" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
					</cfswitch>
				</cfcase>
				<cfcase value="AffiniPayCC">
					<cfswitch expression="#local.action#">
						<cfcase value="addPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"addPaymentProfile","insertPaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.hidecancel = XMLSearch(local.xmlWizard,"string(/data/hcnl/text())")>
							<cfset local.editMode = XMLSearch(local.xmlWizard,"string(/data/em/text())")>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL, hidecancel=local.hidecancel, editMode=local.editMode }>
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="addPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">

							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="insertPaymentProfile">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, token_id=arguments.event.getTrimValue('token_id','') } >
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="insertPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
							<cfset local.data = serializeJSON(local.appStruct)>
						</cfcase>
						<cfcase value="addPaymentProfileReturn">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/p/text())")>
							<cfset local.showCOF = XMLSearch(local.xmlWizard,"string(/data/c/text())")>
							<cfset local.hideSelect = XMLSearch(local.xmlWizard,"string(/data/h/text())")>
							<cfset local.offerDelete = XMLSearch(local.xmlWizard,"string(/data/od/text())")>
							<cfset local.usePopup = XMLSearch(local.xmlWizard,"string(/data/o/text())")>
							<cfset local.usePopupDIVName = XMLSearch(local.xmlWizard,"string(/data/d/text())")>
							<cfset local.editMode = XMLSearch(local.xmlWizard,"string(/data/em/text())")>
							<cfset local.tokenArgs = { siteid=arguments.event.getValue('mc_siteinfo.siteid'), pmid=local.pmid, showCOF=local.showCOF, 
													   hideSelect=local.hideSelect, offerDelete=local.offerDelete, usePopup=local.usePopup, 
													   usePopupDIVName=local.usePopupDIVName, qryGateWayID=local.qryGateWayID, editMode=local.editMode } >
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="addPaymentProfileReturn" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
			
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>
						</cfcase>
						<cfcase value="editPaymentProfile">
							<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"editPaymentProfile","updatePaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryGateWayID.profilecode)>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId, qryGatewayProfileFields=local.qryGatewayProfileFields, EncSaveCardURL=local.EncSaveCardURL } >
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="editPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="updatePaymentProfile">
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId, token_id=arguments.event.getTrimValue('token_id','') } >
							<cfif arguments.event.valueExists('nickname')>
								<cfset local.tokenArgs["nickname"] = arguments.event.getTrimValue('nickname')>
							</cfif>
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="updatePaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
							<cfset local.data = serializeJSON(local.appStruct)>
						</cfcase>
						<cfcase value="reassignPaymentProfile">
							<cfset local.formpost = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"reassignPaymentProfile","reassignPaymentProfileSave"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId, formpost=local.formpost, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="reassignPaymentProfileSave">
							<cfset local.newPayProfileID = arguments.event.getTrimValue('newPayProfileID',0)>
							<cfset local.pmid = XMLSearch(local.xmlWizard,"string(/data/pmid/text())")>
							<cfset local.includeOpenInvoices = XMLSearch(local.xmlWizard,"string(/data/ioi/text())")>
							<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
							<cfset local.tokenArgs = { qryGateWayID=local.qryGateWayID, pmid=local.pmid, customerPaymentProfileId=local.customerPaymentProfileId, formpost='', newPayProfileID=local.newPayProfileID, includeOpenInvoices=local.includeOpenInvoices } >
							<cfinvoke component="model.system.platform.gateways.AffiniPayCC" method="reassignPaymentProfile" returnvariable="local.appStruct" argumentcollection="#local.tokenArgs#">
						
							<cfif len(local.appStruct.head)>
								<cfhtmlhead text="#application.objCommon.minText(local.appStruct.head)#">
							</cfif>
							<cfsavecontent variable="local.data">
								<cfoutput>#local.appStruct.html#</cfoutput>
							</cfsavecontent>
						</cfcase>
					</cfswitch>
				</cfcase>
			</cfswitch>
			
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfsavecontent variable="local.data">
				<cfoutput>Error.</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doWizardTS" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">
		<cfset local.appStruct = "">

		<cftry>
			<cfset local.decString = Decrypt(ToString(ToBinary(URLDecode(Replace(arguments.event.getTrimValue('wizardTS',''),"xPcmKx","%","ALL")))),'M3mberC3ntr@l^_2012@')>
			<cfset local.xmlWizard = XMLParse(local.decString)>

			<cfset local.merchantorgcode = XMLSearch(local.xmlWizard,"string(/data/mo/text())")>
			<cfset local.action = XMLSearch(local.xmlWizard,"string(/data/action/text())")>

			<!--- Find out which gateway to use based on profile --->
			<cfquery name="local.qryGetCIMInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				select authUsername, authTransKey
				from dbo.depoTLA 
				where [state] = <cfqueryparam value="#local.merchantorgcode#" cfsqltype="CF_SQL_VARCHAR">
				and authCIM = 1
			</cfquery>

			<cfif local.qryGetCIMInfo.recordcount is 1 and local.action eq "addPaymentProfile">
			
				<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"addPaymentProfile","insertPaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
				<cfset local.customerid = XMLSearch(local.xmlWizard,"string(/data/cid/text())")>
				<cfset local.appStruct = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").addPaymentProfile(merchantorgcode=local.merchantorgcode, customerID=local.customerid, EncSaveCardURL=local.EncSaveCardURL)>
				
				<cfif len(local.appStruct.head)>
					<cfhtmlhead text="#local.appStruct.head#">
				</cfif>
				<cfsavecontent variable="local.data">
					<cfoutput>#local.appStruct.html#</cfoutput>
				</cfsavecontent>
				
			<cfelseif local.qryGetCIMInfo.recordcount is 1 and local.action eq "insertPaymentProfile">
				
				<cfset local.customerid = XMLSearch(local.xmlWizard,"string(/data/cid/text())")>
				<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
				<cfset local.tokenArgs = structNew()>
				<cfloop list="1,2,4,6,7,11,12,13,14,15" index="local.thisFID">
					<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
				</cfloop>
				<cfset local.appStruct = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").insertPaymentProfile(merchantorgcode=local.merchantorgcode, customerID=local.customerid, customerprofileid=local.customerprofileid, tokenArgs=local.tokenArgs )>
			
				<cfif len(local.appStruct.head)>
					<cfhtmlhead text="#local.appStruct.head#">
				</cfif>
				<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>

			<cfelseif local.qryGetCIMInfo.recordcount is 1 and local.action eq "addPaymentProfileReturn">

				<!--- post processing --->
				<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
				<cfset local.customerID = XMLSearch(local.xmlWizard,"string(/data/cid/text())")>
				<cfset local.chargeInfo = len(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) ? deserializeJSON(XMLSearch(local.xmlWizard,"string(/data/cinf/text())")) : {}>
				<cfset local.appStruct = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").addPaymentProfileReturn(merchantorgcode=local.merchantorgcode, customerProfileId=local.customerprofileid, customerid=local.customerID, chargeInfo=local.chargeInfo)>

				<cfif len(local.appStruct.head)>
					<cfhtmlhead text="#local.appStruct.head#">
				</cfif>
				<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>

			<cfelseif local.qryGetCIMInfo.recordcount is 1 and local.action eq "editPaymentProfile">
			
				<!--- get edit payment profile code --->
				<cfset local.EncSaveCardURL = Replace(URLEncodedFormat(ToBase64(Encrypt(replaceNoCase(local.decString,"editPaymentProfile","updatePaymentProfile"),'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
				<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
				<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
				<cfset local.appStruct = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").editPaymentProfile(merchantorgcode=local.merchantorgcode, customerProfileId=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId, EncSaveCardURL=local.EncSaveCardURL)>
				
				<cfif len(local.appStruct.head)>
					<cfhtmlhead text="#local.appStruct.head#">
				</cfif>
				<cfsavecontent variable="local.data">
					<cfoutput>#local.appStruct.html#</cfoutput>
				</cfsavecontent>

			<cfelseif local.qryGetCIMInfo.recordcount is 1 and local.action eq "updatePaymentProfile">
				
				<cfset local.customerprofileid = XMLSearch(local.xmlWizard,"string(/data/cpid/text())")>
				<cfset local.customerPaymentProfileId = XMLSearch(local.xmlWizard,"string(/data/cppid/text())")>
				<cfset local.tokenArgs = structNew()>
				<cfloop list="1,2,4,6,7,11,12,13,14,15" index="local.thisFID">
					<cfset local.tokenArgs["fld_#local.thisFID#_"] = trim(arguments.event.getValue('fld_' & local.thisFID & '_',''))>
				</cfloop>
				<cfset local.appStruct = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").updatePaymentProfile(merchantorgcode=local.merchantorgcode, customerprofileid=local.customerprofileid, customerPaymentProfileId=local.customerPaymentProfileId, tokenArgs=local.tokenArgs )>
			
				<cfif len(local.appStruct.head)>
					<cfhtmlhead text="#local.appStruct.head#">
				</cfif>
				<cfsavecontent variable="local.data"><!-- blank --></cfsavecontent>

			</cfif>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfsavecontent variable="local.data">
				<cfoutput>Error.</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="showPolicy" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">
		<cfset local.eCommercePolicyLink = "">
	
		<cfset local.viewPolicy = isSimpleValue(arguments.event.getValue('viewPolicy','')) ? arguments.event.getTrimValue('viewPolicy','') : ''>
		<cfif NOT len(local.viewPolicy)>
			<cfset arguments.event.setValue("mc_trigger404page",1)>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cfswitch expression="#local.viewPolicy#">
			<cfcase value="secureCheckout">
				<cfset arguments.event.getCollection()['mc_pageDefinition']['pagetitle'] = "Secure Checkout Policy">
				<cfset local.data = createObject("component","model.admin.custom.mc.mc.PlatformSettings").getPlatformSettingsContent(contentTitle='Platformwide_Secure_Checkout_Policy').rawContent>
			</cfcase>
			<cfcase value="refund">
				<cfset local.eCommercePolicyLink = arguments.event.getValue('mc_siteInfo.rrPolicyURL')>
			</cfcase>
			<cfcase value="delivery">
				<cfset local.eCommercePolicyLink = arguments.event.getValue('mc_siteInfo.deliveryPolicyURL')>
			</cfcase>
			<cfcase value="privacy">
				<cfset local.eCommercePolicyLink = arguments.event.getValue('mc_siteInfo.privacyPolicyURL')>
			</cfcase>
			<cfcase value="terms">
				<cfset local.eCommercePolicyLink = arguments.event.getValue('mc_siteInfo.tcURL')>
			</cfcase>
		</cfswitch>

		<!--- invalid viewPolicy --->
		<cfif NOT len(local.data) AND NOT len(local.eCommercePolicyLink)>
			<cfset arguments.event.setValue("mc_trigger404page",1)>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<!--- redirect --->
		<cfif len(local.eCommercePolicyLink)>
			<cflocation url="#local.eCommercePolicyLink#" addtoken="false">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="chargeCC_TS" access="public" returntype="struct" output="no">
		<cfargument name="customerid" type="string" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="chargeDesc" type="string" required="yes">
		<cfargument name="merchantProfile" type="string" required="yes">
		<cfargument name="TransactionDepoMemberDataID" type="numeric" required="yes">
		<cfargument name="tokenData" type="struct" required="no" hint="used for applepay and googlepay">
	
		<cfset var local = structnew()>
		<cfset local.returnStruct.ccsuccess = false>
		<cfset local.returnStruct.ccresponse = "">
	
		<cfquery name="local.qryCIMInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select authUsername, authTransKey
			from dbo.depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantProfile#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>
		<cfquery name="local.qryCard" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select payProfileID
			from dbo.ccMemberPaymentProfiles 
			where customerid = <cfqueryparam value="#arguments.customerid#" cfsqltype="CF_SQL_VARCHAR">
			and orgcode = <cfqueryparam value="#arguments.merchantProfile#" cfsqltype="CF_SQL_VARCHAR">
			and declined = 0
		</cfquery>

		<cfif local.qryCIMInfo.recordcount is 1 and (local.qryCard.recordcount is 1 or arguments.keyExists("tokenData"))>
			<cfset local.strArgs = { "CIMUsername"=local.qryCIMInfo.authUsername, "CIMPassword"=local.qryCIMInfo.authTransKey, 
				"payProfileID"=0, "amount"=arguments.amount, "detail"=arguments.chargeDesc, "TransactionDepoMemberDataID"=arguments.TransactionDepoMemberDataID }>
			<cfif arguments.keyExists("tokenData")>
				<cfset local.strArgs['tokenData'] = arguments.tokenData>
			<cfelse>
				<cfset local.strArgs['payProfileID'] = local.qryCard.payProfileID>
			</cfif>
			<cfset local.strResponse = createObject("component","model.system.platform.gateways.tsChargeCardCIM").chargeCard(argumentcollection=local.strArgs)>
			<cfset local.returnStruct.payprofileid = local.qryCard.payProfileID>
			<cfif structKeyExists(local.strResponse,"strInsertTrans")>
				<cfset local.returnStruct.strInsertTrans = local.strResponse.strInsertTrans>
			</cfif>
			<cfif structKeyExists(local.strResponse,"accountNumber")>
				<cfset local.returnStruct.accountNumber = local.strResponse.accountNumber>
			</cfif>
			<cfif structKeyExists(local.strResponse,"depoTransactionID")>
				<cfset local.returnStruct.depoTransactionID = local.strResponse.depoTransactionID>
			</cfif>
			<cfif structKeyExists(local.strResponse,"transactionDetail")>
				<cfset local.returnStruct.transactionDetail = local.strResponse.transactionDetail>
			</cfif>
			<cfif local.strResponse.responseCode is 1>
				<cfset local.returnStruct.ccsuccess = true>
			<cfelse>
				<cfset local.returnStruct.ccsuccess = false>
				<cfset local.returnStruct.ccresponse = local.strResponse.publicResponseReasonText>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.ccresponse = "Payment information not found.">
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="validateCouponCode" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="couponCode" type="string" required="true">
		
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { "success"=false, "isvalidcoupon"=false, "couponresponse"="Invalid Promo Code", "totalamt"=0, "discount"=0, "discountappliedtotal"=0 };

		arguments.itemType = trim(arguments.itemType);
		arguments.couponCode = trim(arguments.couponCode);
		
		// if no length 
		if (len(arguments.itemType) is 0 OR len(arguments.couponCode) is 0) return local.returnStruct;
		
		// verify application type has cart item(s) and qualified for coupon
		switch (arguments.itemType) {
			
			// store cart
			case "STORE":
				local.objStoreReg = CreateObject("component","model.store.storeReg");
				local.strItem = local.objStoreReg.cartQualifiedForCoupon();
				break;
			
			default:
				local.strItem = { applicationType = '', isQualified = false, memberIDList='', rateIDList = '' };
				break;
		}

		// unqualified
		if (NOT local.strItem.isQualified) return local.returnStruct;

		var sqlParams = {
			siteID = { value=arguments.mcproxy_siteID, cfsqltype="CF_SQL_INTEGER" },
			applicationType = { value=local.strItem.applicationType, cfsqltype="CF_SQL_VARCHAR" },
			cartItemsXML = { value=local.strItem.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
			couponCode = { value=arguments.couponCode, cfsqltype="CF_SQL_VARCHAR" }
		};

		// using query to exec proc here due to xml output and luceeV5.2 not supporting xml queryparam
		var qryValidCoupon = queryExecute("
			SET NOCOUNT ON;
			
			DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
				@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml;

			EXEC dbo.tr_isValidCouponCode @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
				@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
				@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT;

			SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML;
			", 
			sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
		);

		local.couponID = val(qryValidCoupon.couponID);
		local.returnStruct.couponResponse = qryValidCoupon.couponMessage;
		local.qualifiedCartItemsXML = qryValidCoupon.qualifiedCartItemsXML;

		// valid coupon
		if (local.couponID gt 0) {
			local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.mcproxy_siteID, couponID=local.couponID);
			local.returnStruct.isValidCoupon = true;

			switch (arguments.itemType) {
				// store cart
				case "STORE":
					local.strPrice = local.objStoreReg.applyCouponToStoreCart(qryCoupon=local.qryCoupon, qualifiedCartItemsXML=local.qualifiedCartItemsXML);
					break;
			}

			local.returnStruct.totalamt = NumberFormat(local.strPrice.totalAmount,"9.99");
			local.returnStruct.discount = NumberFormat(local.strPrice.discount,"9.99");
			local.returnStruct.discountappliedtotal = NumberFormat(local.strPrice.discountAppliedTotal,"9.99");
		}

		local.returnStruct.success = true;
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="removeAppliedCoupon" access="public" output="false" returntype="struct">
		<cfargument name="itemType" type="string" required="true">
		
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { "success" = false };
		
		// remove applied coupon
		switch (trim(arguments.itemType)) {
			
			// store cart
			case "STORE":
				CreateObject("component","model.store.storeReg").removeAppliedCouponFromStoreCart();
				break;
		}

		local.returnStruct.success = true;
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

</cfcomponent>