<cfsavecontent variable="local.invJS">
	<style type="text/css">
	.alert { background:#fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid #f00; border-bottom:2px solid #f00; } 
	</style>
	<script language='javascript' type='text/javascript' src='/assets/common/javascript/common.js'></script>
	<script language="javascript">
		function cleanVN(vn) {
			return vn.replace(/[^\d]/g,'');
		}
		function hideAlert() { $('#verr').html('').hide(); };
		function showAlert(msg) { $('#verr').html(msg).attr('class','alert').show(); };

		function validateForm() {
			hideAlert();
			var arrReq = new Array();

			$('#vc').val($('#vc').val().toUpperCase());
			if (trim($('#vn').val()).length == 0) arrReq[arrReq.length] = 'Invoice Number is required.';
			if (trim($('#vc').val()).length == 0) arrReq[arrReq.length] = 'Invoice Code is required.';

			if (arrReq.length > 0) {
				var msg = '';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				return false;
			}
			return true;
		}
		$(function() {
			<cfif arguments.event.getValue('ve',0) is 1>
				showAlert('We could not locate that invoice based on the information provided. Try again.');
			</cfif>
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.invJS)#">

<cfform method="POST" action="#local.formlink#" name="frmInv"  id="frmInv" onsubmit="return validateForm()">

<div class="tsAppBodyText" style="background-color:#eee;padding:6px;margin-bottom:10px;"><b>Enter the following information from the invoice you'd like to pay.</b></div>

<table class="tsAppBodyText">
<tr>
	<td><b>Invoice Number:</b></td>
	<td width="5" rowspan="3"></td>
	<td align="right"><cfoutput>#arguments.event.getValue('mc_siteInfo.orgcode')#</cfoutput></td>
	<td><cfinput type="text" name="vn"  id="vn" class="tsAppBodyText" size="12" autocomplete="off" value="" onBlur="this.value=cleanVN(this.value);">
		&nbsp; <i>(Leading zeros may be omitted.)</i>
	</td>
</tr>
<tr>
	<td><b>Invoice Code:</b></td>
	<td></td>
	<td><cfinput type="text" name="vc"  id="vc" class="tsAppBodyText" size="12" autocomplete="off" value="" maxlength="8"></td>
</tr>
<tr>
	<td colspan="3"></td>
	<td><button type="submit" class="tsAppBodyButton">Lookup Invoice</button></td>
</tr>
</table>

<div id="verr" style="display:none;margin:10px 0 0 0;"></div>

</cfform>
