<cfcomponent>

	<cffunction name="setDefaultPayFeaturesStruct" access="public" returntype="struct" output="no">
		<cfargument name="usePaymentFeatures" type="struct" required="false" default="#structNew()#">

		<cfset var local = structNew()>

		<cfset local.strPaymentFeatures = {
			"applePay": { 
				"enable": 0 
			},
			"googlePay": { 
				"enable": 0 
			},
			"processingFee": { 
				"enable": 0, "select": 0, "label": "", "denylabel": "", "title":"", "msg": "" 
			},
			"surcharge": {
				"enable": 0, 
				"msg": getDefaultSurchargeMsg(orgName=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgName)
			}
		}>

		<cfif isDefined("arguments.usePaymentFeatures.applePay.enable")>
			<cfset local.strPaymentFeatures.applePay.enable = arguments.usePaymentFeatures.applePay.enable>
		</cfif>
		<cfif isDefined("arguments.usePaymentFeatures.googlePay.enable")>
			<cfset local.strPaymentFeatures.googlePay.enable = arguments.usePaymentFeatures.googlePay.enable>
		</cfif>
		<cfif arguments.usePaymentFeatures.keyExists("processingFee")>
			<cfset local.strPaymentFeatures.processingFee = { 
				"enable":arguments.usePaymentFeatures.processingFee.enable ?: 0, 
				"select":arguments.usePaymentFeatures.processingFee.select ?: 0, 
				"label":arguments.usePaymentFeatures.processingFee.label ?: '', 
				"denylabel":arguments.usePaymentFeatures.processingFee.denylabel ?: '', 
				"title":arguments.usePaymentFeatures.processingFee.title ?: '', 
				"msg":arguments.usePaymentFeatures.processingFee.msg ?: '' 
			}>
		</cfif>
		<cfif arguments.usePaymentFeatures.keyExists("surcharge")>
			<cfset local.strPaymentFeatures.surcharge.enable = arguments.usePaymentFeatures.surcharge.enable>
			<cfset local.strPaymentFeatures.surcharge.msg = arguments.usePaymentFeatures.surcharge.msg>
		</cfif>

		<cfreturn local.strPaymentFeatures>
	</cffunction>

	<cffunction name="getDefaultSurchargeMsg" access="public" output="false" returntype="string">
		<cfargument name="orgName" type="string" required="true">
		<cfreturn "#EncodeForHTML(arguments.orgName)# imposes a surcharge of {{PERCENT}} on all credit cards, which is not greater than our actual cost of acceptance. We do not surcharge Debit Cards, ACH, ECheck, or mailed-in check payments.">
	</cffunction>

	<cffunction name="chargeAdHoc" access="public" returntype="struct" output="no" hint="Used to process payments on an ad-hoc basis">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="assignedToMemberID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes" hint="applepay will pass in 0">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="overrideAcceptPending" type="boolean" required="no" default="0" hint="Used by transactionAdmin savePayment to override pending payments">
		<cfargument name="overrideTransactionDate" type="date" required="no" default="#now()#" hint="Used by transactionAdmin savePayment to override transaction date">
		<cfargument name="overrideBatchID" type="numeric" required="no" default="0" hint="Used by transactionAdmin savePayment to override batch ID">
		<cfargument name="overrideCreatedByMemberID" type="numeric" required="no" default="0" hint="Used by transactionAdmin savePayment to override batch createdbymemberID">
		<cfargument name="offeredPaymentFee" type="boolean" required="no" default="0" hint="offered processing fees while making payment">
		<cfargument name="tokenData" type="struct" required="no" hint="used for applepay and googlepay">

		<cfset var local = structNew()>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
	
		<!--- process payment --->
		<cfinvoke method="processPaymentByGateway" argumentcollection="#arguments#" returnvariable="local.result">
		
		<!--- record accounting if successful --->
		<cfset local.result.mc_batchID = 0>
		<cfset local.result.mc_transactionID = 0>
		<cfif local.result.responseCode is 1 and local.result.GLAccountID gt 0>
			<cftry>

				<!--- handle acceptPending --->
				<cfif arguments.overrideAcceptPending is 1 and local.result.status eq "Pending">
					<cfset local.result.status = "Active">
				</cfif>

				<!--- get batch handling override --->
				<cfif arguments.overrideBatchID gt 0 and local.result.status neq "Pending">
					<cfset local.result.mc_batchID = arguments.overrideBatchID>
				<cfelse>
					<cfset local.strTemp = { siteID=arguments.siteid, profileCode=arguments.profileCode, GLAccountID=local.result.GLAccountID, 
											 responseReasonCode=local.result.responseReasonCode, batchDate=arguments.overrideTransactionDate, 
											 paystatus=local.result.status, createdByMemberID=arguments.overrideCreatedByMemberID,
											 paymentMethod=local.result.paymentMethod ?: '', externalBatchID=local.result.externalBatchID ?: '',
											 mode="charge" }>
					<cfset local.result.mc_batchID = local.objAccounting.getBatchID(argumentcollection=local.strTemp)>
				</cfif>
				<cfif local.result.mc_batchID is 0>
					<cfthrow message="The batch could not be identified.">
				</cfif>

				<!--- record payment --->
				<cfset local.strTemp = { ownedByOrgID=arguments.orgID, recordedOnSiteID=arguments.siteid, assignedToMemberID=arguments.assignedToMemberID, 
										 recordedByMemberID=arguments.recordedByMemberID, statsSessionID=arguments.statsSessionID, status=local.result.status, 
										 detail=local.result.transactionDetail, amount=local.result.x_amount, transactionDate=arguments.overrideTransactionDate, 
										 debitGLAccountID=local.result.GLAccountID, profileCode=arguments.profileCode, historyID=local.result.historyID,
										 batchID=local.result.mc_batchID, offeredPaymentFee=arguments.offeredPaymentFee }>
				<cfif isDefined("arguments.tokenData.mctokensource") and arguments.tokenData.mctokensource eq "applePay">
					<cfset local.strTemp.isApplePay = 1>
				<cfelseif isDefined("arguments.tokenData.mctokensource") and arguments.tokenData.mctokensource eq "googlePay">
					<cfset local.strTemp.isGooglePay = 1>
				</cfif>
				<cfset local.strPayment = local.objAccounting.recordPayment(argumentcollection=local.strTemp)>
				<cfif local.strPayment.rc is not 0 or local.strPayment.transactionID is 0>
					<cfthrow message="The payment could not be recorded.">
				</cfif>
				<cfset local.result.mc_transactionID = local.strPayment.transactionID>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.result.responseCode = 3>
				<cfset local.result.responseReasonText = "The payment was successful, but it could not be recorded properly. Contact your association for assistance.">
				<cfset local.result.publicResponseReasonText = local.result.responseReasonText>
			</cfcatch>
			</cftry>
		</cfif>
	
		<cfreturn local.result>
	</cffunction>

	<cffunction name="refundAdHoc" access="public" returntype="struct" output="no" hint="Used to process refunds on an ad-hoc basis">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="assignedToMemberID" type="numeric" required="yes">
		<cfargument name="recordedByMemberID" type="numeric" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="overrideTransactionDate" type="date" required="no" default="#now()#" hint="Used by transactionAdmin saveRefund to override transaction date">
		<cfargument name="overrideBatchID" type="numeric" required="no" default="0" hint="Used by transactionAdmin saveRefund to override batch ID">
		<cfargument name="overrideCreatedByMemberID" type="numeric" required="no" default="0" hint="Used by transactionAdmin saveRefund to override batch createdbymemberID">
	
		<cfset var local = structNew()>
		<cfset local.result = { errCode='', errMsg='', mc_refundTransactionID=0, mc_paymentTransactionID=0, mc_batchID=0, arrResult=[] }>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>

		<cfset local.strArgsCopy = duplicate(arguments)>

		<!--- Find out which gateway to use based on profile id --->
		<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT pr.siteID, s.orgID, pr.profileid, pr.gatewayID, pr.gatewayUsername, pr.gatewayPassword, pr.GLAccountID, 
				pr.gatewayMerchantId, ga.gatewayType, pr.allowRefunds, pr.transactionLabel, ct.currencyType
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			INNER JOIN dbo.sites AS s ON s.siteID = pr.siteID
			INNER JOIN dbo.currencyTypes AS ct ON ct.currencyTypeID = s.defaultCurrencyTypeID
			WHERE pr.profileCode = <cfqueryparam value='#arguments.profileCode#' cfsqltype="CF_SQL_VARCHAR">
			AND pr.siteID = <cfqueryparam value='#arguments.siteID#' cfsqltype="CF_SQL_INTEGER">
			AND pr.status = 'A'
			AND ga.isActive = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strArgsCopy["qryGateWayID"] = local.qryGateWayID>

		<cfinvoke method="processRefundByGateway" argumentcollection="#local.strArgsCopy#" returnvariable="local.result.arrResult">

		<cfset local.numActions = arrayLen(local.result.arrResult)>

		<cftry>
			<!--- get batch to put refund on --->
			<cfif (local.numActions is 1 AND local.result.arrResult[1].responseCode is 1 and local.result.arrResult[1].refundType eq "refund")
				OR 
				(local.numActions is 2 AND local.result.arrResult[1].responseCode is 1 and local.result.arrResult[1].refundType eq "void" and (local.result.arrResult[2].refundType eq "payment" OR local.result.arrResult[2].refundType eq "refund"))
				OR 
				(local.numActions is 2 AND local.result.arrResult[1].responseCode is not 1 and local.result.arrResult[1].refundType eq "void" and local.result.arrResult[2].refundType eq "refund")
				>
				<!--- Override BatchID --->
				<cfif arguments.overrideBatchID gt 0>
					<cfset local.result.mc_batchID = arguments.overrideBatchID>
				<cfelse>
					<cfset local.strTemp = { siteID=arguments.siteid, profileCode=arguments.profileCode, GLAccountID=local.result.arrResult[1].GLAccountID, 
											 responseReasonCode=local.result.arrResult[1].responseReasonCode, batchDate=arguments.overrideTransactionDate, 
											 paystatus='Active', createdByMemberID=arguments.overrideCreatedByMemberID, mode="refund" }>
					<cfset local.result.mc_batchID = local.objAccounting.getBatchID(argumentcollection=local.strTemp)>
				</cfif>
				<cfif local.result.mc_batchID is 0>
					<cfset local.result.errCode = "RAHCB">
					<cfset local.result.errMsg = "The refund was successful, but it could not be recorded properly. P-RAHCB">
					<cfreturn local.result>
				</cfif>
			</cfif>

			<!--- 1 action, failure --->
			<cfif local.numActions is 1 and local.result.arrResult[1].responseCode is not 1>
				<cfset local.result.errCode = "RAHFAIL">
				<cfset local.result.errMsg = "The refund was not successful. P-RAHFAIL. #local.result.arrResult[1].publicResponseReasonText#">
				<cfreturn local.result>

			<!--- 1 action, success (either refund or void) --->
			<cfelseif local.numActions is 1>
				<cfswitch expression="#local.result.arrResult[1].refundType#">
					<cfcase value="refund">
						<cfset local.strTemp = { ownedByOrgID=arguments.orgID, recordedOnSiteID=arguments.siteid, recordedByMemberID=arguments.recordedByMemberID, 
												 statsSessionID=arguments.statsSessionID, status=local.result.arrResult[1].status, 
												 detail=local.result.arrResult[1].transactionDetail, amount=local.result.arrResult[1].refundAmt, 
												 transactionDate=arguments.overrideTransactionDate, creditGLAccountID=local.result.arrResult[1].GLAccountID, 
												 profileCode=arguments.profileCode, historyID=val(local.result.arrResult[1].historyID), 
												 batchID=local.result.mc_batchID, paymentTransactionID=arguments.paymentTransactionID }>
						<cfset local.strRefund = local.objAccounting.recordRefund(argumentcollection=local.strTemp)>
						<cfif local.strRefund.rc is not 0 or local.strRefund.transactionID is 0>
							<cfset local.result.errCode = "RAHRFNM1">
							<cfset local.result.errMsg = "The refund was successful, but it could not be recorded properly. P-RAHRFNM1">
							<cfreturn local.result>
						</cfif>
						<cfset local.result.mc_refundTransactionID = local.strRefund.transactionID>
					</cfcase>
					<cfcase value="void">
						<cfset local.strTemp = { recordedOnSiteID=arguments.siteid, recordedByMemberID=arguments.recordedByMemberID, 
												 statsSessionID=arguments.statsSessionID, transactionID=arguments.paymentTransactionID }>
						<cfset local.strVoid = local.objAccounting.recordVoid(argumentcollection=local.strTemp)>
						<cfif local.strVoid.rc is not 0>
							<cfset local.result.errCode = "RAHVDNM1">
							<cfset local.result.errMsg = "The refund was successful, but it could not be recorded properly. P-RAHVDNM1">
							<cfreturn local.result>
						</cfif>
					</cfcase>
				</cfswitch>

			<!--- 2 actions, both failed --->
			<cfelseif local.numActions is 2 and local.result.arrResult[1].responseCode is not 1 and local.result.arrResult[2].responseCode is not 1>
				<cfset local.result.errCode = "RAHTWOFAIL">
				<cfset local.result.errMsg = local.result.arrResult[2].publicResponseReasonText & " P-RAHTWOFAIL">
				<cfreturn local.result>

			<!--- 2 actions, void success then payment attempt --->
			<cfelseif local.numActions is 2 and local.result.arrResult[1].refundType eq "void" and local.result.arrResult[1].responseCode is 1>
						
				<!--- get active allocations of this payment before voiding --->
				<cfstoredproc procedure="tr_getAllocatedPaymentTransactionsForPartialPayment" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentTransactionID#">
					<cfprocresult name="local.qryAllocations" resultset="1">
					<cfprocresult name="local.qryProcessingFees" resultset="2">
				</cfstoredproc>

				<cfset local.strTemp = { recordedOnSiteID=arguments.siteid, recordedByMemberID=arguments.recordedByMemberID, 
										 statsSessionID=arguments.statsSessionID, transactionID=arguments.paymentTransactionID }>
				<cfset local.strVoid = local.objAccounting.recordVoid(argumentcollection=local.strTemp)>
				<cfif local.strVoid.rc is not 0>
					<cfset local.result.errCode = 'RAHVDNM2'>
					<cfset local.result.errMsg = "The refund was successful, but it could not be recorded properly. P-RAHVDNM2">
					<cfreturn local.result>
				</cfif>

				<cfif local.result.arrResult[2].refundType eq "payment" and local.result.arrResult[2].responseCode is not 1>
					<cfset local.result.errCode = 'RAHVDPAY'>
					<cfset local.result.errMsg = "You will need to add a payment for #dollarformat(local.result.arrResult[2].refundAmt)#.<br/>The error message received was: #local.result.arrResult[2].publicResponseReasonText# P-RAHVDPAY">
					<cfreturn local.result>

				<cfelseif local.result.arrResult[2].refundType eq "payment">
					<cfset local.strTemp = { ownedByOrgID=arguments.orgID, recordedOnSiteID=arguments.siteid, assignedToMemberID=arguments.assignedToMemberID, 
											 recordedByMemberID=arguments.recordedByMemberID, statsSessionID=arguments.statsSessionID, status=local.result.arrResult[2].status, 
											 detail=local.result.arrResult[2].transactionDetail, amount=local.result.arrResult[2].refundAmt, transactionDate=arguments.overrideTransactionDate, 
											 debitGLAccountID=local.result.arrResult[2].GLAccountID, profileCode=arguments.profileCode, historyID=val(local.result.arrResult[2].historyID),
											 batchID=local.result.mc_batchID}>
					<cfset local.strPayment = local.objAccounting.recordPayment(argumentcollection=local.strTemp)>
					<cfif local.strPayment.rc is not 0 or local.strPayment.transactionID is 0>
						<cfset local.result.errCode = 'RAHVDPAY2'>
						<cfset local.result.errMsg = "The refund and new payment was successful, but the new payment could not be recorded properly. P-RAHVDPAY2">
						<cfreturn local.result>
					</cfif>
					<cfset local.result.mc_paymentTransactionID = local.strPayment.transactionID>

					<!--- record processing fee sale --->
					<cfif local.qryProcessingFees.recordCount>
						<cfset local.qryAdditionalFees = queryNew("additionalFees,additionalFeesExcTax,additionalFeesTax,additionalFeesRevTransDesc,stateIDForTax,zipForTax",
															"decimal,decimal,decimal,varchar,numeric,string")>

						<cfif queryAddRow(local.qryAdditionalFees)>
							<cfset QuerySetCell(local.qryAdditionalFees,"additionalFees",local.qryProcessingFees.processingFeesIncTax)>
							<cfset QuerySetCell(local.qryAdditionalFees,"additionalFeesExcTax",local.qryProcessingFees.processingFees)>
							<cfset QuerySetCell(local.qryAdditionalFees,"additionalFeesTax",local.qryProcessingFees.processingFeesTax)>
							<cfset QuerySetCell(local.qryAdditionalFees,"additionalFeesRevTransDesc",local.qryProcessingFees.detail)>
							<cfset QuerySetCell(local.qryAdditionalFees,"stateIDForTax",local.qryProcessingFees.stateIDForTax)>
							<cfset QuerySetCell(local.qryAdditionalFees,"zipForTax",local.qryProcessingFees.zipForTax)>
						</cfif>

						<cfset local.strRecordAdditionalPmtFees = local.objAccounting.recordAdditionalPaymentFees(orgID=arguments.orgID, siteID=arguments.siteID, 
							assignedToMemberID=arguments.assignedToMemberID, recordedByMemberID=arguments.recordedByMemberID, statsSessionID=val(session.cfcuser.statsSessionID), 
							paymentTransactionID=local.result.mc_paymentTransactionID, GLAccountID=local.qryProcessingFees.GLAccountID, qryAdditionalFees=local.qryAdditionalFees, 
							paymentFeeTypeID=local.qryProcessingFees.paymentFeeTypeID)>

						<!--- if not successful --->
						<cfif NOT local.strRecordAdditionalPmtFees.success>
							<cfthrow message="Unable to record additional payment fees.">
						</cfif>
					</cfif>

					<!--- reallocate any active sales/tax/adj from old payment to new payment --->
					<cftry>
						<cfloop query="local.qryAllocations">
							<cfset local.strTemp = { recordedOnSiteID=arguments.siteID, recordedByMemberID=arguments.recordedByMemberID, statsSessionID=arguments.statsSessionID, 
													 status="Active", amount=local.qryAllocations.allocAmount, transactionDate=local.qryAllocations.allocDate, 
													 paymentTransactionID=local.result.mc_paymentTransactionID, saleTransactionID=local.qryAllocations.transactionID, ovBatchID=0 }>
							<cfset local.strAllocation = local.objAccounting.recordAllocation(argumentcollection=local.strTemp)>
							<cfif local.strAllocation.rc is not 0 or local.strAllocation.transactionID is 0>
								<cfthrow message="The new allocations could not be recorded properly.">
							</cfif>
							<cfset local.alloctransactionid = local.strAllocation.transactionID>
							<cfset QuerySetCell(local.qryAllocations,"newAllocTID",local.alloctransactionid,local.qryAllocations.currentrow)>
						</cfloop>
						<cfloop query="local.qryAllocations">
							<cfif val(local.qryAllocations.PITTaxTID) gt 0>
								<cfquery name="local.qryGetTID" dbtype="query">
									select newAllocTID
									from [local].qryAllocations
									where transactionID = 0#local.qryAllocations.PITTaxTID#
								</cfquery>
								<cfif val(local.qryGetTID.newAllocTID) gt 0 and val(local.qryAllocations.newAllocTID) gt 0>
									<cfquery name="local.qryAllocTaxTransRel" datasource="#application.dsn.membercentral.dsn#">
										INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
										VALUES (dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), 0#local.qryAllocations.newAllocTID#, 0#local.qryGetTID.newAllocTID#, #arguments.orgID#)
									</cfquery>
								</cfif>
							</cfif>
						</cfloop>
					<cfcatch type="any">
						<cfset local.tmpCatch = { type="", message="The refund was successful, but new allocations could not be recorded properly. TA-REFPAYALLOC1", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
						<cfset local.tmpErr = { qryAllocations=local.qryAllocations } >
						<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
						<cfset local.result.errCode = 'REFPAYALLOC1'>
						<cfset local.result.errMsg = 'The refund was successful, but new allocations could not be recorded properly. P-REFPAYALLOC1'>
						<cfreturn local.result>
					</cfcatch>
					</cftry>
				</cfif>
					
			<!--- 2 actions, void failure then refund success --->
			<cfelseif local.numActions is 2 and local.result.arrResult[1].refundType eq "void" and local.result.arrResult[2].refundType eq "refund">
				<cfset local.strTemp = { ownedByOrgID=arguments.orgID, recordedOnSiteID=arguments.siteid, recordedByMemberID=arguments.recordedByMemberID, 
										 statsSessionID=arguments.statsSessionID, status=local.result.arrResult[2].status, 
										 detail=local.result.arrResult[2].transactionDetail, amount=local.result.arrResult[2].refundAmt, 
										 transactionDate=arguments.overrideTransactionDate, creditGLAccountID=local.result.arrResult[2].GLAccountID, 
										 profileCode=arguments.profileCode, historyID=val(local.result.arrResult[2].historyID), 
										 batchID=local.result.mc_batchID, paymentTransactionID=arguments.paymentTransactionID }>
				<cfset local.strRefund = local.objAccounting.recordRefund(argumentcollection=local.strTemp)>
				<cfif local.strRefund.rc is not 0 or local.strRefund.transactionID is 0>
					<cfset local.result.errCode = 'RAHVDREF2'>
					<cfset local.result.errMsg = 'The refund was successful, but it could not be recorded properly. P-RAHVDREF2'>
					<cfreturn local.result>
				</cfif>
				<cfset local.result.mc_refundTransactionID = local.strRefund.transactionID>
			</cfif>
		
		<cfcatch type="Any">
			<cfset local.result.errCode = "RAHFAIL1">
			<cfset local.result.errMsg = "Unable to process or record the refund properly. Contact support for assistance. P-RAHFAIL1">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
	
		<cfreturn local.result>
	</cffunction>

	<cffunction name="voidAdHoc" access="public" returntype="struct" output="no" hint="Used to process voids on an ad-hoc basis">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
	
		<cfset var local = structNew()>
	
		<cfinvoke method="processVoidByGateway" argumentcollection="#arguments#" returnvariable="local.result">
	
		<cfreturn local.result>
	</cffunction>

	<cffunction name="getSavedInfoOnFile" access="public" returntype="query" output="no">
		<cfargument name="mppid" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="profileID" type="numeric" required="yes">

		<cfset var qryPP = "">
	
		<cfquery name="qryPP" datasource="#application.dsn.membercentral.dsn#">
			EXEC dbo.tr_getSavedInfoOnFile 
				@payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mppid#">,
				@profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">,
				@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
		</cfquery>

		<cfreturn qryPP>
	</cffunction>

	<cffunction name="showGatewaySubmittedForm" access="public" returntype="string" output="no">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="maskSensitive" type="boolean" required="no" default="false">

		<cfset var local = structNew()>
		<cfset local.qryGatewayProfileFields = getGatewayProfileFields(siteid=arguments.siteid, profilecode=arguments.profilecode)>
		
		<cfsavecontent variable="local.SubmittedForm">
			<cfoutput>
			<table cellpadding="2" cellspacing="0" border="1">
			<tr class="tsAppBodyText"><td colspan="2"><strong>Payment Information</strong></td></tr>
			<cfloop query="local.qryGatewayProfileFields">
				<cfswitch expression="#local.qryGatewayProfileFields.fieldType#">
				<cfcase value="Credit Card Number">
					<tr class="tsAppBodyText" valign="top">
						<td>#local.qryGatewayProfileFields.fieldName#:</td>
						<cfif arguments.maskSensitive>
							<td>****#right(arguments.event.getValue('fld_#local.qryGatewayProfileFields.fieldID#_',''),4)#&nbsp;</td>
						<cfelse>
							<td>#arguments.event.getValue('fld_#local.qryGatewayProfileFields.fieldID#_','')#&nbsp;</td>
						</cfif>
					</tr>
				</cfcase>
				<cfcase value="E-Check Account Number">
					<tr class="tsAppBodyText" valign="top">
						<td>#local.qryGatewayProfileFields.fieldName#:</td>
						<cfif arguments.maskSensitive>
							<td>****#right(arguments.event.getValue('fld_#local.qryGatewayProfileFields.fieldID#_',''),4)#&nbsp;</td>
						<cfelse>
							<td>#arguments.event.getValue('fld_#local.qryGatewayProfileFields.fieldID#_','')#&nbsp;</td>
						</cfif>
					</tr>
				</cfcase>
				<cfcase value="Credit Card Security Code"> <!--- always masked, since we dont store actual value and cannot capture it for offline use --->
					<tr class="tsAppBodyText" valign="top">
						<td>#local.qryGatewayProfileFields.fieldName#:</td>
						<td>***</td>
					</tr>				
				</cfcase>
				<cfcase value="Credit Card Expiration Month">
					<tr class="tsAppBodyText" valign="top">
						<td>Expiration Date:</td>
						<cfif arguments.maskSensitive>
							<td>*** / ***&nbsp;</td>
						<cfelse>
							<td>#arguments.event.getValue('fld_6_','')# / #arguments.event.getValue('fld_7_','')#&nbsp;</td>
						</cfif>
					</tr>
				</cfcase>
				<cfcase value="Credit Card Expiration Year">	<!--- should not appear as a separate item. --->
				</cfcase>
				<cfdefaultcase>
					<tr class="tsAppBodyText" valign="top"><td>#local.qryGatewayProfileFields.fieldName#:</td><td>#arguments.event.getValue('fld_#local.qryGatewayProfileFields.fieldID#_','')#&nbsp;</td></tr>
				</cfdefaultcase>
				</cfswitch>
			</cfloop>
			</table>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.SubmittedForm>
	</cffunction>

	<cffunction name="showGatewayInputForm" access="public" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="pmid" type="numeric" required="no" default="0">
		<cfargument name="showCOF" type="boolean" required="no" default="false">
		<cfargument name="usePopup" type="boolean" required="no" default="true">
		<cfargument name="usePopupDIVName" type="string" required="no" default="paymentTable">
		<cfargument name="adminForm" type="boolean" required="no" default="0">
		<cfargument name="overrideCustomerID" type="string" required="no" default="">
		<cfargument name="editMode" type="string" required="no" default="frontEndPayment" hint="frontEndPayment|frontEndManage|controlPanelPayment|controlPanelManage">
		<cfargument name="paymentFeatures" type="struct" required="no" default="#structNew()#">
		<cfargument name="chargeInfo" type="struct" required="no" default="#structNew()#">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { jsvalidation='', inputForm='', headcode='' }>
		<cfset local.qryGatewayProfileFields = getGatewayProfileFields(siteid=arguments.siteid, profilecode=arguments.profilecode)>
		
		<!--- Find out which gateway to use based on profile --->
		<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT pr.profileID, ga.gatewayType, ga.tokenStore, pr.profileCode, pr.tabTitle, mpContent.rawContent as paymentInstructions,
				pr.enableProcessingFeeDonation, pr.processFeeDonationFeePercent, pfm.message as processFeeDonationFEMsg, 
				pr.gatewayAccountID, pr.googlePayMerchantID, pr.enableSurcharge, pr.surchargePercent, pr.surchargeRevenueGLAccountID,
				pr.enableMCPay, ct.currencyType
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			INNER JOIN dbo.sites AS s ON s.siteID = pr.siteID
			INNER JOIN dbo.currencyTypes AS ct ON ct.currencyTypeID = s.defaultCurrencyTypeID
			OUTER APPLY dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.siteID = @siteID
				AND pfm.messageID = pr.solicitationMessageID
			WHERE pr.profileCode = <cfqueryparam value='#arguments.profileCode#' cfsqltype="cf_sql_varchar">
			AND pr.siteID = @siteID
			AND pr.status = 'A'
			AND ga.isActive = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryGatewayID.recordcount is 0>
			<cfset local.returnStruct.inputForm = '<i class="icon-exclamation-sign icon-2x"></i> &nbsp; The input form could not be generated due to a setup issue. Contact your association for assistance.'>
		<cfelse>
			<!--- PMID needs to be from the org. show message if not. This will prevent superusers from seeing the payment form. --->
			<cfset local.isSameOrg = arguments.pmid gt 0 ? isSameOrgMember(siteID=arguments.siteID, memberID=arguments.pmid) : 1>
			<cfif local.isSameOrg>
				<!--- call gateway --->
				<cftry>
					<cfset local.strGatewayVars = { qryGateWayID=local.qryGateWayID, qryGatewayProfileFields=local.qryGatewayProfileFields } >
					<cfset structAppend(local.strGatewayVars,arguments)>
					<cfset local.gatewayComponent = local.qryGateWayID.tokenStore EQ 'bankdraft' ? 'BankDraft' : local.qryGatewayID.gatewayType>
					<cfinvoke component="#local.gatewayComponent#" method="gather" returnvariable="local.returnStruct" argumentcollection="#local.strGatewayVars#">
				<cfcatch type="any">
					<cfset local.returnStruct.inputForm = "There was an error generating the input form.">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			<cfelse>
				<cfset local.returnStruct.inputForm = '<i class="icon-exclamation-sign icon-2x"></i> &nbsp; Member is not eligible to submit payment form.'>
			</cfif>
		</cfif>

		<cfset local.returnStruct.tabTitle = local.qryGateWayID.tabTitle>
		<cfset local.returnStruct.profileID = local.qryGateWayID.profileID>
		<cfset local.returnStruct.profileCode = arguments.profilecode>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getGatewayInputFormLoader" access="public" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="formHolderElementID" type="string" required="no" default="paymentTable">
		<cfargument name="editMode" type="string" required="no" default="frontEndManageV2" hint="frontEndManageV2">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true, errMsg='', headcode='' }>

		<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mp.profileID, g.gatewayType, g.tokenStore, mp.enableMCPay, mp.enableSurcharge, mp.surchargePercent
			FROM dbo.mp_profiles as mp
			INNER JOIN dbo.mp_gateways as g on g.gatewayid = mp.gatewayID
				AND g.isActive = 1
			WHERE mp.profileCode = <cfqueryparam value='#arguments.profileCode#' cfsqltype="cf_sql_varchar">
			AND mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND mp.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryGatewayID.recordCount is 0>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errMsg = 'The input form could not be generated due to a setup issue. Contact your association for assistance.'>
		<cfelse>
			<!--- PMID needs to be from the org. This will prevent superusers from accessing the form. --->
			<cfset local.isSameOrg = arguments.pmid gt 0 ? isSameOrgMember(siteID=arguments.siteID, memberID=arguments.pmid) : 1>
			<cfif local.isSameOrg>
				<cftry>
					<cfset local.strGatewayVars = { qryGateWayID=local.qryGateWayID } >
					<cfset structAppend(local.strGatewayVars,arguments)>
					<cfset local.gatewayComponent = local.qryGateWayID.tokenStore EQ 'bankdraft' ? 'BankDraft' : local.qryGatewayID.gatewayType>
					<cfinvoke component="#local.gatewayComponent#" method="getInputFormLoaderData" returnvariable="local.returnStruct" argumentcollection="#local.strGatewayVars#">
				<cfcatch type="any">
					<cfset local.returnStruct.success = false>
					<cfset local.returnStruct.errMsg = "There was an error getting the input form data.">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errMsg = 'Member is not eligible to access pay method form.'>
			</cfif>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="isSameOrgMember" access="private" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfquery name="local.qryCheckOrgs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberOrgID int, @siteOrgID int, @sameorg bit;

			select @memberOrgID = orgID from dbo.ams_members where memberID = <cfqueryparam value='#arguments.memberID#' cfsqltype="cf_sql_integer">;
			select @siteOrgID = orgID from dbo.sites where siteID = <cfqueryparam value='#arguments.siteID#' cfsqltype="cf_sql_integer">;

			if @memberOrgID = @siteOrgID
				set @sameorg = 1;
			else
				set @sameorg = 0;

			select @sameOrg as isSameOrg;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryCheckOrgs.isSameOrg eq 1>
	</cffunction>

	<cffunction name="getGatewayProfileFields" access="public" returntype="query" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">

		<cfset var qryGatewayProfileFields = "">

		<cfquery name="qryGatewayProfileFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT p.gatewayID, gf.fieldID, gf.isRequired, ft.fieldType, f.fieldName
			FROM dbo.mp_Profiles AS p 
			INNER JOIN dbo.mp_gatewayFields AS gf ON p.gatewayID = gf.gatewayID
			INNER JOIN dbo.mp_fields as f on f.fieldID = gf.fieldID
			INNER JOIN dbo.mp_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			WHERE p.profileCode = <cfqueryparam value='#arguments.profilecode#' cfsqltype="cf_sql_varchar">
			AND p.siteID = <cfqueryparam value='#arguments.siteid#' cfsqltype="cf_sql_integer">
				union
			SELECT p.gatewayID, pf.fieldID, pf.isRequired, ft.fieldType, f.fieldName
			FROM dbo.mp_Profiles AS p 
			INNER JOIN dbo.mp_profileFields AS pf ON p.profileID = pf.profileID
			INNER JOIN dbo.mp_fields as f on f.fieldID = pf.fieldID
			INNER JOIN dbo.mp_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			WHERE p.profileCode = <cfqueryparam value='#arguments.profilecode#' cfsqltype="cf_sql_varchar">
			AND p.siteID = <cfqueryparam value='#arguments.siteid#' cfsqltype="cf_sql_integer">
			ORDER BY fieldID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGatewayProfileFields>
	</cffunction>

	<cffunction name="showHistoryByGateway" access="public" returntype="string" output="no">
		<cfargument name="transactionID" type="numeric" required="yes">
		<cfargument name="maskSensitive" type="boolean" required="no" default="false">
	
		<cfset var local = structNew()>
	
		<cfset local.qryHistory = getPaymentHistory(paymentTransactionID=arguments.transactionID)>
		<cfif local.qryHistory.recordcount is 0 or val(local.qryHistory.siteid) is 0 or len(local.qryHistory.profilecode) is 0>
			<cfsavecontent variable="local.history">
				<cfoutput>
				<div class="tsAppBodyText">
					Payment information could not be retrieved at this time.
					<br/><br/>
					Contact Support for assistance.
				</div>
				</cfoutput>
			</cfsavecontent>		
		<cfelse>

			<!--- get profilefields and gatewayfields --->
			<cfset local.qryGatewayProfileFields = getGatewayProfileFields(siteid=local.qryHistory.siteid, profilecode=local.qryHistory.profileCode)>

			<cfsavecontent variable="local.history">
				<cfoutput>					
				<table border="0" cellpadding="2" cellspacing="0">
				<tr class="tsAppBodyText" valign="top"><td><b>Payment Profile:</b> &nbsp;</td><td>#local.qryHistory.profileName#</td></tr>
				<tr class="tsAppBodyText" valign="top"><td><b>Batch Name/Date:</b> &nbsp;</td><td>#local.qryHistory.batchName# (#dateformat(local.qryHistory.depositDate,'m/d/yyyy')#)</td></tr>
				<tr><td colspan="2"></td></tr>
				<tr class="tsAppBodyText" valign="top"><td><b>Amount:</b> &nbsp;</td><td>#DollarFormat(XMLSearch(local.qryHistory.paymentInfo,"string(//args/x_amount)"))#</td></tr>
				<tr class="tsAppBodyText" valign="top"><td><b>Detail:</b> &nbsp;</td><td>#XMLSearch(local.qryHistory.paymentInfo,"string(//args/x_description)")#</td></tr>
				<tr><td colspan="2"></td></tr>
				</cfoutput>

				<cfif local.qryHistory.typeID is 2>
					<cftry>
						<cfset local.strGatewayVars = { qryHistory=local.qryHistory, qryGatewayProfileFields=local.qryGatewayProfileFields, maskSensitive=arguments.maskSensitive, typeID=local.qryHistory.typeID } >
						<cfinvoke component="#local.qryHistory.gatewayType#" method="history" returnvariable="local.historyStr" argumentcollection="#local.strGatewayVars#">
						<cfoutput>#local.historyStr.htmlHistory#</cfoutput>
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
				</cfif>		
					
				<cfoutput>
				</table>
				</cfoutput>
			</cfsavecontent>

		</cfif>

		<cfreturn local.history>
	</cffunction>
	
	<cffunction name="getHistoryStructByGateway" access="public" returntype="struct" output="no">
		<cfargument name="transactionID" type="numeric" required="yes">
		<cfargument name="maskSensitive" type="boolean" required="no" default="false">
	
		<cfset var local = structNew()>
	
		<cfset local.qryHistory = getPaymentHistory(paymentTransactionID=arguments.transactionID)>
		<cfif local.qryHistory.recordcount is 0 or val(local.qryHistory.siteid) is 0 or len(local.qryHistory.profilecode) is 0>
			<cfset local.returnStr = structNew()>
		<cfelseif listFind("2,4",local.qryHistory.typeID)>
			<!--- get profilefields and gatewayfields --->
			<cfset local.qryGatewayProfileFields = getGatewayProfileFields(siteid=local.qryHistory.siteid, profilecode=local.qryHistory.profileCode)>

			<cftry>
				<cfset local.strGatewayVars = { qryHistory=local.qryHistory, qryGatewayProfileFields=local.qryGatewayProfileFields, maskSensitive=arguments.maskSensitive, typeID=local.qryHistory.typeID } >
				<cfinvoke component="#local.qryHistory.gatewayType#" method="history" returnvariable="local.historyStr" argumentcollection="#local.strGatewayVars#">
				<cfset local.returnStr = local.historyStr.strHistory>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.returnStr = structNew()>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.returnStr = structNew()>
		</cfif>		
		
		<cfreturn local.returnStr>
	</cffunction>

	<cffunction name="getCardTypeGLAccount" access="package" returntype="string" output="no">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="cardnum" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryAcct" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select top 1 pct.GLAccountID
			from dbo.mp_profileCardTypes as pct
			inner join dbo.mp_cardTypes as ct on ct.cardTypeID = pct.cardTypeID
			where pct.profileID = <cfqueryparam value="#arguments.profileid#" cfsqltype="CF_SQL_INTEGER">
			and ct.cardType = dbo.tr_getCardTypeFromNumber(<cfqueryparam value="#arguments.cardnum#" cfsqltype="CF_SQL_VARCHAR">);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryAcct.GLAccountID>
	</cffunction>

	<cffunction name="getCardTypeGLAccountFromCardType" access="package" returntype="string" output="no">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="cardtype" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryAcct" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select top 1 pct.GLAccountID
			from dbo.mp_profileCardTypes as pct
			inner join dbo.mp_cardTypes as ct on ct.cardTypeID = pct.cardTypeID
			where pct.profileID = <cfqueryparam value="#arguments.profileid#" cfsqltype="CF_SQL_INTEGER">
			and ct.cardType = <cfqueryparam value="#arguments.cardtype#" cfsqltype="CF_SQL_VARCHAR">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryAcct.GLAccountID>
	</cffunction>

	<cffunction name="insertPaymentHistory" access="package" returntype="struct" output="no">
		<cfargument name="payerMemberID" type="numeric" required="yes">
		<cfargument name="memberPaymentProfileID" type="numeric" required="yes">
		<cfargument name="paymentInfo" type="string" required="yes">
		<cfargument name="gatewayID" type="numeric" required="yes">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="paymentType" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { historyID=0, refId=""}>
		
		<cftry>
			<cfquery name="local.qryHistory" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @nowDate datetime, @midnight datetime, @profileID int, @siteID int, @orgID int, @historyID int = 0, @refId varchar(20);
				set @nowDate = getdate();
				set @midnight = DATEADD(Day, 0, DATEDIFF(Day, 0, @nowDate));
				set @profileID = <cfqueryparam value="#arguments.profileID#" cfsqltype="CF_SQL_INTEGER">;

				select @siteID = s.siteID, @orgID = s.orgID
				from dbo.mp_profiles as mp
				inner join dbo.sites as s on s.siteID = mp.siteID
				where mp.profileID = @profileID;
				
				BEGIN TRAN;
					INSERT INTO dbo.tr_paymentHistory (paymentInfo, dateAttempted, statsSessionID, payerMemberID, 
						memberPaymentProfileID, isSuccess, gatewayID, profileID, paymentType, orgID)
					VALUES (
						<cfqueryparam value="#arguments.paymentInfo#" cfsqltype="CF_SQL_LONGVARCHAR">,
						@nowDate,
						<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#arguments.payerMemberID#" cfsqltype="CF_SQL_INTEGER">,
						<cfif arguments.memberPaymentProfileID gt 0>
							<cfqueryparam value="#arguments.memberPaymentProfileID#" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							null,
						</cfif>
						<cfqueryparam value="0" cfsqltype="CF_SQL_BIT">,
						<cfqueryparam value="#arguments.gatewayID#" cfsqltype="CF_SQL_INTEGER">,
						@profileID,
						<cfqueryparam value="#arguments.paymentType#" cfsqltype="CF_SQL_VARCHAR">,
						@orgID
					);
					
					SET @historyID = SCOPE_IDENTITY();
					
					SET @refId = CONCAT('MC_',@siteID,'_',@historyID);
					
					UPDATE dbo.tr_paymentHistory 
					SET refId = @refId 
					WHERE historyID = @historyID;
				COMMIT TRAN;
				
				SELECT @historyID as historyID, @refId as refId;
			</cfquery>
			
			<cfset local.returnStruct.historyID = local.qryHistory.historyID>
			<cfset local.returnStruct.refId = local.qryHistory.refId>
		
			<cfreturn local.returnStruct>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="updatePaymentHistory" access="package" returntype="numeric" output="no">
		<cfargument name="historyID" type="numeric" required="yes">
		<cfargument name="gatewayResponse" type="string" required="yes">
		<cfargument name="responseReasonCode" type="string" required="yes">

		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.gatewayTransactionID = xmlsearch(arguments.gatewayResponse,"string(/response/transactionid)")>
			<cfset local.gatewayApprovalCode = xmlsearch(arguments.gatewayResponse,"string(/response/approvalcode)")>
			<cfset local.isSuccess = xmlsearch(arguments.gatewayResponse,"string(/response/responsecode)")>
			<cfif local.isSuccess neq "1">
				<cfset local.isSuccess = "0">
			</cfif>

			<cfquery name="local.qryHistory" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @nowDate datetime, @midnight datetime, @historyID int, @mppid int, @paymentType varchar(7);
				set @nowDate = getdate();
				set @midnight = DATEADD(Day, 0, DATEDIFF(Day, 0, @nowDate));
				set @historyID = <cfqueryparam value="#arguments.historyID#" cfsqltype="CF_SQL_INTEGER">;

				SELECT @mppid = memberPaymentProfileID, @paymentType = paymentType
				FROM dbo.tr_paymentHistory
				WHERE historyID = @historyID;

				UPDATE dbo.tr_paymentHistory 
				SET gatewayResponse = <cfqueryparam value="#arguments.gatewayResponse#" cfsqltype="CF_SQL_LONGVARCHAR">,
					datePaid = @nowDate,
					gatewayTransactionID = <cfqueryparam value="#local.gatewayTransactionID#" cfsqltype="CF_SQL_VARCHAR">,
					gatewayApprovalCode = <cfqueryparam value="#local.gatewayApprovalCode#" cfsqltype="CF_SQL_VARCHAR">,						
					isSuccess = <cfqueryparam value="#local.isSuccess#" cfsqltype="CF_SQL_BIT">,
					responseReasonCode = nullif(<cfqueryparam value="#arguments.responseReasonCode#" cfsqltype="CF_SQL_VARCHAR">,'')
				WHERE historyID = @historyID;

				-- for PAYMENTS only, if card on file, update failed dates
				IF @paymentType = 'payment' AND @mppid IS NOT NULL
					update mpp
					set mpp.failedLastDate = <cfif local.isSuccess>null<cfelse>@nowDate</cfif>,
						mpp.failedSinceDate = <cfif local.isSuccess>null<cfelse>isNull(mpp.failedSinceDate,@nowDate)</cfif>,
						mpp.nextAllowedAutoChargeDate = <cfif local.isSuccess>null<cfelse>dateadd(day,mp.daysBetweenAutoAttempts,@midnight)</cfif>,
						mpp.failedCount = <cfif local.isSuccess>null<cfelse>isnull(mpp.failedCount,0)+1</cfif>
					from dbo.ams_memberPaymentProfiles mpp
					inner join dbo.mp_profiles mp on mp.profileID = mpp.profileID
					where mpp.payProfileID = @mppid;
			</cfquery>
			
			<cfreturn arguments.historyID>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfreturn 0>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="getProfileCardTypes" access="package" returntype="query" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">

		<cfset var qryCardTypes = "">

		<cfquery name="qryCardTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ct.cardType
			FROM dbo.mp_profileCardTypes AS pct 
			INNER JOIN dbo.mp_cardTypes AS ct ON pct.cardTypeID = ct.cardTypeID
			INNER JOIN dbo.mp_Profiles as p on p.profileID = pct.profileID
			WHERE p.profileCode = <cfqueryparam value='#arguments.profilecode#' cfsqltype="cf_sql_varchar">
			AND p.siteID = <cfqueryparam value='#arguments.siteid#' cfsqltype="cf_sql_integer">
			ORDER BY ct.cardType;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCardTypes>
	</cffunction>

	<cffunction name="processPaymentByGateway" access="private" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes" hint="applepay and googlepay will pass in 0">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="tokenData" type="struct" required="no" hint="used for applepay and googlepay">
	
		<cfset var local = structNew()>
		<cfset local.strArgsCopy = duplicate(arguments)>
	
		<!--- Find out which gateway to use based on profile id --->
		<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
			SELECT pr.siteID, s.orgID, pr.profileid, pr.gatewayID, pr.gatewayMerchantId, pr.gatewayUsername, pr.gatewayPassword, pr.GLAccountID, 
				ga.gatewayType, pr.allowPayments, pr.transactionLabel, ct.currencyType
			FROM dbo.mp_profiles AS pr
			INNER JOIN dbo.mp_gateways AS ga ON ga.gatewayid = pr.gatewayID
			INNER JOIN dbo.sites AS s ON s.siteID = pr.siteID
			INNER JOIN dbo.currencyTypes AS ct ON ct.currencyTypeID = s.defaultCurrencyTypeID
			WHERE pr.profileCode = <cfqueryparam value='#arguments.profileCode#' cfsqltype="cf_sql_varchar">
			AND pr.siteID = <cfqueryparam value='#arguments.siteid#' cfsqltype="cf_sql_integer">
			AND pr.status = 'A'
			AND ga.isActive = 1
		</cfquery>
			
		<!--- get profilefields and gatewayfields --->
		<cfset local.qryGatewayProfileFields = getGatewayProfileFields(siteid=arguments.siteid, profilecode=arguments.profileCode)>
		
		<!--- Set Payment Amount if tokenData is present --->
		<cfif isDefined("arguments.tokenData") and arguments.tokenData.mctokensource eq "applePay">
			<cfset arguments.x_amount = numberFormat(precisionEvaluate(arguments.tokenData.decryptedToken.transactionAmount/100),"0.99")>
		</cfif>

		<cfscript>
		// init response struct
		local.returnStruct = { 	responseCode=99999, responseReasonText="Invalid request.", publicResponseReasonText="Invalid Request.", responseReasonCode="", rawResponse="", 
			historyID=0, transactionDetail="", GLAccountID=0, transactionid="", gatewayID=val(local.qryGateWayID.gatewayID), approvalCode="", status="Active",
			x_amount=0 };

		// check for amount to charge gt 0
		if (arguments.x_amount lte 0) {
			local.returnStruct.responseReasonText = "There is no amount to process.";
			local.returnStruct.publicResponseReasonText = local.returnStruct.responseReasonText;
			return local.returnStruct;
	
		// check for valid profile
		} else if (val(local.qryGateWayID.gatewayID) is 0) {
			local.returnStruct.responseReasonText = "The payment profile is not valid.";
			local.returnStruct.publicResponseReasonText = local.returnStruct.responseReasonText;
			return local.returnStruct;

		// profile allows payments?
		} else if (local.qryGateWayID.allowPayments is not 1) {
			local.returnStruct.responseReasonText = "The payment profile does not accept payments.";
			local.returnStruct.publicResponseReasonText = local.returnStruct.responseReasonText;
			return local.returnStruct;
		}
		</cfscript>
		
		<!--- call gateway --->
		<cftry>
			<cfset local.strGatewayVars = { qryGateWayID=local.qryGateWayID, returnStruct=local.returnStruct, qryGatewayProfileFields=local.qryGatewayProfileFields } >
			<cfset structAppend(local.strGatewayVars,arguments)>
			<cfinvoke component="#local.qryGatewayID.gatewayType#" method="charge" returnvariable="local.returnStruct" argumentcollection="#local.strGatewayVars#">
		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = "There was an error processing the payment.">
			<cfset local.returnStruct.publicResponseReasonText = "Payment Invalid. There was an error processing the payment.">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processRefundByGateway" access="private" returntype="array" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="qryGateWayID" type="query" required="yes">
	
		<cfset var local = structNew()>

		<!--- get profilefields and gatewayfields --->
		<cfset local.qryGatewayProfileFields = getGatewayProfileFields(siteid=arguments.siteid, profilecode=arguments.profileCode)>
		
		<cfscript>
		// init response struct
		local.returnStruct = { 	responseCode=99999, responseReasonText="Invalid request.", publicResponseReasonText="Payment Invalid", 
			responseReasonCode="", rawResponse="", historyID=0, transactionDetail="", refundType="refund", refundAmt=0, GLAccountID=0, 
			transactionid="", approvalCode="", status="Active" };
		local.arrReturnStructs = [ duplicate(local.returnStruct) ];
	
		// check for amount to charge gt 0
		if (arguments.x_amount lte 0) {
			local.arrReturnStructs[1].responseReasonText = "There is no amount to refund.";
			local.arrReturnStructs[1].publicResponseReasonText = local.arrReturnStructs[1].responseReasonText 
			return local.arrReturnStructs;
	
		// check for valid profile
		} else if (val(arguments.qryGateWayID.gatewayID) is 0) {
			local.arrReturnStructs[1].responseReasonText = "The payment profile is not valid.";
			local.arrReturnStructs[1].publicResponseReasonText = local.arrReturnStructs[1].responseReasonText 
			return local.arrReturnStructs;

		// profile allows refunds?
		} else if (arguments.qryGateWayID.allowRefunds is not 1) {
			local.arrReturnStructs[1].responseReasonText = "The payment profile does not accept refunds.";
			local.arrReturnStructs[1].publicResponseReasonText = local.arrReturnStructs[1].responseReasonText 
			return local.arrReturnStructs;
		}
		
		// get original payment info
		local.qryPaymentHistory = getPaymentHistory(paymentTransactionID=arguments.paymentTransactionID);
		</cfscript>		

		<!--- call gateway --->
		<cftry>			
			<cfset local.strGatewayVars = { qryGateWayID=arguments.qryGateWayID, arrReturnStructs=local.arrReturnStructs, qryGatewayProfileFields=local.qryGatewayProfileFields,
				qryPaymentHistory=local.qryPaymentHistory } >
			<cfset structAppend(local.strGatewayVars,arguments)>
			<cfinvoke component="#arguments.qryGateWayID.gatewayType#" method="credit" returnvariable="local.arrReturnStructs" argumentcollection="#local.strGatewayVars#">
		<cfcatch type="any">
			<cfset local.arrReturnStructs[1].responseReasonText = "There was an error processing the refund.">
			<cfset local.arrReturnStructs[1].publicResponseReasonText = local.arrReturnStructs[1].responseReasonText>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfreturn local.arrReturnStructs>
		</cfcatch>
		</cftry>

		<cfreturn local.arrReturnStructs>
	</cffunction>

	<cffunction name="processVoidByGateway" access="private" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.strArgsCopy = duplicate(arguments)>
	
		<!--- Find out which gateway to use based on profile id --->
		<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
			SELECT pr.siteID, pr.profileid, pr.gatewayID, pr.gatewayUsername, pr.gatewayPassword, pr.GLAccountID, pr.gatewayMerchantID, ga.gatewayType
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			WHERE pr.profileCode = <cfqueryparam value='#arguments.profileCode#' cfsqltype="cf_sql_varchar">
			AND pr.siteID = <cfqueryparam value='#arguments.siteid#' cfsqltype="cf_sql_integer">
			AND pr.status = 'A'
			AND ga.isActive = 1
		</cfquery>

		<!--- get profilefields and gatewayfields --->
		<cfset local.qryGatewayProfileFields = getGatewayProfileFields(siteid=arguments.siteid, profilecode=arguments.profileCode)>
		
		<cfscript>
		// init response struct
		local.returnStruct = { 	responseCode=99999, responseReasonText="Invalid request.", publicResponseReasonText="Payment Invalid",
			responseReasonCode="", rawResponse="", historyID=0, transactionid="", approvalCode="" };

		// check for valid profile
		if (val(local.qryGateWayID.gatewayID) is 0) {
			local.returnStruct.responseReasonText = "The payment profile is not valid.";
			local.returnStruct.publicResponseReasonText = "Void Invalid";
			return local.returnStruct;
		}
		
		// get original payment info
		local.qryPaymentHistory = getPaymentHistory(paymentTransactionID=arguments.paymentTransactionID);
		</cfscript>
		
		<!--- call gateway --->
		<cftry>
			<cfset local.strGatewayVars = { qryGateWayID=local.qryGateWayID, returnStruct=local.returnStruct, qryGatewayProfileFields=local.qryGatewayProfileFields,
											qryPaymentHistory=local.qryPaymentHistory } >
			<cfset structAppend(local.strGatewayVars,arguments)>
			<cfinvoke component="#local.qryGatewayID.gatewayType#" method="void" returnvariable="local.returnStruct" argumentcollection="#local.strGatewayVars#">
		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = "There was an error processing the void.">
			<cfset local.returnStruct.publicResponseReasonText = "Invalid request. There was an error processing the void.">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getPaymentHistory" access="private" returntype="query" output="no">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		
		<cfset var qryHistory = "">
		
		<cfquery name="qryHistory" datasource="#application.dsn.membercentral.dsn#">
			select top 1 ph.datepaid, ph.paymentInfo, ph.gatewayResponse, ph.gatewayTransactionID, ph.gatewayApprovalCode, ph.memberPaymentProfileID, 
				mp.profileid, mp.gatewayID, mp.siteid, mp.profileCode, t.typeID, ga.gatewayType, t.assignedToMemberID, 
				mp.profileName, b.batchName, b.depositDate, bs.status as batchStatus, ph.refId
			from dbo.tr_paymentHistory as ph
			inner join dbo.tr_transactionPayments as tp on tp.orgID = ph.orgID and tp.historyID = ph.historyID
			inner join dbo.tr_transactions as t on t.transactionID = tp.transactionID
			inner join dbo.tr_batchTransactions as bt on bt.orgID = t.ownedByOrgID and bt.transactionID = t.transactionID
			inner join dbo.tr_batches as b on b.orgID = bt.orgID and b.batchID = bt.batchID
			inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
			inner join dbo.mp_profiles as mp on mp.profileID = ph.profileID
			inner join dbo.mp_gateways as ga on ga.gatewayID = mp.gatewayID
			where t.transactionID = <cfqueryparam value="#arguments.paymentTransactionID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn qryHistory>
	</cffunction>

	<cffunction name="getMemberPayProfileInvoices" access="package" returntype="query" output="no">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="includeOpenInvoices" type="boolean" required="yes">
		<cfargument name="limitToPayProfileID" type="numeric" required="no" default="0">

		<cfset var qryResult = "">

		<!--- merge will put active COF on the new memberID, but merged member may still be in session so we need force lookup of activeMemberID anyway --->
		<cfquery name="qryResult" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

 			declare @cofMemberID int, @orgID int;
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);
			select @orgID = orgID from dbo.ams_members where memberID = @cofMemberID;

			select mpp.payProfileID, ip.profileName, i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, 
				i.dateDue, sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) as amountDue
			from dbo.ams_memberPaymentProfiles as mpp
			inner join dbo.ams_members m on m.orgID = @orgID and m.memberID = mpp.memberID
			inner join dbo.tr_invoices as i on i.orgID = @orgID and i.payProfileID = mpp.payProfileID
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
			inner join dbo.organizations as o on o.orgID = ip.orgID
			inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
			where mpp.memberID = @cofMemberID
			<cfif arguments.limitToPayProfileID>
				and mpp.payProfileID = <cfqueryparam value="#arguments.limitToPayProfileID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif not arguments.includeOpenInvoices>
				and ins.status <> 'open'
			</cfif>
			and mpp.status = 'A'
			group by mpp.payProfileID, ip.profileName, i.invoiceID, ins.status, o.orgcode, i.invoicenumber, i.dateDue;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryResult>
	</cffunction>

	<cffunction name="getMemberPayProfileSubscriptions" access="package" returntype="query" output="no">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="limitToPayProfileID" type="numeric" required="no" default="0">

		<cfset var qryResult = ""/>

		<!--- merge will put active COF on the new memberID, but merged member may still be in session so we need force lookup of activeMemberID anyway --->
		<cfquery name="qryResult" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @cofMemberID int;
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);

			select mpp.payProfileID, t.typeName, subs.subscriptionName
			from dbo.ams_memberPaymentProfiles as mpp
			inner join dbo.ams_members m on m.memberID = mpp.memberID
			inner join dbo.sub_subscribers as ss on ss.payProfileID = mpp.payProfileID
			inner join dbo.sub_statuses st on st.statusID = ss.statusID
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
			inner join dbo.sub_types t on t.typeID = subs.typeID
			where mpp.memberID = @cofMemberID
			<cfif arguments.limitToPayProfileID>
				and mpp.payProfileID = <cfqueryparam value="#arguments.limitToPayProfileID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			and ss.parentSubscriberID is null
			and st.statusCode in ('A','P','I','O')
			order by mpp.payProfileID, t.typeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryResult>
	</cffunction>

	<cffunction name="getMemberPayProfileContributions" access="package" returntype="query" output="no">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="limitToPayProfileID" type="numeric" required="no" default="0">

		<cfset var qryResult = ""/>

		<!--- merge will put active COF on the new memberID, but merged member may still be in session so we need force lookup of activeMemberID anyway --->
		<cfquery name="qryResult" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @cofMemberID int;
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);

			select mpp.payProfileID, cp.programName
			from dbo.ams_memberPaymentProfiles as mpp
			inner join dbo.ams_members m on m.memberID = mpp.memberID
			inner join dbo.cp_contributionPayProfiles as cpp on cpp.payProfileID = mpp.payProfileID
			inner join dbo.cp_contributions as c on c.contributionID = cpp.contributionID
			inner join dbo.cp_programs as cp on cp.programID = c.programID
			where mpp.memberID = @cofMemberID
			<cfif arguments.limitToPayProfileID>
				and mpp.payProfileID = <cfqueryparam value="#arguments.limitToPayProfileID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			order by mpp.payProfileID, cp.programName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryResult>
	</cffunction>

	<cffunction name="reassignMemberPayProfileApplications" access="package" returntype="void" output="no">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="MPProfileID" type="numeric" required="yes">
		<cfargument name="payProfileID" type="numeric" required="yes">
		<cfargument name="newPayProfileID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfif arguments.newPayProfileID neq arguments.payProfileID>
			<cfset local.recordedByMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)>
			<cfif NOT local.recordedByMemberID>
				<cfset local.recordedByMemberID = arguments.memberID>
			</cfif>

			<cfstoredproc procedure="tr_reassignCOFApplications" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.MPProfileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.newPayProfileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>
		</cfif>
	</cffunction>

	<cffunction name="isValidMemberToPrefillCardInfo" access="package" returntype="boolean" output="no">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var qryCheck = "">

		<cfquery name="qryCheck" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @memberID int, @sysMemberID int;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			select @sysMemberID = dbo.fn_ams_getMCSystemMemberID();

			IF @memberID = @sysMemberID
				select 1 as invalidMember;
			ELSE 
				select 1 as invalidMember
				from dbo.ref_referrals
				where clientFeeMemberID = @memberID;
		</cfquery>

		<cfreturn qryCheck.recordCount is 0>
	</cffunction>

	<cffunction name="getLevel3Data" access="public" output="false" returntype="array">
		<cfargument name="qryLevel3Data" type="query" required="yes">
		<cfargument name="gatewayType" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.arrData = []>

		<cfswitch expression="#arguments.gatewayType#">
			<cfcase value="AuthorizeCCCIM">
				<cfloop query="arguments.qryLevel3Data">
					<cfset local.tmpStr = structNew('ordered')>
					<cfset structInsert(local.tmpStr, "itemId", arguments.qryLevel3Data.currentRow)>
					<cfset structInsert(local.tmpStr, "name", "#left(encodeForHTML(arguments.qryLevel3Data.name),31)#")>
					<cfset structInsert(local.tmpStr, "description", "#left(encodeForHTML(arguments.qryLevel3Data.desc),255)#")>
					<cfset structInsert(local.tmpStr, "quantity", arguments.qryLevel3Data.qty)>
					<cfset structInsert(local.tmpStr, "unitPrice", arguments.qryLevel3Data.itemPriceIncDiscount)>
					<cfset local.arrData.append(local.tmpStr)>
				</cfloop>
			</cfcase>
		</cfswitch>
		
		<cfreturn local.arrData>
	</cffunction>

	<cffunction name="applePayOVM" access="public" returntype="struct" output="false">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="profileID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success":true, "data":{} }>

		<cftry>
			<cfset local.applePayUtils = new applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url)>
			<cfset local.strApplePayResult = local.applePayUtils.onvalidateMerchant(profileID=arguments.profileID, siteCode=arguments.mcproxy_siteCode)>
			<cfif local.strApplePayResult.success>
				<cfset local.strReturn.data = local.strApplePayResult.data>
			<cfelse>
				<cfthrow message="#local.strApplePayResult.error.message#">
			</cfif>
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="TSApplePayOVM" access="public" returntype="struct" output="false">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="merchantOrgCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success":true, "data":{} }>

		<cftry>
			<cfset local.applePayUtils = new applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url)>
			<cfset local.strApplePayResult = local.applePayUtils.onvalidateMerchantForTS(merchantOrgCode=arguments.merchantOrgCode, siteCode=arguments.mcproxy_siteCode)>
			<cfif local.strApplePayResult.success>
				<cfset local.strReturn.data = local.strApplePayResult.data>
			<cfelse>
				<cfthrow message="#local.strApplePayResult.error.message#">
			</cfif>
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="applePayOPA" access="public" returntype="struct" output="false">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="payload" type="string" required="yes">

		<cfset var local = {}>
		<cfset local.strReturn = { "success":true, "data":{} }>

		<cftry>
			<cfset local.applePayUtils = new applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url)>
			<cfset local.strApplePayResult = local.applePayUtils.decryptToken(profileID=arguments.profileID, siteCode=arguments.mcproxy_siteCode, payload=arguments.payload)>

			<cfif local.strApplePayResult.success>
				<cfset local.strReturn.data = local.strApplePayResult.data>
			<cfelse>
				<cfthrow message="#local.strApplePayResult.error.message#">
			</cfif>

		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="TSApplePayOPA" access="public" returntype="struct" output="false">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="merchantOrgCode" type="string" required="true">
		<cfargument name="payload" type="string" required="yes">

		<cfset var local = {}>
		<cfset local.strReturn = { "success":true, "data":{} }>

		<cftry>
			<cfset local.applePayUtils = new applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url)>
			<cfset local.strApplePayResult = local.applePayUtils.decryptTokenForTS(merchantOrgCode=arguments.merchantOrgCode, siteCode=arguments.mcproxy_siteCode, payload=arguments.payload)>

			<cfif local.strApplePayResult.success>
				<cfset local.strReturn.data = local.strApplePayResult.data>
			<cfelse>
				<cfthrow message="#local.strApplePayResult.error.message#">
			</cfif>

		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="applePayTestOPA" access="public" returntype="struct" output="false">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="payload" type="string" required="yes">

		<cfset var local = {}>
		<cfset local.strReturn = { "success":true, "data":{} }>

		<cftry>
			<cfset local.applePayUtils = new applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url)>
			<cfset local.strApplePayResult = local.applePayUtils.decryptToken(profileID=arguments.profileID, siteCode=arguments.mcproxy_siteCode, payload=arguments.payload)>

			<cfif local.strApplePayResult.success>
				<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
				<cfset local.payAttempt = chargeAdHoc(
					orgID=local.mc_siteinfo.orgID,
					siteID=local.mc_siteinfo.siteID,
					profileCode=arguments.profileCode,
					assignedToMemberID=session.cfcuser.memberData.memberid,
					recordedByMemberID=session.cfcuser.memberData.memberid,
					statsSessionID=session.cfcuser.statsSessionID,
					x_amount=0,
					x_description="Test ApplePay Payment",
					tokenData=local.strApplePayResult.data
				)>
			<cfelse>
				<cfthrow message="#local.strApplePayResult.error.message#">
			</cfif>

		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="googlePayTestPP" access="public" returntype="struct" output="false">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="payload" type="string" required="yes">

		<cfset var local = {}>
		<cfset local.strReturn = { "success":true, "data":{} }>
		<cfset local.tokenData = { "mctokensource":"googlePay" }>
			
		<cftry>
			<cfset local.tokenData["paymentData"] = deserializeJSON(arguments.payload)>
			<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
			<cfset local.payAttempt = chargeAdHoc(
				orgID=local.mc_siteinfo.orgID,
				siteID=local.mc_siteinfo.siteID,
				profileCode=arguments.profileCode,
				assignedToMemberID=session.cfcuser.memberData.memberid,
				recordedByMemberID=session.cfcuser.memberData.memberid,
				statsSessionID=session.cfcuser.statsSessionID,
				x_amount=arguments.amount,
				x_description="Test GooglePay Payment",
				tokenData=local.tokenData
			)>
			<cfif local.payAttempt.responseCode is not 1>
				<cfthrow message="#local.payAttempt.responseReasonText#">
			</cfif>

		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getChargeInfoAndPaymentFeatures" access="public" output="no" returntype="struct">
		<cfargument name="amt" type="numeric" required="yes">
		<cfargument name="strprocessingfee" type="struct" required="yes">
		<cfargument name="enableSurcharge" type="boolean" required="yes">
		<cfargument name="enableApplePay" type="boolean" required="yes">
		<cfargument name="enableGooglePay" type="boolean" required="yes">
		<cfargument name="stateIDForTax" type="numeric" required="yes">
		<cfargument name="zipForTax" type="string" required="yes">

		<cfset var strReturn = { 
			"chargeInfo": { "amt": arguments.amt, "processingfees":0 }, 
			"paymentFeatures": setDefaultPayFeaturesStruct()
		}>
		
		<cfif arguments.strprocessingfee.enable AND val(arguments.strprocessingfee.feepct)>
			<cfset var strProcessingFees = getPaymentProcessingFees(gl=arguments.strprocessingfee.gl, amt=arguments.amt, feepct=arguments.strprocessingfee.feepct,
											stateIDForTax=arguments.stateIDForTax, zipForTax=arguments.zipForTax)>
			<cfset strReturn.chargeInfo.processingfees = strProcessingFees.processingfees>
			<cfset strReturn.paymentFeatures.processingFee = { 
				"enable": 1, 
				"select": val(arguments.strprocessingfee.select) EQ 1 ? 1 : 0, 
				"label": findNoCase("{{AMOUNT}}", arguments.strprocessingfee.label) 
							? replaceNoCase(arguments.strprocessingfee.label, "{{AMOUNT}}", strProcessingFees.processingfeesDspLabel)
							: replaceNoCase(arguments.strprocessingfee.label, "{{PERCENT}}", "#strProcessingFees.feepct#%"), 
				"denylabel": arguments.strprocessingfee.denylabel,
				"title": arguments.strprocessingfee.title,
				"msg": arguments.strprocessingfee.msg 
			}>
		</cfif>
		<cfset strReturn.paymentFeatures.surcharge.enable = arguments.enableSurcharge>
		<cfset strReturn.paymentFeatures.applePay.enable = arguments.enableApplePay>
		<cfset strReturn.paymentFeatures.googlePay.enable = arguments.enableGooglePay>

		<cfreturn strReturn>
	</cffunction>

	<cffunction name="getPaymentProcessingFees" access="public" output="no" returntype="struct">
		<cfargument name="gl" type="numeric" required="yes">
		<cfargument name="amt" type="numeric" required="yes">
		<cfargument name="feepct" type="numeric" required="yes">
		<cfargument name="stateIDForTax" type="numeric" required="yes">
		<cfargument name="zipForTax" type="string" required="yes">

		<cfset var strReturn = { "success":true, "processingfees":0, "processingfeesExcTax":0, "tax":0, "processingfeesDspLabel":"" }>
		
		<cftry>
			<cfset strReturn.processingfeesExcTax = NumberFormat(precisionEvaluate(arguments.amt * arguments.feepct/100),'0.00')>
			<cfset strReturn.tax = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=arguments.gl, 
									saleAmount=strReturn.processingfeesExcTax, transactionDate=dateFormat(now(),"m/d/yyyy"), stateIDForTax=arguments.stateIDForTax, 
									zipForTax=arguments.zipForTax).totalTaxAmt>
			
			<cfif strReturn.tax>
				<cfset strReturn.processingfees = strReturn.processingfeesExcTax + strReturn.tax>
				<cfset strReturn.processingfeesDspLabel = "#dollarFormat(strReturn.processingfeesExcTax)# (+#dollarFormat(strReturn.tax)# tax)">
			<cfelse>
				<cfset strReturn.processingfees = strReturn.processingfeesExcTax>
				<cfset strReturn.processingfeesDspLabel = dollarFormat(strReturn.processingfeesExcTax)>
			</cfif>
		<cfcatch type="any">
			<cfset strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=strReturn)>
		</cfcatch>
		</cftry>

		<cfreturn strReturn>
	</cffunction>

	<cffunction name="getAdditionalFeesInfo" access="public" output="no" returntype="struct">
		<cfargument name="qryMerchantProfile" type="query" required="yes">
		<cfargument name="amt" type="numeric" required="yes">
		<cfargument name="stateIDForTax" type="numeric" required="yes">
		<cfargument name="zipForTax" type="string" required="yes">
		<cfargument name="processingFeeOpted" type="boolean" required="yes">
		<cfargument name="surchargeEligibleCard" type="boolean" required="yes">

		<cfset var strReturn = { 
			"success": true, 
			"finalAmountToCharge": arguments.amt, 
			"additionalFees": 0, 
			"additionalFeesPct": 0, 
			"additionalFeesExcTax": 0, 
			"additionalFeesTax": 0, 
			"additionalFeesLabel": "",
			"additionalFeesAmtDspLabel": "",
			"offeredPaymentFee": false,
			"paymentFeeTypeID": 0,
			"gl": 0,
			"qryAdditionalFees": queryNew("additionalFees,additionalFeesExcTax,additionalFeesTax,additionalFeesRevTransDesc,additionalFeesLabel,stateIDForTax,zipForTax",
									"decimal,decimal,decimal,varchar,varchar,numeric,string")
		}>
		
		<!--- no amt to calc --->
		<cfif arguments.amt EQ 0>
			<cfreturn strReturn>
		</cfif>
		
		<cftry>
			<cfif arguments.qryMerchantProfile.enableProcessingFeeDonation AND arguments.qryMerchantProfile.processFeeDonationFeePercent GT 0>
				<cfset strReturn.offeredPaymentFee = true>
				<cfif arguments.processingFeeOpted>
					<cfset strReturn.gl = arguments.qryMerchantProfile.processFeeDonationRenevueGLAccountID>
					<cfset strReturn.additionalFeesPct = arguments.qryMerchantProfile.processFeeDonationFeePercent>
					<cfset strReturn.paymentFeeTypeID = 1>
					<cfset strReturn.additionalFeesLabel = arguments.qryMerchantProfile.processingFeeLabel>
				</cfif>
			<cfelseif arguments.qryMerchantProfile.enableSurcharge AND arguments.surchargeEligibleCard>
				<cfset strReturn.gl = arguments.qryMerchantProfile.surchargeRevenueGLAccountID>
				<cfset strReturn.additionalFeesPct = arguments.qryMerchantProfile.surchargePercent>
				<cfset strReturn.paymentFeeTypeID = 2>
				<cfset strReturn.additionalFeesLabel = "Surcharge">
			</cfif>

			<cfif listFind("1,2",strReturn.paymentFeeTypeID) AND strReturn.additionalFeesPct>
				<cfset strReturn.additionalFeesExcTax = NumberFormat(precisionEvaluate(arguments.amt * strReturn.additionalFeesPct/100),'0.00')>
				<cfset strReturn.additionalFeesTax = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=strReturn.gl, 
										saleAmount=strReturn.additionalFeesExcTax, transactionDate=dateFormat(now(),"m/d/yyyy"), stateIDForTax=arguments.stateIDForTax, 
										zipForTax=arguments.zipForTax).totalTaxAmt>
				
				<cfif strReturn.additionalFeesTax>
					<cfset strReturn.additionalFees = strReturn.additionalFeesExcTax + strReturn.additionalFeesTax>
					<cfset strReturn.additionalFeesAmtDspLabel = "#dollarFormat(strReturn.additionalFeesExcTax)# (+#dollarFormat(strReturn.additionalFeesTax)# tax)">
				<cfelse>
					<cfset strReturn.additionalFees = strReturn.additionalFeesExcTax>
					<cfset strReturn.additionalFeesAmtDspLabel = dollarFormat(strReturn.additionalFeesExcTax)>
				</cfif>

				<cfset strReturn.finalAmountToCharge = NumberFormat(precisionEvaluate(arguments.amt + strReturn.additionalFees),'0.00')>

				<cfif queryAddRow(strReturn.qryAdditionalFees)>
					<cfset QuerySetCell(strReturn.qryAdditionalFees,"additionalFees",strReturn.additionalFees)>
					<cfset QuerySetCell(strReturn.qryAdditionalFees,"additionalFeesExcTax",strReturn.additionalFeesExcTax)>
					<cfset QuerySetCell(strReturn.qryAdditionalFees,"additionalFeesTax",strReturn.additionalFeesTax)>
					<cfset QuerySetCell(strReturn.qryAdditionalFees,"additionalFeesRevTransDesc",strReturn.paymentFeeTypeID EQ 1 ? arguments.qryMerchantProfile.processFeeDonationRevTransDesc : "#strReturn.additionalFeesPct#% Credit Card Surcharge")>
					<cfset QuerySetCell(strReturn.qryAdditionalFees,"additionalFeesLabel",strReturn.additionalFeesLabel)>
					<cfset QuerySetCell(strReturn.qryAdditionalFees,"stateIDForTax",arguments.stateIDForTax)>
					<cfset QuerySetCell(strReturn.qryAdditionalFees,"zipForTax",arguments.zipForTax)>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset strReturn.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=strReturn)>
		</cfcatch>
		</cftry>

		<cfreturn strReturn>
	</cffunction>

	<cffunction name="getMemberPaymentProfileInfo" access="public" returnType="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="payProfileID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "data":{} }>

		<cfquery name="local.qryMemberPayProfile" datasource="#application.dsn.membercentral.dsn#">
			EXEC dbo.tr_getMemberPayProfiles
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
				@orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
				@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@limitToPayProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">;
		</cfquery>

		<cfif local.qryMemberPayProfile.recordCount>
			<cfset local.strMPP = structNew()>
			<cfset local.strMPP["mpprofileid"] = local.qryMemberPayProfile.profileID>
			<cfset local.strMPP["payprofileid"] = local.qryMemberPayProfile.payProfileID>
			<cfset local.strMPP["detail"] = local.qryMemberPayProfile.detail>
			<cfset local.strMPP["expiration"] = dateformat(local.qryMemberPayProfile.expiration,'mm/yy')>
			<cfset local.strMPP["nickname"] = local.qryMemberPayProfile.nickname>
			<cfset local.strMPP["bankaccounttype"] = len(local.qryMemberPayProfile.bankAccountType) ? local.qryMemberPayProfile.bankAccountType & " Checking" : "">
			<cfset local.strMPP["dateadded"] = "#datetimeformat(local.qryMemberPayProfile.dateAdded,'m/d/yy h:nn tt')#">
			<cfset local.strMPP["profilename"] = local.qryMemberPayProfile.profileName>
			<cfset local.strMPP["profileonscreenlabel"] = local.qryMemberPayProfile.tabTitle>
			<cfset local.strMPP["enablesurcharge"] = local.qryMemberPayProfile.enableSurcharge>
			<cfset local.strMPP["surchargeeligible"] = val(local.qryMemberPayProfile.surchargeEligible)>
			<cfset local.strMPP["surchargepercent"] = (local.qryMemberPayProfile.enableSurcharge eq 1 and IsNumeric(local.qryMemberPayProfile.surchargePercent) and local.qryMemberPayProfile.surchargePercent gt 0 and val(local.qryMemberPayProfile.surchargeEligible) eq 1) ? local.qryMemberPayProfile.surchargePercent : "">
			<cfset local.strMPP["gatewayclass"] = local.qryMemberPayProfile.gatewayClass>
			<cfset local.strMPP["tokenstore"] = local.qryMemberPayProfile.tokenStore>
			<cfset local.strMPP["cardtype"] = local.qryMemberPayProfile.cardType>

			<cfset local.returnStruct["data"] = local.strMPP>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getActiveMemberData" access="public" returntype="query" output="no">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var qryResult = ""/>

		<cfquery name="qryActiveMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @cofMemberID int;
			SELECT @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);

			SELECT membernumber, lastname + ', ' + firstname as fullname
			FROM dbo.ams_members
			WHERE memberID = @cofMemberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryActiveMember>
	</cffunction>

</cfcomponent>