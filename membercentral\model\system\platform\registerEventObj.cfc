<cfcomponent>

	<cffunction name="init" access="public" returntype="registerEventObj" hint="sets/resets all variables">
		<cfargument name="eventID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryEvent" datasource="#application.dsn.membercentral.dsn#">
			SELECT r.registrationID, e.GLAccountID, cl.contentTitle
			FROM dbo.ev_events as e
			INNER JOIN dbo.ev_registration as r on r.eventID = e.eventID and r.siteID = e.siteID and r.status = 'A'
			INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
			WHERE e.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>

		<cfset local.displayedCurrencyType = "">
		<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).showCurrencyType is 1>
			<cfset local.displayedCurrencyType = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCurrencyType>
		</cfif>

		<cfset this["eventID"] = arguments.eventID>
		<cfset this["registrationID"] = local.qryEvent.registrationID>
		<cfset this["eventTitle"] = local.qryEvent.contentTitle>
		<cfset this["eventGLAccountID"] = local.qryEvent.GLAccountID>
		<cfset this["displayedCurrencyType"] = local.displayedCurrencyType>
		<cfset this["performedByMemberID"] = 0>
		<cfset this["strData"] = structNew()>
		<cfset this["strData"]["s1"] = { memberid=0, stateIDforTax=0, zipForTax='', identificationMethod='' }>
		<cfset this["strData"]["s2"] = { rateid=0, rateGLAccountID=0, rateName='', rateAmt=0 }>
		<cfset this["strData"]["s3"] = { qrytotals=QueryNew("col1,col2,col3,col4,itemTax,totalTax,qty,sortOrder,GLToUse,itemType,itemID,itemOverrideID","varchar,varchar,varchar,double,double,double,integer,integer,integer,varchar,varchar,varchar") }>
		
		<cfreturn this>
	</cffunction>

	<cffunction name="setRegistrant" access="public" output="no" returntype="void">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="stateIDforTax" type="numeric" required="no" default="0">
		<cfargument name="zipForTax" type="string" required="no" default="">
		<cfargument name="identificationMethod" type="string" required="no" default="">

		<cfset var local = structNew()>

		<cfif arguments.memberID>
			<cfset this.strData.s1["memberID"] = arguments.memberID>
			
			<!--- passed in billing fields --->
			<cfif arguments.stateIDforTax AND len(arguments.zipForTax)>
				<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=arguments.zipForTax, billingStateID=arguments.stateIDForTax)>
				<cfif local.strBillingZip.isvalidzip>
					<cfset arguments.zipForTax = local.strBillingZip.billingzip>
				<cfelse>
					<cfthrow message="Invalid State/Zip.">
				</cfif>
			</cfif>
			
			<cfif arguments.stateIDforTax is 0 OR NOT len(arguments.zipForTax)>
				<cfset local.qryBilling = application.objMember.getMemberAddressByBillingAddressType(orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgid, memberID=arguments.memberID)>
			</cfif>
			<cfif arguments.stateIDforTax is 0>
				<cfset this.strData.s1["stateIDforTax"] = VAL(local.qryBilling.stateID)>
			<cfelse>
				<cfset this.strData.s1["stateIDforTax"] = arguments.stateIDforTax>
			</cfif>
			<cfif NOT len(arguments.zipForTax)>
				<cfset this.strData.s1["zipForTax"] = local.qryBilling.postalCode>
			<cfelse>
				<cfset this.strData.s1["zipForTax"] = arguments.zipForTax>
			</cfif>
			<cfset this.strData.s1["identificationMethod"] = arguments.identificationMethod>
		</cfif>
	</cffunction>

	<cffunction name="setRate" access="public" output="no" returntype="void">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfset var local = structNew()>
		
		<cfif arguments.rateID>
			<cfstoredproc procedure="ev_getRateByRateID" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.rateID#">
				<cfprocresult name="local.qryRate" resultset="1">
			</cfstoredproc>				

			<cfset this.strData.s2.rateID = arguments.rateID>
			<cfset this.strData.s2.rateGLAccountID = val(local.qryRate.GLAccountID)>
			<cfset this.strData.s2.rateName = local.qryRate.rateName>
			<cfset this.strData.s2.rateAmt = local.qryRate.rate>

			<cfif QueryAddRow(this.strData.s3.qrytotals)>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"col1","#this.eventTitle# - #local.qryRate.rateName#")>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"col2","")>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"col3","#dollarformat(local.qryRate.rate)# #this.displayedCurrencyType#")>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"col4",local.qryRate.rate)>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"itemTax",0)>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"totalTax",0)>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"qty",0)>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"sortOrder",1)>
				<cfif this.strData.s2.rateGLAccountID gt 0>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"GLToUse",this.strData.s2.rateGLAccountID)>
				<cfelse>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"GLToUse",this.eventGLAccountID)>
				</cfif>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"itemType","rate")>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"itemID",arguments.rateID)>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"itemOverrideID",0)>
			</cfif>

			<!--- Add in any forceSelect subEvents --->
			<cfquery name="local.qrySubEvents" datasource="#application.dsn.membercentral.dsn#">
				select cl.contentTitle as subEventTitle, r.rateName as subEventRateName, r.rateID as subEventRateID, subE.eventID as subEventID, 
					subR.registrationID as subEventRegistrationID, coalesce(r.glaccountID,subE.glAccountID) as useGLAccountID, 
					r.rate as subEventRate, se.sendStaffConfirmation, se.sendRegConfirmation,
					dbo.fn_ev_isUserRegisteredForEvent(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.strData.s1.memberID#">,subE.eventID) as isRegistered
				from dbo.ev_events as e
				inner join dbo.ev_subEvents as se on se.parenteventID = e.eventID 
					and se.forceSelect = 1
				inner join dbo.ev_events as subE on subE.eventID = se.eventID 
					and subE.status = 'A'
				inner join dbo.cms_contentLanguages as cl on cl.contentID = subE.eventContentID
					and cl.languageID = 1
				inner join dbo.ev_registration as subR on subR.eventID = subE.eventID 
					and subR.siteID = subE.siteID
					and subR.status = 'A'
					and getdate() between subR.startDate and subR.endDate
				inner join dbo.ev_rateMappings as rm on rm.subEventID = se.subEventID
					and rm.parentRateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="CF_SQL_INTEGER">
				inner join dbo.ev_rates as r on r.rateID = rm.subRateID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where e.eventID = <cfqueryparam value="#this.eventID#" cfsqltype="CF_SQL_INTEGER">
				order by cl.contentTitle, r.rateName
			</cfquery>		
			<cfloop query="local.qrySubEvents">
				<cfif local.qrySubEvents.isRegistered is 0 and QueryAddRow(this.strData.s3.qrytotals)>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"col1","&nbsp; &bull; #local.qrySubEvents.subEventTitle# - #local.qrySubEvents.subEventRateName#")>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"col2","")>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"col3","#dollarformat(local.qrySubEvents.subEventRate)# #this.displayedCurrencyType#")>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"col4",local.qrySubEvents.subEventRate)>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"itemTax",0)>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"totalTax",0)>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"qty",0)>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"sortOrder",1)>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"GLToUse",local.qrySubEvents.useGLAccountID)>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"itemType","subrate")>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"itemID","#local.qrySubEvents.subEventID#|#local.qrySubEvents.subEventRegistrationID#|#local.qrySubEvents.subEventRateID#|#local.qrySubEvents.useGLAccountID#|#local.qrySubEvents.sendStaffConfirmation#|#local.qrySubEvents.sendRegConfirmation#")>
					<cfset QuerySetCell(this.strData.s3.qrytotals,"itemOverrideID",0)>
				</cfif>
			</cfloop>
		</cfif>
	</cffunction>

	<cffunction name="prepForRegistration" access="private" output="no" returntype="void">
		<cfset var local = structNew()>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset this.performedByMemberID = this.strData.s1.memberID>
		<cfelse>
			<cfset this.performedByMemberID = session.cfcUser.memberData.memberID>
		</cfif>

		<!--- set GL to use and total tax --->
		<cfquery name="local.qryRateGLToUse" dbtype="query">
			select GLToUse
			from this.strData.s3.qrytotals
			where itemType = 'rate'
		</cfquery>
		<cfloop query="this.strData.s3.qrytotals">
			<cfif val(this.strData.s3.qrytotals.col4)>
				<cfif this.strData.s3.qrytotals.qty gt 0>
					<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=this.strData.s3.qrytotals.GLToUse, saleAmount="#this.strData.s3.qrytotals.col4/this.strData.s3.qrytotals.qty#", transactionDate=now(), stateIDForTax=this.strData.s1.stateIDForTax, zipForTax=this.strData.s1.zipForTax)>
					<cfset local.strTaxTotal = local.strTax.totalTaxAmt*this.strData.s3.qrytotals.qty>
				<cfelse>
					<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=this.strData.s3.qrytotals.GLToUse, saleAmount=this.strData.s3.qrytotals.col4, transactionDate=now(), stateIDForTax=this.strData.s1.stateIDForTax, zipForTax=this.strData.s1.zipForTax)>
					<cfset local.strTaxTotal = local.strTax.totalTaxAmt>
				</cfif>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"itemTax",local.strTax.totalTaxAmt,this.strData.s3.qrytotals.currentRow)>
				<cfset QuerySetCell(this.strData.s3.qrytotals,"totalTax",local.strTaxTotal,this.strData.s3.qrytotals.currentRow)>
			</cfif>
		</cfloop>
	</cffunction>

	<cffunction name="register" access="public" output="no" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfset prepForRegistration()>

			<cfquery name="local.qryRateGLToUse" dbtype="query">
				select GLToUse, itemTax
				from [this].strData.s3.qrytotals
				where itemType = 'rate'
			</cfquery>		

			<cfquery name="local.qrySubEvents" dbtype="query">
				select *
				from [this].strData.s3.qrytotals
				where itemType = 'subrate'
			</cfquery>

			<cfquery name="local.qrySaveData" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @invoiceID int, @rateID int, @orgID int, @siteID int, @loggedInMemberID int, @statsSessionID int,
							@GLAccountID int, @RateGLAccountID int, @EventGLAccountID int, @QuestionGLAccountID int, @registrantID int, 
							@rateTransactionID int, @detailID int, @paymentTransactionID int, 
							@paymentID int, @minTTAAutoID int, @allocTaxTID int, @allocSaleTID int, @allocAllocTID int, 
							@allocTransactionID int, @invoiceProfileID int, @minVID int, @deferredGLAccountID int, @subregistrantID int,
							@invoiceNumber varchar(18), @deferredDateStr varchar(10), @detail varchar(max), @invoiceIDList varchar(max),
							@nowDate datetime, @accrualDate datetime, @deferredDate datetime, @allocAmount decimal(18,2), @vAmount decimal(18,2),
							@xmlSchedule xml, @registrationMerchantProfiles varchar(1000), @amount decimal(18,2), @stateIDForTax int, 
							@zipForTax varchar(25), @taxAmount decimal(18,2);
					DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int, amount decimal(18,2));
						
					IF dbo.fn_ev_isUserRegisteredForEvent(#this.strData.s1.memberID#,#this.eventID#) = 1
						RAISERROR('Already registered for event.',16,1);

					set @nowDate = getdate();
					set @orgID = #application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID#;
					set @siteID = #application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID#;
					set @loggedInMemberID = #this.performedByMemberID#;
					set @statsSessionID = #val(session.cfcUser.statsSessionID)#;
					set @stateIDForTax = <cfif this.strData.s1.stateIDforTax>#this.strData.s1.stateIDforTax#<cfelse>null</cfif>;
					set @zipForTax = <cfif len(this.strData.s1.zipForTax)>'#this.strData.s1.zipForTax#'<cfelse>null</cfif>;

					set @EventGLAccountID = #this.eventGLAccountID#;

					select @accrualDate = et.startTime
						FROM dbo.ev_events as e 
						inner join dbo.ev_times as et on e.eventID = et.eventID   
						INNER JOIN dbo.sites as s on s.siteID = @siteid AND s.defaultTimeZoneID = et.timeZoneID
						WHERE e.eventID = #this.eventID#;

					-- this date is used if we need to create deferred entries. We will use the LATEST of event start date, nowdate.
					set @deferredDate = case when @accrualDate > @nowDate then @accrualDate else @nowDate end;
					set @deferredDateStr = convert(varchar(10),@deferredDate,101);


					BEGIN TRAN;

						<!--- rate --->
						set @rateID = #this.strData.s2.rateID#;
						set @detail = '#this.eventTitle# - #this.strData.s2.rateName#';
						set @GLAccountID = #local.qryRateGLToUse.GLToUse#;
						set @amount = #this.strData.s2.rateAmt#;

						select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
						select @registrationMerchantProfiles = dbo.sortedIntList(profileID) 
							from dbo.ev_rates r 
							inner join dbo.ev_registrationMerchantProfiles as regmp on regmp.registrationID = r.registrationID 
							where r.rateID = @rateID;

						-- create invoice for rate
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
							@assignedToMemberID=#this.strData.s1.memberID#, @dateBilled=@nowDate, @dateDue=@nowDate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);

						EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;

						-- add registrant
						EXEC dbo.ev_addRegistrant @registrationID=#this.registrationID#, @memberID=#this.strData.s1.memberID#, 
							@recordedOnSiteID=@siteid, @rateID=@rateID, @dateRegistered=@nowDate, @identificationMethod='#this.strData.s1.identificationMethod#',
							@registrantID=@registrantID OUTPUT;
						IF @registrantID = 0 RAISERROR('unable to add registrant',16,1);

						-- handle deferred revenue
						select @xmlSchedule = null, @deferredGLAccountID = null;
						select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
						IF @deferredGLAccountID is not null
							set @xmlSchedule = '<rows><row amt="' + cast(@amount as varchar(10)) + '" dt="' + @deferredDateStr + '" /></rows>';

						-- record rate transaction
						set @taxAmount = #val(local.qryRateGLToUse.itemTax)#;
						EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
							@assignedToMemberID=#this.strData.s1.memberID#, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=null, 
							@amount=@amount, @transactionDate=@nowDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, 
							@stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, 
							@bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@rateTransactionID OUTPUT;
							
						EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@rateTransactionID, 
							@itemType='Rate', @itemID=@registrantID, @subItemID=null;

						<!--- forceselect sub events --->
						<cfloop query="local.qrySubEvents">
							<cfset local.thisSubEventEventID = GetToken(local.qrySubEvents.itemID,1,"|")>
							<cfset local.thisSubEventRegistrationID = GetToken(local.qrySubEvents.itemID,2,"|")>
							<cfset local.thisSubEventRateID = GetToken(local.qrySubEvents.itemID,3,"|")>
							<cfset local.thisSubEventGLAccountID = GetToken(local.qrySubEvents.itemID,4,"|")>

							SELECT @subregistrantID = null, @accrualDate = null, @detail = null;

							-- add registrant and link to parent registrant
							EXEC dbo.ev_addRegistrant @registrationID=#local.thisSubEventRegistrationID#, 
								@memberID=#this.strData.s1.memberID#, @recordedOnSiteID=@siteid, @rateID=#local.thisSubEventRateID#, 
								@dateRegistered=@nowDate, @identificationMethod='#this.strData.s1.identificationMethod#',
								@registrantID=@subregistrantID OUTPUT;
							IF @subregistrantID = 0 RAISERROR('unable to add registrant',16,1);

							UPDATE dbo.ev_registrants
							SET parentRegistrantID = @registrantID
							where registrantID = @subregistrantID;

							select @invoiceProfileID = null, @invoiceID = null;
							set @GLAccountID = #local.thisSubEventGLAccountID#;
							set @amount = #val(local.qrySubEvents.col4)#;
							set @taxAmount = #val(local.qrySubEvents.itemTax)#;
							select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
							select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

							-- if necessary, create invoice assigned to payer based on invoice profile
							IF @invoiceID is null BEGIN
								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=#this.strData.s1.memberID#, @dateBilled=@nowDate, @dateDue=@nowDate, 
									@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

								insert into @tblInvoices (invoiceID, invoiceProfileID)
								values (@invoiceID, @invoiceProfileID);

								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@registrationMerchantProfiles;
							END

							select Top 1 @detail = cl.contentTitle, @accrualDate = et.startTime
							FROM dbo.ev_events as e 
							inner join dbo.ev_times as et on e.eventID = et.eventID   
							INNER JOIN dbo.sites as s on s.siteID = @siteid AND s.defaultTimeZoneID = et.timeZoneID
							INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
							WHERE e.eventID = #local.thisSubEventEventID#;

							select @detail = @detail + isnull(' - ' + rateName,'')
							from dbo.ev_rates 
							where rateID = #local.thisSubEventRateID#;

							-- this date is used if we need to create deferred entries. We will use the LATEST of event start date, nowdate.
							set @deferredDate = case when @accrualDate > @nowDate then @accrualDate else @nowDate end;
							set @deferredDateStr = convert(varchar(10),@deferredDate,101);

							-- handle deferred revenue
							select @xmlSchedule = null, @deferredGLAccountID = null, @rateTransactionID = null;
							select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
							IF @deferredGLAccountID is not null
								set @xmlSchedule = '<rows><row amt="' + cast(@amount as varchar(10)) + '" dt="' + @deferredDateStr + '" /></rows>';

							-- record rate transaction
							EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
								@assignedToMemberID=#this.strData.s1.memberID#, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=null, 
								@amount=@amount, @transactionDate=@nowDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, 
								@stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, @taxAmount=@taxAmount, 
								@bypassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@rateTransactionID OUTPUT;
								
							EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Events', @transactionID=@rateTransactionID, 
								@itemType='Rate', @itemID=@subregistrantID, @subItemID=null;
						</cfloop>

						-- close invoices
						select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) from @tblInvoices;
						EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=@invoiceIDList;
					COMMIT TRAN;

					SELECT @registrantID as registrantID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.strReturn.success = true>
			<cfset local.strReturn.registrantID = local.qrySaveData.registrantID>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.registrantID = 0>
		</cfcatch>
		</cftry>

		<!--- If success, send registrant and staff their versions --->
		<cfif local.strReturn.success>
			<cftry>
				<cfset local.objAdminEventReg = CreateObject("component","model.admin.events.eventReg")>
				<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode)>
				<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=local.mc_siteInfo.siteid)>

				<cfset local.strEmailConfirmation = local.objAdminEventReg.generateConfirmationEmail(registrantID=local.strReturn.registrantID, emailMode="registrant", emailCC=session.cfcUser.memberData.email, siteid=local.mc_siteInfo.siteid)>
				<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
					<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.mc_siteInfo.orgname, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.strEmailConfirmation.arrEmailTo,
						emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
						emailsubject=local.strEmailConfirmation.mailCollection.subject,
						emailtitle=local.strEmailConfirmation.emailTitle,
						emailhtmlcontent=local.strEmailConfirmation.emailcontent,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.strEmailConfirmation.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
						sendingSiteResourceID=local.eventAdminSiteResourceID
					)>
				</cfif>

				<cfset local.strEmailConfirmation = local.objAdminEventReg.generateConfirmationEmail(registrantID=local.strReturn.registrantID, emailMode="staff", siteid=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID)>
				<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
					<cfset application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.mc_siteInfo.orgname, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.strEmailConfirmation.arrEmailTo,
						emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
						emailsubject=local.strEmailConfirmation.mailCollection.subject,
						emailtitle=local.strEmailConfirmation.emailTitle,
						emailhtmlcontent=local.strEmailConfirmation.emailcontent,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.mc_siteInfo.sysMemberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
						sendingSiteResourceID=local.eventAdminSiteResourceID
					)>
				</cfif>
			
				<!--- conf for forceselect sub events --->
				<cfloop query="local.qrySubEvents">
					<cfset local.thisSubEventRegistrationID = GetToken(local.qrySubEvents.itemID,2,"|")>
					<cfset local.thisSubEventStaffConf = GetToken(local.qrySubEvents.itemID,5,"|")>
					<cfset local.thisSubEventRegConf = GetToken(local.qrySubEvents.itemID,6,"|")>

					<cfquery name="local.getRegInfo" datasource="#application.dsn.membercentral.dsn#">
						SELECT top 1 r.registrantID
						FROM dbo.ev_registrants r					
						INNER JOIN dbo.ams_members m on m.memberID = r.memberID
						WHERE r.registrationID = <cfqueryparam value="#local.thisSubEventRegistrationID#" cfsqltype="CF_SQL_INTEGER">
						AND r.memberID = <cfqueryparam value="#this.strData.s1.memberID#" cfsqltype="CF_SQL_INTEGER">
						AND r.recordedOnSiteID = <cfqueryparam value="#local.mc_siteInfo.siteID#" cfsqltype="CF_SQL_INTEGER">
						AND r.status = 'A'
					</cfquery>

					<cfif local.thisSubEventRegConf is 1>
						<cfset local.strEmailConfirmation = local.objAdminEventReg.generateConfirmationEmail(registrantID=local.getRegInfo.registrantID, emailMode="registrant", emailCC=session.cfcUser.memberData.email, siteid=local.mc_siteInfo.siteID)>

						<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
							<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=local.mc_siteInfo.orgname, email=local.mc_siteInfo.networkEmailFrom },
								emailto=local.strEmailConfirmation.arrEmailTo,
								emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
								emailsubject=local.strEmailConfirmation.mailCollection.subject,
								emailtitle=local.strEmailConfirmation.emailTitle,
								emailhtmlcontent=local.strEmailConfirmation.emailcontent,
								siteID=local.mc_siteInfo.siteID,
								memberID=local.strEmailConfirmation.memberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
								sendingSiteResourceID=local.eventAdminSiteResourceID
							)>
						</cfif>
					</cfif>

					<cfif local.thisSubEventStaffConf is 1>
						<cfset local.strEmailConfirmation = local.objAdminEventReg.generateConfirmationEmail(registrantID=local.getRegInfo.registrantID, emailMode="staff", siteid=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID)>
						<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
							<cfset application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=local.mc_siteInfo.orgname, email=local.mc_siteInfo.networkEmailFrom },
								emailto=local.strEmailConfirmation.arrEmailTo,
								emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
								emailsubject=local.strEmailConfirmation.mailCollection.subject,
								emailtitle=local.strEmailConfirmation.emailTitle,
								emailhtmlcontent=local.strEmailConfirmation.emailcontent,
								siteID=local.mc_siteInfo.siteID,
								memberID=local.mc_siteInfo.sysMemberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
								sendingSiteResourceID=local.eventAdminSiteResourceID
							)>
						</cfif>
					</cfif>
				</cfloop>
			
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>				
			</cftry>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>