<cfscript>
	variables.applicationReservedURLParams 	= "TestMode";
	local.customPage.baseURL				= "/?#getBaseQueryString(false)#";
	
	local.arrCustomFields = [];
	
	local.tmpField = { name="StaffConfirmationEmailFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="StaffConfirmationEmailTo", type="STRING", desc="who do we send member confirmations to", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="PaymentProfileCodeCC", type="STRING", desc="pay profile code for CC", value="NHCC" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="FirstYearPrice", type="DECIMAL2", desc="First Year in Practice", value="95" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="2to5YearPrice", type="DECIMAL2", desc="2 to 5 Years in Practice", value="175" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="6to10YearPrice", type="DECIMAL2", desc="6 to 10 Years in Practice", value="300" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="11YearPrice", type="DECIMAL2", desc="11+ Years in Practice", value="325" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="PublicSecPrice", type="DECIMAL2", desc="Public Sector Attorney", value="145" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="LawStudentPrice", type="DECIMAL2", desc="Law Student", value="45" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="ParalegalPrice", type="DECIMAL2", desc="Paralegal", value="85" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="RetiredAttorneysPrice", type="DECIMAL2", desc="Retired Attorneys", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="RetiredJudgesPrice", type="DECIMAL2", desc="Retired Judges", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="PlatinumPrice", type="DECIMAL2", desc="Platinum Member", value="565" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="SectionPrice", type="DECIMAL2", desc="Sections", value="25" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
	
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName = 'frmJoin',
		formNameDisplay = 'Membership Application',
		orgEmailTo = local.strPageFields.StaffConfirmationEmailTo,
		memberEmailFrom =  local.strPageFields.StaffConfirmationEmailFrom
	));	
	
	local.profile_1._profileCode 	= local.strPageFields.PaymentProfileCodeCC;
	local.profile_1._description 	= '#local.Organization# - #local.formNameDisplay#';
	
	local.USStates 					= application.objCustomPageUtils.mem_getStatesByCountry('United States');
	local.ReferralInfoStruct	 	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Referral Info");
	
	
	// GET MEMBER DATA: ------------------------------------------------------------------------------------------------------
	local.memberData 			= getMemberData(orgID=local.orgID,siteID=local.siteID,memberID=local.memberID);
	local.memberAddress 		= application.objMember.getMemberAddressByAddressType(orgID=local.orgID, memberid=local.useMID, addressType='Address');
	local.memberHomeAddress 	= application.objMember.getMemberAddressByAddressType(orgID=local.orgID, memberid=local.useMID, addressType='Home Address');
	local.memberPhone			= application.objMember.getMemberPhones(orgID=local.orgID, memberid=local.useMID);
	local.barDate				= local.memberData.customData['Bar Date'];
	local.barID					= local.memberData.customData['License Number'];
	local.politicalAfil		 	= local.memberData.customData['Political Party'];
	local.mentorProgram		 	= local.memberData.customData['Interested in Mentor Program'];
	local.website				= application.objMember.getMemberWebsites(memberid=local.useMID,websiteTypeID=9,orgID=local.orgID);
	
	local.objSubs				= CreateObject('component','model.admin.subscriptions.subscriptions');	
	
  /* SET PRORATED DUES RATES BY MONTH */

	local.strDues = structNew();
	
	/* DUES STRUCT PER TYPE */
	local.strDues.firstYr = structNew();
	local.strDues.firstYr.txt = 'First Year in Practice';
	local.strDues.firstYr.amt = local.strPageFields.FirstYearPrice;
	
	local.strDues.2to5Years = structNew();
	local.strDues.2to5Years.txt = '2 to 5 Years in Practice';
	local.strDues.2to5Years.amt = local.strPageFields.2to5YearPrice;
	
	local.strDues.6to10Years = structNew();
	local.strDues.6to10Years.txt = '6 to 10 Years in Practice';
	local.strDues.6to10Years.amt = local.strPageFields.6to10YearPrice;
	
	local.strDues.11PlusYears = structNew();
	local.strDues.11PlusYears.txt = '11+ Years in Practice';
	local.strDues.11PlusYears.amt = local.strPageFields.11YearPrice;
	
	local.strDues.publicService = structNew();
	local.strDues.publicService.txt = 'Public Sector Attorney'; 
	local.strDues.publicService.amt = local.strPageFields.PublicSecPrice;
		
	local.strDues.lawStudent = structNew();
	local.strDues.lawStudent.txt = 'Law Student';
	local.strDues.lawStudent.amt = local.strPageFields.LawStudentPrice;
	
	local.strDues.paralegal = structNew();
	local.strDues.paralegal.txt = 'Paralegal';
	local.strDues.paralegal.amt = local.strPageFields.ParalegalPrice;
	
	local.strDues.retired = structNew();
	local.strDues.retired.txt = 'Retired Attorneys';
	local.strDues.retired.amt = local.strPageFields.RetiredAttorneysPrice;
	
	local.strDues.judge = structNew();
	local.strDues.judge.txt = 'Retired Judges';
	local.strDues.judge.amt = local.strPageFields.RetiredJudgesPrice;
	
	local.strDues.platinum = structNew();
	local.strDues.platinum.txt = 'Platinum Member';
	local.strDues.platinum.amt = local.strPageFields.PlatinumPrice;
	
	local.strDues.sections = structNew();
	local.strDues.sections.txt = 'Sections';
	local.strDues.sections.amt = local.strPageFields.SectionPrice;
	
</cfscript>
<cfset local.cellPhone = '' />
<cfset local.faxPhone = '' />
<cfset local.phonePhone = '' />

 <cfloop query="local.data.phone">
	<cfif local.data.phone.phoneType eq 'cell'>
		<cfset local.cellPhone = local.data.phone.phone />
	</cfif>
	<cfif local.data.phone.phoneType eq 'Fax'>
		<cfset local.faxPhone = local.data.phone.phone />
	</cfif>
	<cfif local.data.phone.phoneType eq 'Phone'>
		<cfset local.phonePhone = local.data.phone.phone />
	</cfif>
</cfloop> 

<cfset local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID="2B5DD871-7094-42F5-B6FB-A8B481CC746C")>
<cfset local.hasSub = false>
<cfif event.getValue('msg','') neq 2 and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
	<cfset local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
</cfif>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">			
			
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##dbdedf; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; color:##000; }
			.frmButtons{ padding:5px 0; border-top:3px solid ##03608B; border-bottom:3px solid ##03608B; }
			
			.TitleText {font-size:16pt; color:##000; font-weight:bold; }
			.CPSection{ border:1px solid ##56718c; margin-bottom:15px; }
			.CPSectionTitle { font-size:12pt; height:20px; font-weight:bold; color:##000; padding:10px; background:##bbb; }
			.CPSectionContent{ padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##ececec; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##ececec; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##ececec;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold; }
			.subCPSectionText { font-size:8.5pt; }
			
			.info{ font-style:italic; font-size:7pt; color:##777; }
			.small{ font-size:7pt;}
			
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b { font-weight:bold; }
			
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			
			.tsAppBodyText { color:##000000;}
			select.tsAppBodyText{color:##666;}
			
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
						
		</style>
	</cfsavecontent>
	
	#local.pageJS#
	#local.pageCSS#
	
	<div id="customPage">
		<div class="TitleText c" style="padding-bottom:15px;">#local.Organization# - #local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			
			<cfcase value="0">
				
				<cfif event.getValue('msg',0) EQ "2">		
					<!--- Renewal form is not Open --->
					<div class="tsAppBodyText" >
						<strong>We've found an issue with your application. We apologize, but it is not available online. Please contact the NHAJ office by calling (603) 224-7077 or via email at <a href="mailto:#local.strPageFields.StaffConfirmationEmailTo#">#local.strPageFields.StaffConfirmationEmailTo#</a>.<br /><br />We apologize for the inconvenience. </strong><br /><br /><br />
					</div>
				<cfelse>
					
					<cfif local.hasSub eq true>
						<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no" />
					</cfif>
				
					<script type="text/javascript">
						function _FB_validateForm() {
							var thisForm = document.forms["#local.formName#"];
							var arrReq = new Array();
							var lastMtrxErr = '';
							
								if (thisForm.dues[0].checked ||thisForm.dues[1].checked || thisForm.dues[2].checked || thisForm.dues[3].checked || thisForm.dues[4].checked) {
								if (!_FB_hasValue(thisForm['lawpracticecertification'], 'CHECKBOX')) arrReq[arrReq.length]	= 'Law Practice Certification';
								if (!_FB_hasValue(thisForm['fname'], 'TEXT')) arrReq[arrReq.length] 						= 'First Name';
								if (!_FB_hasValue(thisForm['lname'], 'TEXT')) arrReq[arrReq.length] 						= 'Last Name';
								if (!_FB_hasValue(thisForm['email'], 'TEXT')) arrReq[arrReq.length] 						= 'Email Address';
								if (!_FB_hasValue(thisForm['admissionDate_new'], 'TEXT')) arrReq[arrReq.length] 			= 'Date of Admission to NH Bar';
								
							}
							
							if (arrReq.length > 0) {
								var msg = 'The following questions are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
							return true;
						}
						
						function assignMemberData(memObj){
							var thisForm = document.forms["#local.formName#"];
							var er_change = function(r) {
								var results = r;
								if( results.success ){
									thisForm['prefix'].value				= results.prefix;
									thisForm['memberNumber'].value 			= results.membernumber;
									thisForm['memberID'].value 				= results.memberid;
									thisForm['fname'].value 				= results.firstname;
									thisForm['mname'].value 				= results.middlename;
									thisForm['lname'].value 				= results.lastname;
									thisForm['suffix'].value 				= results.suffix;
									thisForm['profSuffix'].value			= results.professionalsuffix;
									thisForm['firmName'].value				= results.company;
									thisForm['firmaddress'].value 			= results.address1;
									thisForm['firmcity'].value 				= results.city;
									thisForm['firmstate'].value 			= results.statecode;
									thisForm['firmzip'].value 				= results.postalcode;
									thisForm['phone'].value			 		= results.phone;
									thisForm['email'].value 				= results.email;
									thisForm['website'].value				= results.website;
									
									if ( results['bar date'] ) {
										var barDate = new Date( results['bar date'] );
										if( !isNaN( barDate ) ) {
											var offset = barDate.getTimezoneOffset() * 60 * 1000;
											barDate = new Date(barDate.getTime() + offset);
										} else {
											barDate = "";
										}
										thisForm['admissionDate_new'].value = (barDate.getMonth()+1) + "/" + barDate.getDate() + "/" + barDate.getFullYear();
										checkBarDate(thisForm['admissionDate_new']);
									}
									thisForm['barID'].value 				= results['license number'];
									
									if ( results['political party'] ){
										var polRadioDem 	= $('##pol[value="Democrat"]');
										var polRadioRep 	= $('##pol[value="Republican"]');
										var polRadioInd 	= $('##pol[value="Independent"]');
										var polRadioOther = $('##pol[value="Other"]');
										
										switch( results['political party'] ){
											case "Democrat":
												polRadioDem.prop("checked",true);
											break;
											case "Republican":
												polRadioRep.prop("checked",true);
											break;
											case "Independent":
												polRadioInd.prop("checked",true);
											break;
											case "Other":
												polRadioOther.prop("checked",true);
											break;
											default:
											break;
										}
									}
									
									if ( results['interested in mentor program'] ){
										if ( results['interested in mentor program'] == 'Yes, as a Mentee' ){
											thisForm['mentorProg'].checked	= true;
										}
									}									
									
									// un hide form   
									document.getElementById('formToFill').style.display 			= '';
								}
								else{ /*alert('not success');*/ }
							};
							
							var arrKeys = ["Bar Date","License Number","Political Party","Interested in Mentor Program"] ;
							var objParams = { memberNumber:memObj.memberNumber, customfields:arrKeys };
							TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
						}
												
						function loadMember(memNumber){
							var objParams = { memberNumber:memNumber };
							assignMemberData(objParams);
						}
						
						function checkPayments(control){
							var prompt 			= document.getElementById('payments');
							var paymentsTR		= document.getElementById('paymentsTr');
							var paymentsSelect	= document.getElementById('paymentOptions');
							
							if (control == 'hide') {
								paymentsTR.style.display = 'none';
								prompt.checked 	= false;
								prompt.disabled = true;	
							}
							
							if (control == 'show'){
								paymentsTR.style.display = '';
								prompt.disabled = false;	
							}
							
							if (prompt.checked == true){
								paymentsTR.style.display = '';
							} else {
								paymentsTR.style.display = 'none';
								paymentsSelect.value = '';
							}
							
						}
						
						function checkPracticeCert() {
							var rdo = $('input[name="dues"]');
							if (rdo[0].checked || rdo[1].checked || rdo[2].checked || rdo[3].checked || rdo[4].checked || rdo[9].checked){
								document.getElementById('LawPracticeCert').style.display = '';
							} else {																																	
								document.getElementById('LawPracticeCert').style.display = 'none';
							}	 
						}
						
						function countPracticeCodes(){
							var practiceCodes = document.getElementsByName('sections');
							var practiceCodesCount = 0;
							
							for(var i=0; i < practiceCodes.length; i++){
								if ( practiceCodes[i].checked == true ) practiceCodesCount++;
							}
							
							if (practiceCodesCount > 8) {
								alert('You have selected too many Practice Codes. \n Please select eight(8) or less.');
								for(var j=0; j < practiceCodes.length; j++){
									if ( practiceCodes[j].checked == true ) practiceCodes[j].checked = false;
								}
							}
						}						
						
						function checkBarDate(x){
							
							var barDate 	= new Date(x.value);
							var barYear   	= barDate.getFullYear();
							var barMonth	= barDate.getMonth() + 1;
							var barDay 	 	= barDate.getDate();
							
							var currDate  	= new Date();
							var currYear 	= currDate.getFullYear();
							var currMonth	= currDate.getMonth() + 1;
							var currDay		= currDate.getDate();
							
							var yearDiff = currYear - barYear;
							
							if (barYear){
								if ( yearDiff == 0 ){
									selectMemberType(1);
								}
								
								if ( yearDiff == 1 ){
									if( barMonth > currMonth ){
										//less than one year
										selectMemberType(1);
									}
									else {
										//2 - 5 years	
										selectMemberType(2);
									}
									
								}
								
								if ( yearDiff >= 2 && yearDiff <=4 ){
									//2 - 5 years	
									selectMemberType(2);
								}
								
								if ( yearDiff == 5 ){
									if( barMonth > currMonth ){
										//2 - 5 years
										selectMemberType(2);
									}
									else if ( barMonth == currMonth ) {
										if( barDay > currDay ){
											//2 - 5 years
											selectMemberType(2);
										}
										else{ 
											//6 - 10 years
											selectMemberType(3);
										}
									}
									else {
										//6 - 10 years	
										selectMemberType(3);
									}
									
								}
								
								if ( yearDiff > 5 && yearDiff <=9 ){
									//6 - 10 years	
									selectMemberType(3);
								}
								
								if ( yearDiff == 10 ){
									if( barMonth > currMonth ){
										//6 - 10 years
										selectMemberType(3);
									}
									else if ( barMonth == currMonth ) {
										if( barDay > currDay ){
											//6 - 10 years
											selectMemberType(3);
										}
										else{ 
											//11+ years
											selectMemberType(4);
										}
									}
									else {
										//11+ years	
										selectMemberType(4);
									}
									
								}
								
								if ( yearDiff > 10 ){
									//11+ years	
									selectMemberType(4);
								}
							}
						}
							
						
						function selectMemberType(toSelect){
							var memberRadios = $('[name="dues"]');
							
							//disable all radios except Associate member levels
							memberRadios[0].disabled 	= true;
							memberRadios[0].checked 	= false;
							memberRadios[1].disabled 	= true;
							memberRadios[1].checked 	= false;
							memberRadios[2].disabled 	= true;
							memberRadios[2].checked 	= false;
							memberRadios[3].disabled 	= true;
							memberRadios[3].checked 	= false;
							memberRadios[4].checked 	= false;
							memberRadios[5].checked 	= false;
							memberRadios[6].checked 	= false;
							memberRadios[7].checked 	= false;
							
							//enable and select appropriate one
							
							switch(toSelect){
								case 1:
									memberRadios[0].disabled 	= false;
									memberRadios[0].checked 	= true;
								break;
								case 2:
									memberRadios[1].disabled 	= false;
									memberRadios[1].checked 	= true;
								break;
								case 3:
									memberRadios[2].disabled 	= false;
									memberRadios[2].checked 	= true;
								break;
								case 4:
									memberRadios[3].disabled 	= false;
									memberRadios[3].checked 	= true;
								break;
								case 0:
									memberRadios[0].disabled 	= false;
									memberRadios[0].checked 	= false;
									memberRadios[1].disabled 	= false;
									memberRadios[1].checked 	= false;
									memberRadios[2].disabled 	= false;
									memberRadios[2].checked 	= false;
									memberRadios[3].disabled 	= false;
									memberRadios[3].checked 	= false;
									memberRadios[4].disabled 	= false;
									memberRadios[4].checked 	= false;
									memberRadios[5].disabled 	= false;
									memberRadios[5].checked 	= false;
									memberRadios[6].disabled 	= false;
									memberRadios[6].checked 	= false;
									memberRadios[7].disabled 	= false;
									memberRadios[7].checked 	= false;
								break;
							}
							checkPracticeCert();
						}
						
						function clearPol(){
							var polRadio = $('input[name="pol"]');
							polRadio.each(function(){
									$(this).prop('checked',false);
							});
						}
						
						$(document).ready(function(){
						
							$('##allSections').click(function(){
								var isChecked = $("##allSections").is(':checked');
								calcDues('allSections');
								if(isChecked){	
									$('.sectionGroups').each(function() {
										$(this).prop('checked', false);
									});									
									$("input.sectionGroups").attr("disabled", true);						
								}
								else{
									$("input.sectionGroups").removeAttr("disabled");
									$("##sectionsTotalSpan").html("$0.00");
								}
							});	
													
						});	
						
					</script>
					<div class="r i frmText">*Denotes required field</div>
					<cfform name="#local.formName#" id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
						<input type="hidden" name="isSubmitted" value="1" />
						<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
						<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />						
						
						<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
							<div class="CPSection">
								<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
								<div class="frmRow1" style="padding:10px;">
									<table cellspacing="0" cellpadding="2" border="0" width="100%">
										<tr>
											<td width="175" class="c">
												<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
													<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
												</div>
											</td>
											<td>
												<span class="frmText">
													<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
													<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
													<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
													<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
												</span>
											</td>
										</tr>
									</table>
								</div>
							</div>
						</cfif>
						
							<div id="formToFill" <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>style="display:none;"<cfelse>style="display:;"</cfif>>
							
							<div class="CPSection" style="padding:10px;">
							<table  cellpadding="5" style="width:50%;margin-left:25%;" cellspacing="0" border="0" align="center">
								<tr>
									<td colspan="2" align="center" class="HeaderText" bgcolor="##CCCCCC">Membership Profile</td>
								</tr>
								<tr><td></td></tr>
								<tr>
									<td align="right" class="BodyText" width="40%">Prefix</td>
									<td>
										<select name="prefix" id="prefix">
											<option value=""> - Please Select - </option>
											<option value="Chief Justice">Chief Justice</option>
											<option value="Commissioner">Commissioner</option>
											<option value="Dean">Dean</option>
											<option value="Dr.">Dr.</option>
											<option value="Gov.">Gov.</option>
											<option value="Hon.">Hon.</option>
											<option value="Mr.">Mr.</option>
											<option value="Ms.">Ms.</option>
											<option value="Prof.">Prof.</option>
											<option value="Rep.">Rep.</option>
											<option value="Sen.">Sen.</option>
										</select>
									</td>
								</tr>
								<tr>
									<td align="right" class="BodyText" width="40%">First Name*</td>
									<td><cfinput type="text" class="BodyText" size="25" name="fname"  id="fname" required="yes" message="Please enter your first name" value="#Session.cfcuser.memberData.firstname#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Middle Name or Initial</td>
									<td><cfinput class="BodyText" type="text" size="25" name="mname"  id="mname" value="#left(Session.cfcuser.memberData.middlename,1)#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Last Name*</td>
									<td><cfinput class="BodyText" type="text" size="25" name="lname"  id="lname" required="yes" message="Please enter your last name" value="#Session.cfcuser.memberData.lastname#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText" width="40%">Suffix</td>
									<td><cfinput type="text" class="BodyText" size="25" name="suffix"  id="suffix" required="no" value="#Session.cfcuser.memberData.suffix#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText" width="40%">Professional Suffix</td>
									<td><cfinput type="text" class="BodyText" size="25" name="profSuffix"  id="profSuffix" required="no" value="#Session.cfcuser.memberData.professionalsuffix#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Firm Name*</td>
									<td><cfinput class="BodyText" size="25" name="firmName"  id="firmName" required="yes" message="Please enter your firm name" value="#Session.cfcuser.memberData.company#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Firm Address*</td>
									<td><cfinput class="BodyText" size="35" name="firmaddress"  id="firmaddress" required="yes" message="Please enter your Firm address" value="#local.memberAddress.address1#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">City*</td>
									<td><cfinput class="BodyText" size="25" name="firmcity"  id="firmcity" required="yes" message="Please enter your Firm city"  value="#local.memberAddress.city#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">State*</td>
									<td>
										<select name="firmstate" class="tsAppBodyText">
											<option value = ''> - Please Select - </option>
											<cfloop query="local.USStates">
												<option value="#local.USStates.code#"<cfif local.USStates.code EQ local.memberAddress.stateCode> SELECTED</cfif>>#local.USStates.Name#</option>
											</cfloop>
										</select>
									</td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Zip* (plus four)</td>
									<td><cfinput class="BodyText" size="12" name="firmzip"  id="firmzip" required="yes" message="Please enter your Firm zip" value="#local.memberAddress.postalCode#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Firm Email*</td>
									<td><cfinput class="BodyText" size="25" name="email"  id="email" required="yes" message="Please enter your email address" value="#Session.cfcuser.memberData.email#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Firm Phone*</td>
									<td><cfinput class="BodyText" size="25" name="phone"  id="phone" required="yes" message="Please enter your Phone number" value="" validate="telephone" /> <small>(xxx-xxx-xxxx)</small></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Mobile Phone</td>
									<td><cfinput class="BodyText" size="25" name="cellphone"  id="cellphone" value="#local.cellPhone#" validate="telephone" /> <small>(xxx-xxx-xxxx)</small></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Firm Fax</td>
									<td><cfinput class="BodyText" size="25" name="fax"  id="fax" value="#local.faxPhone#" validate="telephone" /> <small>(xxx-xxx-xxxx)</small></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Website</td>
									<td><cfinput class="BodyText" size="25" name="website"  id="website" value="#local.website.website#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText" colspan="2"><hr></td>
								</tr>								
								<tr>
									<td align="right" class="BodyText">Home Address*</td>
									<td><cfinput class="BodyText" size="35" name="homeaddress"  id="homeaddress" required="yes" message="Please enter your home address" value="#local.memberHomeAddress.address1#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">City*</td>
									<td><cfinput class="BodyText" size="25" name="homecity"  id="homecity" required="yes" message="Please enter your home city" value="#local.memberHomeAddress.city#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">State*</td>
									<td>
										<select name="homestate" class="tsAppBodyText">
											<option value = ''> - Please Select - </option>
											<cfloop query="local.USStates">
												<option value="#local.USStates.code#"<cfif local.USStates.code EQ local.memberHomeAddress.stateCode> SELECTED</cfif>>#local.USStates.Name#</option>
											</cfloop>
										</select>
									</td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Zip* (plus four)</td>
									<td><cfinput class="BodyText" size="12" name="homezip"  id="homezip" required="yes" message="Please enter your home zip" value="#local.memberHomeAddress.postalCode#"></td>
								</tr>
								<tr>
								<tr>
									<td align="right" class="BodyText">Home Email</td>
									<td><cfinput class="BodyText" size="25" name="homeEmail"  id="homeEmail" value="#Session.cfcuser.memberData.email#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Home Phone</td>
									<td><cfinput class="BodyText" size="25" name="homePhone"  id="homePhone" value="#local.phonePhone#" validate="telephone" /> <small>(xxx-xxx-xxxx)</small></td>
								</tr>
								
								<tr>
									<td align="right" class="BodyText"> How referred?</td>
									<td>
										<select name="referralInfo" class="tsAppBodyText">
											<option value = ''> - Please Select - </option>
											<cfloop array="#local.ReferralInfoStruct.COLUMNVALUEARR#" index="refObj">
												<option value="#refObj.COLUMNVALUESTRING#" >#refObj.COLUMNVALUESTRING#</option>
											</cfloop>
										</select>
									</td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Date of Admission to NH Bar*</td>
									<td>
										<cfset local.columnName = 'admissionDate' />
										<cfset local.showClear = true />
										<cfset local.clearOnSameLine = true />
										
										<cfinput type="hidden" name="#local.columnName#_old"  id="#local.columnName#_old" value="">
										<cfif local.showClear AND NOT local.clearOnSameLine>
											<nobr>
										</cfif>
										<cfinput class="tsAppBodyText" type="text" name="#local.columnName#_new" id="#local.columnName#_new" value="#local.barDate#" required="yes" message="Please select Date of Admission to NH Bar" onChange="checkBarDate(this);" autocomplete="off" size="16">
										<cfif local.showClear>
											<cfinput type="button" class="tsAppBodyButton" name="btnClear#local.columnName#"  id="btnClear#local.columnName#" value="clear" onClick="selectMemberType(0);">
										</cfif>
										<cfif local.showClear AND NOT local.clearOnSameLine>
											</nobr>
										</cfif>
										<cfsavecontent variable="local.datejs">
											<script language="javascript">
												$(function() { 
													mca_setupDatePickerField('#local.columnName#_new'); 
													$("##btnClear#local.columnName#").click( function(e) { mca_clearDateRangeField('#local.columnName#_new');return false; } );
												});
											</script>
											<style type="text/css">
											###local.columnName#_new { 
												background-image:url("/assets/common/images/calendar/monthView.gif"); 
												background-position:right center; background-repeat:no-repeat; }
											</style>
										</cfsavecontent>
										<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">	
									</td>
								</tr>
								<tr>
									<td align="right" class="BodyText">Bar ID Number</td>
									<td><cfinput class="BodyText" size="12" name="barID" id="barID" value="#local.barID#"></td>
								</tr>
								<tr>
									<td align="right" class="BodyText" valign="top">Political Party Affiliation (Optional):</td>
									<td nowrap>
										<cfinput type="radio" name="pol"  id="pol" value="Democrat" checked="#local.politicalAfil eq 'Democrat'#"> Democrat
										<cfinput type="radio" name="pol"  id="pol" value="Republican" checked="#local.politicalAfil eq 'Republican'#"> Republican 
										<cfinput type="radio" name="pol"  id="pol" value="Independent" checked="#local.politicalAfil eq 'Independent'#"> Independent
										<cfinput type="radio" name="pol"  id="pol" value="Other" checked="#local.politicalAfil eq 'Other'#"> Other
										<cfinput type="button" class="tsAppBodyButton" name="btnClear_pol"  id="btnClear_pol" value="clear" onClick="clearPol();">
									</td>
								</tr>
								<tr>
									<td colspan="2" align="center" valign="top" style="font-size: 8pt;">*Information will not be published or shared with any commercial entities.</td>
									<tr>
										<td align="right" valign="top"><cfinput type="checkbox" name="websitelisting"  id="websitelisting" value="Yes"></td>
										<td class="BodyText">Please add my Bio to the directory listing for and extra <strong>$100</strong> fee.  NHAJ will contact you about submitting a 200 Word or less Biographical text to your profile. (Fee is waived with payment of <a href="##voluntary">Platinum Dues</a>)</td>
									</tr>
									<tr>
										<td align="right" valign="top"><cfinput type="checkbox" name="mentorProg"  id="mentorProg" value="Yes" checked="#local.mentorProgram eq 'Yes, as a mentee'#"></td>
										<td class="BodyText"><strong>Yes!</strong> I would like information on the NHAJ mentor program.</td>
									</tr>
									<tr>
										<td colspan="2" align="center" class="HeaderText" bgcolor="##CCCCCC">Member Dues</td>
									</tr>
									<tr>
										<td align="center" colspan="2" class="BodyText"><i>Membership dues are based on NH Bar admission date.<i></td>
									</tr>
									<tr>
										<td align="center" colspan="2">
											<table cellpadding="2" border="0" cellspacing="0">
												<tr>
													<td class="BodyText" style="font-weight:bold;">Member Annual Dues</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="FirstYr" required="yes" message="Please select your dues schedule." onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10">#local.strDues.firstYr.txt# - #dollarFormat(local.strDues.firstYr.amt)#</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="Two-FiveYears" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.2to5Years.txt# - #dollarFormat(local.strDues.2to5Years.amt)#</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="Six-TenYears" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.6to10Years.txt# - #dollarFormat(local.strDues.6to10Years.amt)#</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="ElevenOrMoreYears" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.11PlusYears.txt# - #dollarFormat(local.strDues.11PlusYears.amt)#</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="PublicService" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.publicService.txt# - #dollarFormat(local.strDues.publicService.amt)#</td>
												</tr>
												<tr>
													<td><hr /></td>
												</tr>
												<tr>
													<td class="BodyText" style="font-weight:bold;">Associate Member Annual Dues</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="LawStudent" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.lawStudent.txt# - #dollarFormat(local.strDues.lawStudent.amt)#</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="Paralegals" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.paralegal.txt# - #dollarFormat(local.strDues.paralegal.amt)#</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="Retired" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.retired.txt# - #dollarFormat(local.strDues.retired.amt)#</td>
												</tr>
												<tr>
													<td class="BodyText"><cfinput type="radio" name="dues"  id="dues" value="Judge" onClick="checkPracticeCert();" /><img src="/images/spacer.gif" width="10" />#local.strDues.judge.txt# - #dollarFormat(local.strDues.judge.amt)#</td>
												</tr>
												<tr>
													<td><cfinput type="checkbox" name="voices"  id="voices" value="Yes"> <strong>Yes!</strong> Please send me information on the <i>Voices for Justice</i> advocacy fund.</td>
												</tr>
												<tr>
													<td><a name="voluntary"></a><hr /></td>
												</tr>
												<tr>
													<td colspan="2" align="center" class="HeaderText" bgcolor="##CCCCCC">OPTIONAL BENEFITS AND SAVINGS</td>
												</tr>
												<tr>
													<td class="BodyText">
														<div><strong><br> PLATINUM MEMBERS</strong><br>Platinum members receive these additional benefits:
															<ul>
																<li style="padding-bottom:3px;">Up to 8 free practice area listings in our online Attorney Directory together with a link to the firm website.</li>
																<li style="padding-bottom:3px;">Client referrals through our Platinum member public referral program.</li>
																<li style="padding-bottom:3px;">50% off the already reduced member pricing for NHAJ CLEs.</li>
																<li style="padding-bottom:3px;">Recognition in the Trial Bar News. </li>
															</ul>
															<cfinput type="radio" name="dues"  id="dues" value="platinum" onClick="checkPracticeCert();"><img src="/images/spacer.gif" width="10"><strong>Platinum Dues</strong> - $#local.strPageFields.PlatinumPrice#
														</div>
														<br>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</div>
							<div class="CPSection" style="padding:10px;">
								<table style="width:50%;margin-left:25%;" cellpadding="5" cellspacing="0" border="0" align="center">
									<tr>
										<td align="center" class="HeaderText" bgcolor="##CCCCCC">Membership Attestation *</td>
									</tr>
									<tr>
										<td>
											<table cellpadding="0" cellspacing="0" border="0" width="100%">
												<tr>
													<td class="BodyText">
														I certify that I support the mission of NHAJ to protect constitutional rights and make sure people have a fair chance to receive justice through the legal system when they have been harmed by the acts of others.<br>
													</td>
												</tr>
												<tr>
													<td class="BodyText">
														<cfinput type="checkbox" name="memAttestation"  id="memAttestation" value="I agree." required="Yes"  message="You must agree to become a member."  /><img src="/images/spacer.gif" width="10">I Agree.<br  /><small>Agreeing indicates compliance with certification statement above</small><br /><br />
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</div>
							<div id="LawPracticeCert"  style="display:none;">
								<div class="CPSection" style="padding:10px;">
									<table style="width:50%;margin-left:25%;" cellpadding="5" cellspacing="0" border="0" align="center">
										<tr>
											<td align="center" class="HeaderText" bgcolor="##CCCCCC">Law Practice Certification Required for Members *</td>
										</tr>
										<tr>
											<td>
												<table cellpadding="0" cellspacing="0" border="0" width="100%">
													<tr>
														<td class="BodyText">
															I certify that the representation of defendants (or their insurers) in personal injury matters and/or employers (or their insurers) in employment matters constitutes no more than 10% of my practice.<br>
														</td>
													</tr>
													<tr>
														<td class="BodyText">
															<cfinput type="radio" name="lawpracticecertification"  id="lawpracticecertification" value="I agree." /><img src="/images/spacer.gif" width="10">I Agree.&nbsp;&nbsp;&nbsp;<cfinput type="radio" name="lawpracticecertification"  id="lawpracticecertification" value="I do not agree."><img src="/images/spacer.gif" width="10">I Do Not Agree.<br  /><small>Agreeing indicates compliance with certification statement above</small><br /><br />
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
							</div>	
							<div class="CPSection" style="padding:10px;">
								<table style="width:50%;margin-left:25%;" cellpadding="5" cellspacing="0" border="0" align="center">
									<tr>
										<td align="center" class="HeaderText" bgcolor="##CCCCCC">Listserv Attestation *</td>
									</tr>
									<tr>
										<td>
											<table cellpadding="0" cellspacing="0" border="0" width="100%">
												<tr>
													<td class="BodyText">
														NHAJ has five member listservs and four Peer Groups. Please indicate below which listservs and Peer Groups you would like to join.
														<br>
														Participation in any Peer Group and any Peer Group meeting is open to any NHAJ member subject to approval by the peer group chair, completing and signing the relevant attestation, and abiding by the policies of the listservs outlined in the NHAJ by-laws.
														<br>
														Each Peer Group meets semi-annually to discuss case presentation ideas, developing trends, relevant legislative topics, and provide opportunities for each member to become better advocates for their clients within their practice area.
														<br>
														<br>
													</td>
												</tr>
												<tr>
													<td class="BodyText">
														<cfinput type="checkbox" name="memberList"  id="memberList" value="Yes" /><img src="/images/spacer.gif" width="10"><strong>NHAJ General Member Listserv</strong> <a href="https://www.nhaj.org/docDownload/820321">CLICK HERE FOR ATTESTATION.</a><br>
													</td>
												</tr>
												<tr>
													<td class="BodyText">
														<cfinput type="checkbox" name="mednegList"  id="mednegList" value="Yes" /><img src="/images/spacer.gif" width="10"><strong>Medical Negligence Listserv and Peer Group</strong><br>
													</td>
												</tr>
												<tr>
													<td class="BodyText">
														<cfinput type="checkbox" name="wcList"  id="wcList" value="Yes" /><img src="/images/spacer.gif" width="10"><strong>Workers' Compensation Listserv and Peer Group</strong><br>
													</td>
												</tr>
												<tr>
													<td class="BodyText">
														<cfinput type="checkbox" name="flList"  id="flList" value="Yes" /><img src="/images/spacer.gif" width="10"><strong>Family Law Listserv and Peer Group</strong><br>
													</td>
												</tr>
												<tr>
													<td class="BodyText">
														<cfinput type="checkbox" name="mvaList"  id="mvaList" value="Yes" /><img src="/images/spacer.gif" width="10"><strong>General PI/MVA Listserv and Peer Group</strong><br>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</div>
							<div class="CPSection" style="padding:10px;">
								<table style="width:50%;margin-left:25%;" cellpadding="0" border="0" cellspacing="0" width="100%">
									<tr>
										<td bgcolor="##CCCCCC" align="center" colspan="2" class="HeaderText">Practice Areas</td>
									</tr>								
									<tr>
										<td bgcolor="##CCCCCC" align="center" colspan="2" class="BodyText">Please select up to eight practice areas to list in our online Attorney Directory.<font color="red"><br><i>Listings $25/each (free for platinum members).</i></font></td>
									</tr>
								
									<tr>
										<td colspan="2">
											
												<tr>
													<td><strong>Business Litigation</strong></td>
													<td><strong>Government</strong></td>
												</tr>
												<tr>
													<td width="50%" class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Bankruptcy">Bankruptcy</td>
													<td width="50%" class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Eminent Domain">Eminent Domain</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Business Formation and Dissolution">Business Formation and Dissolution</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Government Relations">Government Relations</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Contract Disputes">Contract Disputes</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Municipal Matters">Municipal Matters</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="General">General</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="School Liability">School Liability</td>
												</tr>
												<tr>
													<td><strong>Civil Litigation</strong></td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Social Security & Disability">Social Security & Disability</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Administrative Law">Administrative Law</td>
													<td><strong>Insurance</strong></td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Appeals">Appeals</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Insurance Defense">Insurance Defense</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Arbitration/Mediation/Neutral Evaluation">Arbitration, Mediation,Neutral Evaluation </td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Liens/Subrogation/ERISA">Liens, Subrogation & ERISA</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Civil Litigation">Bankruptcy</td>
													<td><strong>Intellectual Property</strong></td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Class Actions">Class Actions</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Intellectual Property Matters">Intellectual Property Matters</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Consumer Protection">Consumer Protection</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Patents/Trademarks/Copyrights">Patents, Trademarks & Copyrights</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Debt Collections">Debt Collections</td>
													<td><strong>Property & Land Use</strong></td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="General">General</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Boundaries and Easements">Boundaries and Easements</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Small Claims">Small Claims</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Landlord-Tenant">Landlord-Tenant</td>
												</tr>
												<tr>
													<td><strong>Civil Rights</strong></td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Real Estate Transactions">Real Estate Transactions</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Americans With Disabilities Act">Americans With Disabilities Act</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Zoning and Land Use">Zoning and Land Use</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Education/Special Education">Education/Special Education</td>
													<td><strong>Personal Injury/Torts</strong></td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Civil Rights- General">Gender Discriminations</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Aircraft Accidents">Aircraft Accidents</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="General">General</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Boating Accidents">Boating Accidents</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Police Misconduct">Police Misconduct</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Brain Injury">Brain Injury</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Racial Discrimination">Racial Discrimination</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Construction Accidents">Construction Accidents</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Section 1983">Section 1983</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Environmental & Toxic Torts">Environmental & Toxic Torts</td>
												</tr>
												<tr>
													<td><strong>Criminal</strong></td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="General">General</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Appeals">Appeals</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Intentional Torts">Intentional Torts</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Defense">Defense</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Lead Paint Injuries">Lead Paint Injuries</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="DUI/DWI">DUI/DWI</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Legal Malpractice">Legal Malpractice</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Federal & White Collar Crimes">Federal & White Collar Crimes</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Motor Vehicle Accidents">Motor Vehicle Accidents</td>
												</tr>
												<tr>
													<td><strong>Employment & Workers' Compensation</strong></td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Negligent Torts">Negligent Torts</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Discrimination">Discrimination</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Nursing Homes">Nursing Homes</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="General">General</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Police Misconduct">Police Misconduct</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Governmental Employees">Governmental Employees</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Premises Liability">Premises Liability</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Non-Compete Agreements">Non-Compete Agreements</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Product Liability">Product Liability</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Sexual Harassment">Sexual Harassment</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Profesional Malpractice - Dental">Profesional Malpractice - Dental</td>
												</tr>	
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Workers Compensation">Workers' Compensation</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Professional Malpractice- Legal">Professional Malpractice- Legal</td>
												</tr>	
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Wrongful Termination">Wrongful Termination</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Professional Malpractice- Medical">Professional Malpractice- Medical</td>
												</tr>
												<tr>
													<td><strong>Family Law</strong></td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Professional Malpractice- Psychiatric">Professional Malpractice- Psychiatric</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Adoption & Guardianship">Adoption & Guardianship</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Property Damage">Property Damage</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Child Custody & Parenting">Child Custody & Parenting</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Sexual Assault">Sexual Assault</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Divorce and Separation">Divorce and Separation</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Slip & Fall">Slip & Fall</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Domestic Violence">Domestic Violence</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Wrongful Death">Wrongful Death</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Elder Law">Elder Law</td>
													<td><strong>Trusts and Estates</strong></td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="General">General</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Probate/Wills/Trusts">Probate, Wills, Trusts</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Paternity">Paternity</td>
													<td><strong>Other</strong></td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Post Divorce Matters">Post Divorce Matters</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Collaborative Law">Collaborative Law</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Prenuptual Agreements">Prenuptual Agreements</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Estate Planning">Estate Planning</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;">&nbsp;</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Foreclosure Defense">Foreclosure Defense</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;">&nbsp;</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Investigations/Training/Policy Development">Investigations, Training, Policy Development</td>
												</tr>
												<tr>
													<td class="BodyText" style="font-size:10px;">&nbsp;</td>
													<td class="BodyText" style="font-size:10px;"><cfinput type="checkbox" name="sections"  id="sections" onClick="countPracticeCodes();" value="Multi-District Litigation">Multi-District Litigation</td>
												</tr>
											</td>
										</tr>
									<tr>
										<td align="center" colspan="2" class="BodyText" style="font-size:11px; font-style:italic;">** 80% of your membership dues are tax deductible.</td>
									</tr>
								</table>
							</div>
												
							<div id="formButtons">
								<div style="padding:10px;">
									<div align="center" class="frmButtons">
										<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
									</div>
								</div>
							</div>
											
						</div><!--//  formToFill -->
					</cfform>
	
					<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
					</cfif>
				</cfif>				
			</cfcase>
			
			<cfcase value="1">
				<cfif application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=event.getValue('memberID'), typeID=local.membershipDuesTypeID)>
					<cflocation url="#local.customPage.baseURL#&msg=2" addtoken="no">
				</cfif>

				<cfscript>
					
					local.profile_1.strPaymentForm = 	application.objPayments.showGatewayInputForm(
																			siteid=local.siteID,
																			profilecode=local.profile_1._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopupDIVName='paymentTable'
																		);
				</cfscript>
				<script type="text/javascript">
					function checkPaymentMethod() {
						var rdo = document.forms["#local.formName#"].payMeth;
						if (rdo.checked) {//credit card
							document.getElementById('CCInfo').style.display = '';
							document.getElementById('CheckInfo').style.display = 'none';
						}  
						/*else if (rdo[1].checked) {//check
							document.getElementById('CCInfo').style.display = 'none';
							document.getElementById('CheckInfo').style.display = '';
						} */ 
						
					}
					
					function getMethodOfPayment() {
						var btnGrp = document.forms['#local.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					
					function _validate() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#local.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement		= thisForm['confirmationStatement'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}						
						
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
				
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
				</script>
				<cfif len(local.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
				</cfif>
				
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
									and left(local.key,4) neq "fld_">
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
							<div>
								
								<div class="CPSection">
									<div class="CPSectionTitle">*Method of Payment</div>
									<div class="P">
										<table cellpadding="2" cellspacing="0" width="100%" border="0">
											<tr valign="top">
												<td colspan="2">Please select your preferred method of payment from the options below.</td>
											</tr>
											<tr>
												<td>
													<table cellpadding="2" cellspacing="0" width="100%" border="0">
														<tr>
															<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}"></td>
															<td>Credit Card</td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</div>
								</div>
								
								<div id="CCInfo" style="display:none;" class="CPSection">
									<div class="CPSectionTitle">Credit Card Information</div>
									<div class="PL PR frmText paymentGateway BT BB">
										<cfif len(local.profile_1.strPaymentForm.inputForm)>
											<div>#local.profile_1.strPaymentForm.inputForm#</div>
										</cfif>
									</div>
									
									<div class="P">
										<div class="PB">* Please confirm the statement below:</div>
										<table width="100%">
											<tr>
												<td width="25"><input name="confirmationStatement" id="confirmationStatement"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
												<td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
											</tr>
										</table>
									</div>
									
									<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">AUTHORIZE</button></div>
								</div>
							
								<div id="CheckInfo" style="display:none;" class="CPSection">
									<div class="CPSectionTitle">Check Information</div>
									<div class="P">
										
												Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
												<strong>New Hampshire Association for Justice</strong><br />
												10 Ferry Street ##311<br />
												Concord, NH 03301
												
									</div>
									<div class="P">
										<button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button><br />
										<br />
										<strong>NOTE:</strong> Your membership will not be active until payment is received.
									</div>
								</div>
								
							</div>
							<cfinclude template="/model/cfformprotect/cffp.cfm" />
						</cfform>
					</div>
				</div>
			</cfcase>
			
			<cfcase value="2">
				
				<!--- CHECK FOR SPAM SUBMISSION: -------------------------------------------------------------------------------- --->
				<cfif NOT local.objCffp.testSubmission(form)>
					<!--- This submission is spam --->
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>				
				
				<cfscript>
					switch(event.getValue('dues','')){					
						case 'FirstYr':  			 		 local.membershipCategory 	= local.strDues.firstYr.txt;				local.membershipDues	=	local.strDues.firstYr.amt; 				break;
						case 'Two-FiveYears':  		 local.membershipCategory 	= local.strDues.2to5Years.txt; 			local.membershipDues	=	local.strDues.2to5Years.amt;			break;
						case 'Six-TenYears':   		 local.membershipCategory 	= local.strDues.6to10Years.txt; 		local.membershipDues	=	local.strDues.6to10Years.amt;			break;
						case 'ElevenOrMoreYears':  local.membershipCategory		= local.strDues.11PlusYears.txt; 		local.membershipDues	=	local.strDues.11PlusYears.amt;		break;
						case 'PublicService':  		 local.membershipCategory 	= local.strDues.publicService.txt; 	local.membershipDues	=	local.strDues.publicService.amt;	break;
						case 'LawStudent':  			 local.membershipCategory 	= local.strDues.lawStudent.txt; 		local.membershipDues	=	local.strDues.lawStudent.amt;			break;
						case 'Paralegals':  			 local.membershipCategory 	= local.strDues.paralegal.txt; 			local.membershipDues	=	local.strDues.paralegal.amt;			break;
						case 'Retired':						 local.membershipCategory  	= local.strDues.retired.txt; 				local.membershipDues	=	local.strDues.retired.amt;				break;
						case 'Judge': 						 local.membershipCategory  	= local.strDues.judge.txt; 					local.membershipDues	=	local.strDues.judge.amt;					break;
						case 'platinum':				 local.membershipCategory   = local.strDues.platinum.txt;			local.membershipDues  = local.strDues.platinum.amt;			break;
					}
					
						local.addtlDues = 0;
					if ( event.getValue('websitelisting','No') eq 'Yes' ){
						local.addtlDues = 100;	
					}
					
					local.sectionDues = 0;
					local.sectionCount = 0;
					if ( event.getValue('sections','') neq '' ){
						local.sectionCount = listLen(event.getValue('sections',''));
						if ( local.sectionCount gt 0 ){
							local.sectionDues = local.sectionCount * local.strDues.sections.amt;	
						}
					}
					if ( event.getValue('dues','') eq 'platinum' ){
						local.sectionDues = 0;	
					}
					
					local.totalAmount = local.membershipDues + local.sectionDues + local.addtlDues;
				</cfscript>
				
				<cfset local.timeStamp 			= now() />
				<cfset local.savedAccounting 	= false />
				
				<cfsavecontent variable="local.name">
					#event.getValue('firstName','')# #event.getValue('lastName','')#
				</cfsavecontent>
				
				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					<!-- @msg@ -->
					<!-- @profile_1.ccResponse@ -->
					<p>#local.formNameDisplay# submitted on #dateformat(local.timeStamp,"dddd, m/d/yyyy")# #timeformat(local.timeStamp,"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
					
					<tr class="msgHeader"><td colspan="2" class="b">Contact Information</td></tr>
						<tr class="frmRow1"><td class="frmText b">MemberNumber:</td><td class="frmText">#event.getValue('memberNumber')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Prefix:</td><td class="frmText">#event.getValue('prefix','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('fName','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Middle Name/Initial:</td><td class="frmText">#event.getValue('mName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lName','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Suffix:</td><td class="frmText">#event.getValue('suffix','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Professional Suffix:</td><td class="frmText">#event.getValue('profSuffix','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm Name:</td><td class="frmText">#event.getValue('firmName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Firm Address 1:</td><td class="frmText">#event.getValue('firmaddress','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm City:</td><td class="frmText">#event.getValue('firmcity','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Firm State:</td><td class="frmText">#event.getValue('firmstate','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm Zip:</td><td class="frmText">#event.getValue('firmzip','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Firm Email:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm Phone:</td><td class="frmText">#event.getValue('phone','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Mobile Phone:</td><td class="frmText">#event.getValue('cellphone','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Firm Fax:</td><td class="frmText">#event.getValue('fax','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Website:</td><td class="frmText">#event.getValue('website','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Home Address 1:</td><td class="frmText">#event.getValue('homeaddress','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Home City:</td><td class="frmText">#event.getValue('homecity','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Home State:</td><td class="frmText">#event.getValue('homestate','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Home Zip:</td><td class="frmText">#event.getValue('homezip','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Home Email:</td><td class="frmText">#event.getValue('homeemail','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Home Phone:</td><td class="frmText">#event.getValue('homephone','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">How referred?:</td><td class="frmText">#event.getValue('referralInfo','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Bar Date:</td><td class="frmText">#event.getValue('admissionDate_new','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Bar ID Number:</td><td class="frmText">#event.getValue('barID','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Political Party Affiliation:</td><td class="frmText">#event.getValue('pol','')#&nbsp;</td></tr>
						<cfif event.getValue('websiteListing','No') eq 'Yes'>
							<tr class="frmRow2"><td class="frmText b">Add my Bio to Directory:</td><td class="frmText">#event.getValue('websiteListing','No')# - #dollarFormat(100)#&nbsp;</td></tr>
						<cfelse>
							<tr class="frmRow1"><td class="frmText b">Add my Bio to Directory:</td><td class="frmText">#event.getValue('websiteListing','No')#&nbsp;</td></tr>
						</cfif>
						<tr class="frmRow2"><td class="frmText b">Mentor Program:</td><td class="frmText">#event.getValue('mentorProg','')#&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">Membership Category</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.membershipCategory#</td><td class="frmText">#dollarFormat(local.membershipDues)#&nbsp;</td></tr>
						<cfif event.getValue('platinumDues','no') neq 'no'>
							<tr class="frmRow2"><td class="frmText b">Platinum Membership</td><td class="frmText">#dollarFormat(local.platinumDues)#&nbsp;</td></tr>
						</cfif>	
						<tr class="frmRow1"><td class="frmText b">Voices for Justice information:</td><td class="frmText">#event.getValue('voices','No')#&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">Membership Attestation</td></tr>
						<tr class="frmRow1"><td class="frmText b">I certify that I support the mission of NHAJ:</td><td class="frmText">#event.getValue('memAttestation','')#&nbsp;</td></tr>

						<tr class="msgHeader"><td colspan="2" class="b">Listserv Attestation</td></tr>
						<tr class="frmRow1"><td class="frmText b">NHAJ General Member Listserv:</td><td class="frmText">#event.getValue('memberList','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Medical Negligence Listserv and Peer Group:</td><td class="frmText">#event.getValue('mednegList','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Workers' Compensation Listserv and Peer Group:</td><td class="frmText">#event.getValue('wcList','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Family Law Listserv and Peer Group:</td><td class="frmText">#event.getValue('flList','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">General PI/MVA Listserv and Peer Group:</td><td class="frmText">#event.getValue('mvaList','No')#&nbsp;</td></tr>
												
						<cfif event.getValue('sections','') neq ''>
							<tr class="msgHeader"><td colspan="2" class="b">Practice Areas</td></tr>
							<tr class="frmRow1"><td colspan="2" class="frmText">#event.getValue('sections','None')#&nbsp;</td></tr>
							<tr class="frmRow1"><td colspan="2" class="frmText">#dollarFormat(local.sectionDues)#&nbsp;</td></tr>
						</cfif>						
						
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr class="msgHeader"><td colspan="2">PAYMENT INFORMATION</td></tr>
						<tr><td class="frmText b frmRow1">Payment Type: </td><td class="frmText"><cfif event.getValue('payMeth','CC') EQ 'CC'>Credit Card<cfelse>Check</cfif></td></tr>
						<tr class="frmRow2"><td class="frmText b">Payment Amount: </td><td class="frmText">#dollarFormat(local.totalAmount)#</td></tr>
						
					</table>
				</cfsavecontent>
				
				<cfset local.profile_1.amountToCharge = local.totalAmount />
				<cfset local.profile_1.strTotal = structNew() />
				<cfset local.profile_1.strTotal = {
									total 			= local.profile_1.amountToCharge, 
									attempted 	= 0, 
									success 		= 0, 
									profileCode = local.profile_1._profileCode, 
									description = '#local.profile_1._description#'
				} />

				<cfquery name="local.qryFirmState" dbtype="query">
					select stateID
					from [local].USStates
					where code = '#arguments.event.getTrimValue('firmstate','')#'
				</cfquery>
				<cfquery name="local.qryHomeState" dbtype="query">
					select stateID
					from [local].USStates
					where code = '#arguments.event.getTrimValue('homestate','')#'
				</cfquery>

				<cfswitch expression="#event.getValue('dues','')#">
					<cfcase value="FirstYr,Two-FiveYears,Six-TenYears,ElevenOrMoreYears,Platinum">
						<cfset local.contactTypeColumnValue = 'Attorney' />
					</cfcase>
					<cfcase value="PublicService">
						<cfset local.contactTypeColumnValue = 'Public Sector Attorney' />
					</cfcase>
					<cfcase value="LawStudent">
						<cfset local.contactTypeColumnValue = 'Student' />
					</cfcase>
					<cfcase value="Paralegals">
						<cfset local.contactTypeColumnValue = 'Paralegal' />
					</cfcase>
					<cfcase value="Retired">
						<cfset local.contactTypeColumnValue = 'Retired Attorney' />
					</cfcase>
					<cfcase value="Judge">
						<cfset local.contactTypeColumnValue = 'Retired Judge' />
					</cfcase>
				</cfswitch>

				<!--- Update Member --->
				<cftry>
				 	<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getValue('memberID'))>
					<cfset local.objSaveMember.setDemo(prefix=arguments.event.getTrimValue('prefix',''), firstName=arguments.event.getTrimValue('fname',''),
						middleName=arguments.event.getTrimValue('mname',''), lastName=arguments.event.getTrimValue('lname',''),
						suffix=arguments.event.getTrimValue('suffix',''), professionalsuffix=arguments.event.getTrimValue('profSuffix',''), 
						company=arguments.event.getTrimValue('firmName',''))>
					<cfset local.objSaveMember.setMemberType(memberType='User')>
					<cfset local.objSaveMember.setMemberStatus(memberStatus='Active')>
					<cfset local.objSaveMember.setAddress(type='Address', address1=arguments.event.getTrimValue('firmaddress',''), 
						city=arguments.event.getTrimValue('firmcity',''), stateID=local.qryFirmState.stateID, 
						postalCode=arguments.event.getTrimValue('firmzip',''))>
					<cfset local.objSaveMember.setPhone(addresstype='Address', type='Phone', value=arguments.event.getTrimValue('phone',''))>	
					<cfset local.objSaveMember.setPhone(addresstype='Address', type='Fax', value=arguments.event.getTrimValue('Fax',''))>	
					<cfset local.objSaveMember.setPhone(addresstype='Address', type='Cell', value=arguments.event.getTrimValue('cellphone',''))>
					<cfset local.objSaveMember.setAddress(type='Home Address', address1=arguments.event.getTrimValue('homeaddress',''), 
						city=arguments.event.getTrimValue('homecity',''), stateID=local.qryHomeState.stateID, 
						postalCode=arguments.event.getTrimValue('homezip',''))>
					<cfset local.objSaveMember.setPhone(addresstype='Home Address', type='Phone', value=arguments.event.getTrimValue('homephone',''))>		
					<cfset local.objSaveMember.setWebsite(type='Website', value=arguments.event.getTrimValue('website',''))>					
					<cfset local.objSaveMember.setEmail(type='Email', value=arguments.event.getTrimValue('email',''))>		
					<cfset local.objSaveMember.setEmail(type='Home Email', value=arguments.event.getTrimValue('homeEmail',''))>
					<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.contactTypeColumnValue)>
					<cfif event.getValue('dues','') eq 'Platinum'>
						<cfset local.objSaveMember.setCustomField(field='Platinum', value='1')>
					</cfif>
					<cfif event.getValue('admissionDate_new','') neq ''>
						<cfset local.objSaveMember.setCustomField(field='Bar Date', value=event.getTrimValue('admissionDate_new',''))>
					</cfif>
					<cfif event.getValue('barID','') neq ''>				
						<cfset local.objSaveMember.setCustomField(field='License Number', value=event.getTrimValue('barID',''))>
					</cfif>
					<cfif event.getValue('pol','') neq ''>					
						<cfset local.objSaveMember.setCustomField(field='Political Party', value=event.getTrimValue('pol',''))>
					</cfif>
					<cfif event.getValue('mentorProg','') neq ''>					
						<cfset local.objSaveMember.setCustomField(field='Interested in Mentor Program', value='Yes, as a Mentee')>
					</cfif>
					<cfif event.getValue('referralInfo','') neq ''>					
						<cfset local.objSaveMember.setCustomField(field='Referral Info', value='#event.getValue('referralInfo','')#')>
					</cfif>
					<cfset local.strResult = local.objSaveMember.saveData()>					
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
						<hr />
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=local.memberEmail.from },
                            emailto=[{ name="", email=local.memberEmail.to }],
                            emailreplyto=local.ORGEmail.to,
                            emailsubject=local.memberEmail.SUBJECT,
                            emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & local.formNameDisplay,
                            emailhtmlcontent=local.mailContent,
                            siteID=local.siteID,
                            memberID=val(arguments.event.getValue('memberID')),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

				<cfset local.emailSentToUser = local.responseStruct.success>
				
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							#local.name# was not sent email confirmation due to bad Data.<br />
							Please contact, and let them know.
							<hr />
						</cfif>
						
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.toEmailArr = listToArray(local.ORGEmail.to.replaceAll(',',';'),';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
					if (arrayLen(local.arrEmailTo)) {
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.subject,
							emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
							emailhtmlcontent=local.mailContent,
							siteID=arguments.event.getValue('mc_siteinfo.siteID'),
							memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						);
					}
				</cfscript>

				<!--- relocate to message page --->
				<cfset session.invoice = local.invoice />
				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			
			<cfcase value="99">
				<!--- output to screen --->
				<div class="HeaderText">Thank you for submitting your application!</div>
				<br/>
				<div>This page has been emailed to the email address on file. If you would like you could also print the page out as a receipt.</div>
				<br />
				<div class="BodyText">
					#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
				</div>
				<cfset session.invoice = "" />
					
			</cfcase>
			
			<cfcase value="100">
					<div>
						Error! you Can't Post Here.
					</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>

<!--- CUSTOM FUNCTIONS ====================================================================== --->
<cffunction name="getMemberAdditionalData" access="public" returntype="struct">
	<cfargument name="orgID" type="numeric" required="yes">
	<cfargument name="memberID" type="numeric" required="yes">

	<cfset var local					 	= structNew()>
	<cfset local.objMember					= CreateObject("component","model.admin.members.members") />
	<cfset local.xmlAdditionalData_Member = local.objMember.getMemberAdditionalData(memberid=arguments.memberid) />
	<cfset local.xmlAdditionalData 			= application.objCustomPageUtils.mem_getOrgAdditionalDataColumns(arguments.orgID) />
	<cfset local.returnStruct 				= StructNew()>
	<cfloop array="#local.xmlAdditionalData.data.XMlChildren#" index="local.column">
		<cfset local.memberColDataActualValue = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@actualColumnValue)")>
		<cfset local.memberColDataValue 			= XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@columnValue)")>
		<cfset local.memberColDataValueID 		= XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@valueID)")>
		<cfset local.columnName 							= local.column.xmlAttributes.columnName />
		<cfset local.returnStruct[local.columnName] = local.memberColDataValue>
	</cfloop>
	<cfreturn local.returnStruct />
</cffunction>

<cffunction name="getMemberData" access="private" returntype="struct">
	<cfargument name="orgID" type="numeric" required="true">
	<cfargument name="siteID" type="numeric" required="true">
	<cfargument name="memberID" type="numeric" required="true">
	
	<cfset var local = structNew() />
	<cfset local.memberData = structNew()>
	
	<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=arguments.orgID)>
	<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.orgID)>

	<cfset local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID = arguments.orgID)>
	
	<cfscript>
		local.returnStruct 				= structNew();
		
		local.objMember 				= CreateObject("component","model.admin.members.members");
		local.memberData.qryMember	=  application.objMember.getMemberInfo(int(val(arguments.memberID)),arguments.orgID);
		local.memberData.qryMemberEmails	=  application.objMember.getMemberEmails(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);
		local.memberData.qryMemberWebsites	=  application.objMember.getMemberWebsites(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);
		local.memberData.qryMemberAddresses	=  local.objMember.getMember_addresses(memberID=int(val(arguments.memberID)),orgID=arguments.orgID);

		local.customMemberData			= getMemberAdditionalData(orgID=arguments.orgID,memberID=arguments.memberID);
	</cfscript>
	
	<cfsavecontent variable="local.memberNamePrinted">
		<cfoutput>
		<cfif local.qryOrgMemberFields.hasprefix is 1 and len(local.memberData.qryMember.prefix)>#local.memberData.qryMember.prefix#</cfif> 
		#local.memberData.qryMember.firstname# 
		<cfif local.qryOrgMemberFields.hasmiddlename is 1 and len(local.memberData.qryMember.middlename)>#local.memberData.qryMember.middlename#</cfif> 
		#local.memberData.qryMember.lastname# 
		<cfif local.qryOrgMemberFields.hassuffix is 1 and len(local.memberData.qryMember.suffix)>#local.memberData.qryMember.suffix#</cfif> 
		<cfif local.qryOrgMemberFields.hasprofessionalsuffix is 1 and len(local.memberData.qryMember.professionalsuffix)>#local.memberData.qryMember.professionalsuffix#</cfif>
		</cfoutput>
	</cfsavecontent>
	
	<cfsavecontent variable="local.memberName">
		<cfoutput>
		#local.memberData.qryMember.firstname# #local.memberData.qryMember.lastname# 
		</cfoutput>
	</cfsavecontent>
	
	<cfsavecontent variable="local.memberEmails">
		<cfoutput>
			<cfloop query="local.qryOrgEmails">
				<cfset local.tmpEmailTypeID = local.qryOrgEmails.emailTypeID>
				<cfquery name="local.qryEmailInfo" dbtype="query">
					select email
					from [local].memberData.qryMemberEmails
					where emailTypeID = #local.tmpEmailTypeID#
				</cfquery>		
				#local.qryOrgEmails.emailType#: <cfif len(local.qryEmailInfo.email)>#local.qryEmailInfo.email#<cfelse><span class="dim"><i>not specified</i></span></cfif><br/>
			</cfloop>
		</cfoutput>
	</cfsavecontent>
	
	<cfsavecontent variable="local.memberWebsites">
		<cfoutput>
			<cfloop query="local.qryOrgWebsites">
				<cfset local.tmpWebsiteTypeID = local.qryOrgWebsites.WebsiteTypeID>
				<cfquery name="local.qryWebsiteInfo" dbtype="query">
					select Website
					from [local].memberData.qryMemberWebsites
					where WebsiteTypeID = #local.tmpWebsiteTypeID#
				</cfquery>		
				#local.qryOrgWebsites.WebsiteType#: <cfif len(local.qryWebsiteInfo.Website)>#local.qryWebsiteInfo.Website#<cfelse><span class="dim"><i>not specified</i></span></cfif><br/>
			</cfloop>
		</cfoutput>
	</cfsavecontent>
	
	<cfloop query="local.qryOrgAddresses">
		<cfset local.tmpAddressTypeID	= local.qryOrgAddresses.addressTypeID />
		<cfset local.tmpAddressType 	= local.qryOrgAddresses.addressType />
		<cfset local.hasAttn 			= local.qryOrgAddresses.hasAttn />
		<cfset local.hasAddress2 		= local.qryOrgAddresses.hasAddress2 />
		<cfset local.hasAddress3 		= local.qryOrgAddresses.hasAddress3 />
		<cfset local.hasCounty 			= local.qryOrgAddresses.hasCounty />
		<cfquery name="local.qryAddressInfo" dbtype="query">
			select attn, address1, address2, address3, city, stateCode, postalcode, county, country
			from [local].memberData.qryMemberAddresses
			where addressTypeID = #local.tmpAddressTypeID#
		</cfquery>	
		<cfsavecontent variable="local.thisaddrFull">
			<cfoutput>
				<cfif local.hasAttn and len(local.qryAddressInfo.attn)>#local.qryAddressInfo.attn#<br/></cfif>
				<cfif len(local.qryAddressInfo.address1)>#local.qryAddressInfo.address1#<br/></cfif>
				<cfif local.hasAddress2 and len(local.qryAddressInfo.address2)>#local.qryAddressInfo.address2#<br/></cfif>
				<cfif local.hasAddress3 and len(local.qryAddressInfo.address3)>#local.qryAddressInfo.address3#<br/></cfif>
				<cfif len(local.qryAddressInfo.city)>#local.qryAddressInfo.city#</cfif> 
				<cfif len(local.qryAddressInfo.stateCode)>#local.qryAddressInfo.stateCode#</cfif> 
				<cfif len(local.qryAddressInfo.postalcode)>#local.qryAddressInfo.postalcode#</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfif local.hasCounty and len(local.qryAddressInfo.county)>
			<cfsavecontent variable="local.thisaddrFull">
				<cfoutput><cfif len(local.thisaddrFull)>#local.thisaddrFull#<br/></cfif>#local.qryAddressInfo.county#</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfif len(local.qryAddressInfo.country)>
			<cfsavecontent variable="local.thisaddrFull">
				<cfoutput><cfif len(local.thisaddrFull)>#local.thisaddrFull#<br/></cfif>#local.qryAddressInfo.country#</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfsavecontent variable="local.thisaddrPhonesFull">
			<cfoutput>
				<table>
					<cfloop query="local.qryOrgPhones">
						<cfset local.phoneTypeID = local.qryOrgPhones.phoneTypeID>
						<cfquery name="local.qryPhoneInfo" dbtype="query">
							select phone
							from [local].memberData.qryMemberAddresses
							where addressTypeID = #local.tmpAddressTypeID#
							and phoneTypeID = #local.phoneTypeID#
						</cfquery>	
						<cfif len(local.qryPhoneInfo.phone)>
							<tr valign="top">
								<td class="tsAppBodyText frmText">#local.qryOrgPhones.phoneType#: &nbsp;</td>
								<td class="tsAppBodyText frmText">#local.qryPhoneInfo.phone#</td>
							</tr>
						</cfif>
					</cfloop>
				</table>
			</cfoutput>
		</cfsavecontent>
	</cfloop>
	<cfset local.returnStruct['Email']				= local.memberData.qryMemberEmails.email />
	<cfset local.returnStruct['memberName'] 		= local.memberName />
	<cfset local.returnStruct['memberNamePrinted'] 	= ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL")>
	<cfset local.returnStruct['company']			= local.memberData.qryMember.company />
	<cfset local.returnStruct['memberEmails'] 		= local.memberEmails />
	<cfset local.returnStruct['memberWebsites'] 	= local.memberWebsites />
	<cfset local.returnStruct['tmpAddressType']		= local.tmpAddressType />
	<cfset local.returnStruct['thisaddrFull'] 		= local.thisaddrFull />
	<cfset local.returnStruct['thisaddrPhonesFull'] = local.thisaddrPhonesFull />
	<cfset local.returnStruct['customData']			= local.customMemberData />
	<cfset local.returnStruct['qryOrgMemberFields']	= local.qryOrgMemberFields />
	
	<cfreturn local.returnStruct />
</cffunction>