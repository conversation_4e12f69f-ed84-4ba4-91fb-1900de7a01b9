<cfcomponent output="false">
    <cffunction name="getCertMergeCodes" access="public" output="false" returntype="struct">
		<cfscript>
			var strCertMergeCodes = {
				"member": "prefix,firstname,middlename,lastname,suffix,professionalsuffix"
			};
			return strCertMergeCodes;
		</cfscript>
	</cffunction>

    <cffunction name="generateCertBody" access="public" output="false" returntype="string">
		<cfargument name="strCertMergeCodes" type="struct" required="true">

		<cfset var local = structNew()>
        <cfoutput>
            
            <cfsavecontent variable="local.registrantName">
                <cfoutput>#UCASE(arguments.strCertMergeCodes.member.prefix & " " & arguments.strCertMergeCodes.member.firstname & " " & arguments.strCertMergeCodes.member.middlename & " " & arguments.strCertMergeCodes.member.lastname & " " & arguments.strCertMergeCodes.member.suffix & " " & arguments.strCertMergeCodes.member.professionalsuffix)#</cfoutput>
            </cfsavecontent>
            
            <cfset local.creditContent = ""/>
            <cfset local.strCustomFieldValue = ""/>
            <cfif structKeyExists(arguments.strCertMergeCodes.event.qryEventCustomFields, "customvalue")>
                <cfquery name="local.qryEventDetails" dbtype="query">
                    SELECT customvalue as eventDetails
                    FROM arguments.strCertMergeCodes.event.qryEventCustomFields
                    WHERE UID = 'B5F0BECA-7A07-4BA9-87B4-2D93836CD436'
                </cfquery>
                
                <cfif local.qryEventDetails.recordCount>
                    <cfset local.strCustomFieldValue = local.qryEventDetails.eventDetails/>
                </cfif>
            </cfif>
          
        </cfoutput>
        <cfsavecontent variable="local.data">    
            <cfoutput>
                <html>
                    <head>
                        <title>CLE Certificate of Attendance</title>
                        <style>	
                            html, body { width:8in; padding:0; margin: 0; }
                            body{
                                font-style: normal;                   
                                color: ##000000;
                                margin:0px;    
                            }
                            table td, table td * {
                                vertical-align: top;
                            }
                            .text-center{
                                text-align:center;
                            }
                            .text-left{
                                text-align:left;
                            }
                            .text-right{
                                text-align:right;
                            }
                            .b{
                                font-weight:bold;
                            }
                            .i{
                                font-style:italic;
                            }
                            .logo{

                            }
                            .headingl{
                                font-size:17px;
                                font-family: Arial;
                                margin-bottom:2px;

                            }

                            .subheadingl{
                                font-size:20px;
                                font-family: Arial;
                                margin-bottom:3px;
                                margin-top:3px;

                            }
                            .mb4{
                                margin-bottom:4px;
                            }
                            .mt2{
                               margin-top:2px; 
                            }
                            .pb7{
                                padding-bottom:7px;
                            }
                            .topline{
                                color:##9BA192;
                                
                            }
                            .lineb{
                               border-bottom: 2px solid black; 
                            }
                            .linet{
                                border-top: 2px solid black;  
                            }
                            .certHead{
                                font-size:16px;
                                font-family: Arial;
                                margin-bottom:5px;
                                margin-top:3px;

                            }
                            .eventTitle{
                                font-size:32px;
                            }
                            .contentFirst .contentTwo .certifyContent {
                            font-size:18px; 
                            }
                            .contentFirst2{
                                font-size:18px; 
                                font-family: "Times New Roman", Times, serif;
                            }
                            .creditContent{
                            font-size:16px; 
                            font-family: "Calibri";
                            }
                            .signature1{
                                font-size:14px; 
                                margin-bottom:2px;
                                
                            }
                            .signature2{
                                font-size:14px;
                                font-family: "Garamond",serif;
                                margin-bottom:3px;
                                margin-top:2px;
                            }
                            .attendeeDetail {
                                font-size: 20px;
                                font-family: "Calibri",serif;
                            }
                            .bottomContent{
                                font-size: 11px;
                                font-family: "Garamond",serif;
                            }
                            .reminder {
                                margin-left: auto;
                                margin-right: auto;
                                font-family: "Garamond",serif;
                            
                            }
                            .bottom-right-img {
                                position: fixed;
                                right: 0;
                                bottom: 0;
                                width: 300px; /* Adjust as needed */
                                height: auto;
                            }
                            .sign-img {
                                width: 200px; /* Adjust as needed */
                                height: auto;
                                border-bottom:1px black;
                            }
                            .topBorder{
                               border-top: 1px solid black; 
                            }
                            .bottomimage{
                                margin-bottom:0px;
                            }
                        </style>
                    </head>
                    <body>
                        <div style="height:8.15in;padding-left:10px;padding-right:10px;">
                           
                            <table  width="100%"> 
                               
                                <tr>
                                    <td>  
                                        <span class="topline b">_______________________________</span>
                                        <br/>
                                        <p class=" b headingl">Continuing Legal Education </p>	
                                        <p class="b subheadingl">Certificate of Attendance </p>		
                                    </td>
                                    <td  class="text-right">
                                        <img  class="text-right" style="width: 110px; height: auto;" src="#arguments.strCertMergeCodes.certificate.imagesurl#certificateLogo.png">
                                    </td>
                                </tr>
                            </table>
                            
                            <cfset local.creditCount = 0>
                            <cfset local.totalCredit = 0>
                            <cfset local.totalEthnicCredit = 0>
                            <cfloop query="arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate">                   
                                <cfif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
                                    <cfset local.creditCount = local.creditCount + 1 >
                                    <cfset local.totalCredit = local.totalCredit + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
                                    <cfif local.creditCount GT 1 >
                                    <cfset local.creditContent = local.creditContent & ' +  '>
                                    </cfif>
                                    <cfif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.originalCreditType EQ 'General'>
                                        <cfset local.creditContent = local.creditContent & NumberFormat(arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded, "0.0") & " credit hours" >
                                    <cfelseif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.originalCreditType EQ 'Ethics'>
                                        <cfset local.totalEthnicCredit = local.totalEthnicCredit + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
                                        <cfset local.creditContent = local.creditContent & NumberFormat(arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded, "0.0") & " ethics hours">
                                    <cfelse>
                                        <cfset local.creditContent = local.creditContent & NumberFormat(arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded, "0.0") & " " & arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditType>
                                    </cfif>
                                </cfif>
                            </cfloop>
                            <table width="80%" class="text-left"  cellpadding="0" cellspacing="1" > 
                                <tr>
                                    <td class="text-left pb7"  width="30%">
                                        <div class="contentFirst "><span class="b">Sponsor:</span> </div>
                                    </td>
                                    <td class="text-left pb7" >
                                        <div class="contentFirst "> Rhode Island Bar Association</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-left pb7" width="30%">
                                        <div class="contentFirst "><span class="b">Seminar Title:</span></div>
                                    </td>
                                    <td class="text-left pb7">
                                        <div class="contentFirst "> #arguments.strCertMergeCodes.event.qryEventMeta.eventContentTitle#</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-left pb7" width="30%">
                                        <div class="contentFirst "><span class="b">Date:</span></div>
                                    </td>
                                    <td class="text-left pb7" >
                                        <div class="contentFirst ">#DateFormat(arguments.strCertMergeCodes.event.qryEventTimes.startTime,"dddd, mmm dd, yyy ")#</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-left pb7" width="30%">
                                        <div class="contentFirst "><span class="b">Location:</span> </div>
                                    </td>
                                    <td class="text-left pb7" >
                                        <div class="contentFirst ">#local.strCustomFieldValue#</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-left pb7" width="30%" >
                                        <div class="contentFirst "><span class="b">Credits:</span></div>
                                    </td>
                                    <td class="text-left pb7" >
                                        <div class="contentFirst ">#local.creditContent#</div>
                                    </td>
                                </tr>
                            </table>

                            <br/>
                            <table width="100%" cellpadding="0" cellspacing="4" > 
                                <tr>
                                    <td>
                                        <div class="creditContent  ">Subject to Article IV, Rule 3 of the Rhode Island Supreme Court Rules, this course has been approved by the Mandatory Continuing Legal Education Commission for a maximum of <span class="b">#NumberFormat(local.totalCredit, "0.0")# </span> CLE credits, inclusive of <span class="b">#NumberFormat(local.totalEthnicCredit, "0.0")#</span> legal ethics credit.   
                                        </div>
                                    </td>
                                </tr>                   
                            </table> 
                            <br/>
                            <table width="40%" cellpadding="0" cellspacing="4"> 
                                <tr>  
                                    <td class="text-left" width="25%"  >
                                        <img   class="sign-img"  src="#arguments.strCertMergeCodes.certificate.imagesurl#certificateSignature.png">	
                                        
                                        <p class="text-left b signature2 topBorder">Madeline Benner</p>	
                                        <p class="text-left i signature2">CLE Director</p>	
                                    </td>
                                </tr>                   
                            </table> 
                            <br/>
                            <table width="100%">
                                <tr >
                                    <td class="lineb" >&nbsp;</td>
                                </tr>
                                <tr>
                                    <td> </td>
                                </tr>
                                <tr>
                                    <td class="linet">&nbsp;</td>
                                </tr>
                            </table>
                            <table width="100%" cellpadding="1" cellspacing="4" > 
                                <tr>
                                    <td>
                                        <div class="certHead b">Attorney Certification 
                                        </div>
                                    </td>
                                </tr> 
                                <tr>
                                    <td>
                                        <div class="creditContent mb4 ">I certify that I completed ______ hour(s) of CLE credit by attending this program. </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="creditContent ">I further certify that the information provided is complete and accurate and that I did in fact attend the seminar for the total number of credits listed above. </div>
                                    </td>
                                </tr>                     
                            </table> 
                            <br/><br/>
                            <table width="100%" cellpadding="0" cellspacing="4" > 
                                <tr>
                                    <td class="text-left" >
                                        <div class="attendeeDetail">
                                        <span>_______________________________</span><br/>
                                        <span>Attendee's Signature:</span>
                                        </div>
                                    </td>
                               
                                    <td class="text-left" >
                                        <div class="attendeeDetail">
                                        <span>___________________________________</span><br/>
                                        <span>BAR ID</span>
                                        </div>
                                    </td>
                                </tr>
                            </table> 
                            <table class="bottomimage" width="100%"> 
                                <tr>
                                    <td  class="text-right">
                                        <img  class="bottom-right-img"   src="#arguments.strCertMergeCodes.certificate.imagesurl#certificateFooter.png">
                                    </td>
                                </tr>
                            </table>
                            
                        </div>    
                    </body>
                </html>    
            </cfoutput>
        </cfsavecontent>
		<cfreturn local.data>
	</cffunction>

</cfcomponent>    